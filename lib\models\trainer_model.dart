class TrainerModel {
  final String id;
  final String userId;
  final List<String> specialization;
  final String? bio;
  final int experienceYears;
  final List<String> certifications;
  final List<String> languages;
  final double rating;
  final int totalReviews;
  final double pricePerSession;
  final double pricePerMonth;
  final Map<String, dynamic> availability;
  final bool isVerified;
  final bool isAvailable;

  TrainerModel({
    required this.id,
    required this.userId,
    required this.specialization,
    this.bio,
    required this.experienceYears,
    required this.certifications,
    required this.languages,
    required this.rating,
    required this.totalReviews,
    required this.pricePerSession,
    required this.pricePerMonth,
    required this.availability,
    required this.isVerified,
    required this.isAvailable,
  });

  factory TrainerModel.fromJson(Map<String, dynamic> json) {
    return TrainerModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      specialization: (json['specialization'] as List<dynamic>?)
              ?.map((e) => e.toString())
              .toList() ??
          [],
      bio: json['bio'] as String?,
      experienceYears: json['experience_years'] as int? ?? 0,
      certifications: (json['certifications'] as List<dynamic>?)
              ?.map((e) => e.toString())
              .toList() ??
          [],
      languages: (json['languages'] as List<dynamic>?)
              ?.map((e) => e.toString())
              .toList() ??
          [],
      rating: (json['rating'] is int)
          ? (json['rating'] as int).toDouble()
          : (json['rating'] as double? ?? 0.0),
      totalReviews: json['total_reviews'] as int? ?? 0,
      pricePerSession: (json['price_per_session'] is int)
          ? (json['price_per_session'] as int).toDouble()
          : (json['price_per_session'] as double? ?? 0.0),
      pricePerMonth: (json['price_per_month'] is int)
          ? (json['price_per_month'] as int).toDouble()
          : (json['price_per_month'] as double? ?? 0.0),
      availability: json['availability'] as Map<String, dynamic>? ?? {},
      isVerified: json['is_verified'] as bool? ?? false,
      isAvailable: json['is_available'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'specialization': specialization,
      'bio': bio,
      'experience_years': experienceYears,
      'certifications': certifications,
      'languages': languages,
      'rating': rating,
      'total_reviews': totalReviews,
      'price_per_session': pricePerSession,
      'price_per_month': pricePerMonth,
      'availability': availability,
      'is_verified': isVerified,
      'is_available': isAvailable,
    };
  }
}
