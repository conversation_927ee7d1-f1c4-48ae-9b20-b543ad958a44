import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../main.dart';
import '../../theme/app_theme.dart';
import '../../widgets/widgets.dart';
import '../home/<USER>';
import 'register_screen.dart';
import '../profile/trainer_profile_setup_screen.dart';
import '../../l10n/app_localizations.dart';
import '../../services/localization_service.dart';

class EnhancedLoginScreen extends StatefulWidget {
  final Function(String) onLanguageChanged;

  const EnhancedLoginScreen({super.key, required this.onLanguageChanged});

  @override
  State<EnhancedLoginScreen> createState() => _EnhancedLoginScreenState();
}

class _EnhancedLoginScreenState extends State<EnhancedLoginScreen>
    with TickerProviderStateMixin {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  bool _obscurePassword = true;

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  Future<void> _signIn() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await supabase.auth.signInWithPassword(
        email: _emailController.text.trim(),
        password: _passwordController.text,
      );

      if (response.user != null) {
        // Check if user exists in users table
        final userRow = await supabase
            .from('users')
            .select()
            .eq('id', response.user!.id)
            .maybeSingle();

        if (userRow != null) {
          // Check if trainer profile exists
          final trainerRow = await supabase
              .from('trainers')
              .select()
              .eq('user_id', response.user!.id)
              .maybeSingle();

          if (mounted) {
            if (trainerRow == null) {
              // Navigate to profile setup
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => TrainerProfileSetupScreen(
                    onLanguageChanged: widget.onLanguageChanged,
                  ),
                ),
              );
            } else {
              // Navigate to dashboard
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => DashboardScreen(
                    onLanguageChanged: widget.onLanguageChanged,
                  ),
                ),
              );
            }
          }
        } else {
          throw Exception('User not found in database');
        }
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              LocalizationService.isArabic
                  ? 'خطأ في تسجيل الدخول: ${error.toString()}'
                  : 'Login error: ${error.toString()}',
            ),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: LoadingOverlay(
        isLoading: _isLoading,
        message: LocalizationService.isArabic
            ? 'جاري تسجيل الدخول...'
            : 'Signing in...',
        child: Container(
          decoration: const BoxDecoration(
            gradient: AppTheme.darkGradient,
          ),
          child: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const SizedBox(height: 40),
                      _buildHeader(),
                      const SizedBox(height: 40),
                      _buildLoginForm(),
                      const SizedBox(height: 24),
                      _buildLoginButton(),
                      const SizedBox(height: 24),
                      _buildDivider(),
                      const SizedBox(height: 24),
                      _buildRegisterLink(),
                      const SizedBox(height: 40),
                      _buildLanguageSelector(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: AppTheme.goldGradient,
            shape: BoxShape.circle,
            boxShadow: AppTheme.goldShadow,
          ),
          child: const Icon(
            Icons.fitness_center,
            size: 60,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 24),
        Text(
          LocalizationService.isArabic
              ? 'فيت جولد للمدربين'
              : 'FitGold Trainers',
          style: AppTheme.headlineLarge.copyWith(
            color: AppTheme.primaryGold,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          LocalizationService.isArabic
              ? 'منصة احترافية لإدارة التدريب'
              : 'Professional Training Management Platform',
          style: AppTheme.bodyLarge.copyWith(
            color: AppTheme.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildLoginForm() {
    return GlassCard(
      padding: const EdgeInsets.all(24),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            CustomInputField(
              label:
                  LocalizationService.isArabic ? 'البريد الإلكتروني' : 'Email',
              hint: LocalizationService.isArabic
                  ? 'أدخل بريدك الإلكتروني'
                  : 'Enter your email',
              controller: _emailController,
              keyboardType: TextInputType.emailAddress,
              prefixIcon: const Icon(Icons.email_outlined),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return LocalizationService.isArabic
                      ? 'يرجى إدخال البريد الإلكتروني'
                      : 'Please enter email';
                }
                if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                    .hasMatch(value)) {
                  return LocalizationService.isArabic
                      ? 'يرجى إدخال بريد إلكتروني صحيح'
                      : 'Please enter a valid email';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            CustomInputField(
              label: LocalizationService.isArabic ? 'كلمة المرور' : 'Password',
              hint: LocalizationService.isArabic
                  ? 'أدخل كلمة المرور'
                  : 'Enter your password',
              controller: _passwordController,
              obscureText: _obscurePassword,
              prefixIcon: const Icon(Icons.lock_outlined),
              suffixIcon: Icon(
                _obscurePassword
                    ? Icons.visibility_outlined
                    : Icons.visibility_off_outlined,
              ),
              onSuffixIconTap: () {
                setState(() {
                  _obscurePassword = !_obscurePassword;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return LocalizationService.isArabic
                      ? 'يرجى إدخال كلمة المرور'
                      : 'Please enter password';
                }
                if (value.length < 6) {
                  return LocalizationService.isArabic
                      ? 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
                      : 'Password must be at least 6 characters';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoginButton() {
    return CustomButton(
      text: LocalizationService.isArabic ? 'تسجيل الدخول' : 'Sign In',
      onPressed: _signIn,
      isLoading: _isLoading,
      gradient: AppTheme.goldGradient,
      icon: Icon(Icons.login), // Fixed: wrap IconData in Icon widget
    );
  }

  Widget _buildDivider() {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 1,
            color: AppTheme.textSecondary.withValues(alpha: 0.3),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            LocalizationService.isArabic ? 'أو' : 'OR',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
            ),
          ),
        ),
        Expanded(
          child: Container(
            height: 1,
            color: AppTheme.textSecondary.withValues(alpha: 0.3),
          ),
        ),
      ],
    );
  }

  Widget _buildRegisterLink() {
    return CustomButton(
      text: LocalizationService.isArabic
          ? 'إنشاء حساب جديد'
          : 'Create New Account',
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => RegisterScreen(
              onLanguageChanged: widget.onLanguageChanged,
            ),
          ),
        );
      },
      isOutlined: true,
      icon: Icon(Icons.person_add), // Fixed: wrap IconData in Icon widget
    );
  }

  Widget _buildLanguageSelector() {
    return GlassCard(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildLanguageButton('العربية', 'ar'),
          const SizedBox(width: 16),
          _buildLanguageButton('English', 'en'),
        ],
      ),
    );
  }

  Widget _buildLanguageButton(String label, String languageCode) {
    final isSelected = LocalizationService.currentLanguage == languageCode;

    return GestureDetector(
      onTap: () => widget.onLanguageChanged(languageCode),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryGold : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? AppTheme.primaryGold : AppTheme.borderColor,
          ),
        ),
        child: Text(
          label,
          style: AppTheme.bodyMedium.copyWith(
            color: isSelected ? Colors.black : AppTheme.textPrimary,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
