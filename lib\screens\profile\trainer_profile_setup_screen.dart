import 'package:flutter/material.dart';
import '../../main.dart';
import '../../theme/app_theme.dart';
import '../home/<USER>';
import '../../services/localization_service.dart';
import '../../widgets/custom_app_bar.dart';

class TrainerProfileSetupScreen extends StatefulWidget {
  final Function(String) onLanguageChanged;

  const TrainerProfileSetupScreen({super.key, required this.onLanguageChanged});

  @override
  State<TrainerProfileSetupScreen> createState() =>
      _TrainerProfileSetupScreenState();
}

class _TrainerProfileSetupScreenState extends State<TrainerProfileSetupScreen> {
  final _bioController = TextEditingController();
  final _experienceController = TextEditingController();
  final _certificationsController = TextEditingController();
  final _pricePerSessionController = TextEditingController();
  final _pricePerMonthController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  bool _isLoading = false;
  List<String> _selectedSpecializations = [];
  List<String> _selectedLanguages = ['Arabic'];
  String? _avatarUrl;

  final List<String> _availableSpecializations = [
    'Weight Loss',
    'Muscle Building',
    'Cardio Training',
    'Strength Training',
    'Yoga',
    'Pilates',
    'CrossFit',
    'Nutrition Coaching',
    'Sports Training',
    'Rehabilitation',
    'Bodybuilding',
    'Powerlifting',
    'Functional Training',
    'HIIT Training',
  ];

  final List<String> _availableLanguages = [
    'Arabic',
    'English',
    'French',
    'Spanish',
    'German',
  ];

  @override
  void dispose() {
    _bioController.dispose();
    _experienceController.dispose();
    _certificationsController.dispose();
    _pricePerSessionController.dispose();
    _pricePerMonthController.dispose();
    super.dispose();
  }

  Future<void> _uploadAvatar() async {
    try {
      // رفع الصورة غير مدعوم حالياً
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocalizationService.isArabic
                ? 'ميزة رفع الصورة غير متوفرة حالياً'
                : 'Profile image upload is not available yet'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocalizationService.isArabic
                ? 'فشل في رفع الصورة'
                : 'Failed to upload image'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _completeProfile() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedSpecializations.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocalizationService.isArabic
              ? 'يرجى اختيار تخصص واحد على الأقل'
              : 'Please select at least one specialization'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final user = supabase.auth.currentUser;
      if (user == null) throw Exception('User not found');

      // Parse certifications as array
      List<String> certificationsList = _certificationsController.text
          .split('\n')
          .map((cert) => cert.trim())
          .where((cert) => cert.isNotEmpty)
          .toList();

      // تحقق هل يوجد صف مسبقاً لهذا المستخدم في trainers
      final existingTrainer = await supabase
          .from('trainers')
          .select('id')
          .eq('user_id', user.id)
          .maybeSingle();

      final trainerData = {
        'user_id': user.id,
        'specialization': _selectedSpecializations,
        'bio': _bioController.text.trim(),
        'experience_years': int.tryParse(_experienceController.text) ?? 0,
        'certifications': certificationsList,
        'languages': _selectedLanguages,
        'rating': 0.00,
        'total_reviews': 0,
        'price_per_session':
            double.tryParse(_pricePerSessionController.text) ?? 0.00,
        'price_per_month':
            double.tryParse(_pricePerMonthController.text) ?? 0.00,
        'availability': {}, // Empty JSONB object
        'is_verified': false,
        'is_available': true,
      };

      if (existingTrainer != null) {
        // إذا كان هناك صف، قم بالتحديث
        await supabase
            .from('trainers')
            .update(trainerData)
            .eq('user_id', user.id);
      } else {
        // إذا لم يوجد صف، قم بالإضافة
        await supabase.from('trainers').insert(trainerData);
      }

      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) =>
                DashboardScreen(onLanguageChanged: widget.onLanguageChanged),
          ),
        );
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                '${LocalizationService.isArabic ? 'فشل في إنشاء الملف الشخصي: ' : 'Failed to create profile: '}${error.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: LocalizationService.isArabic
            ? 'إعداد الملف الشخصي'
            : 'Profile Setup',
        leading: const SizedBox(width: 0),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.darkBackground,
              Color(0xFF1A1A1A),
            ],
          ),
        ),
        child: SafeArea(
          child: LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                padding: EdgeInsets.only(
                  left: 24,
                  right: 24,
                  top: 24,
                  bottom: MediaQuery.of(context).viewInsets.bottom + 24,
                ),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const SizedBox(height: 20),

                      // Header
                      Text(
                        LocalizationService.isArabic
                            ? 'أكمل ملفك الشخصي'
                            : 'Complete Your Profile',
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryGold,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        LocalizationService.isArabic
                            ? 'أضف معلوماتك المهنية لجذب المتدربين'
                            : 'Add your professional information to attract trainees',
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 16,
                          color: AppTheme.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 40),

                      // Avatar Upload
                      Center(
                        child: GestureDetector(
                          onTap: _uploadAvatar,
                          child: Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: _avatarUrl == null
                                  ? const LinearGradient(
                                      colors: [
                                        AppTheme.primaryGold,
                                        AppTheme.accentGold,
                                      ],
                                    )
                                  : null,
                              image: _avatarUrl != null
                                  ? DecorationImage(
                                      image: NetworkImage(_avatarUrl!),
                                      fit: BoxFit.cover,
                                    )
                                  : null,
                              border: Border.all(
                                color: AppTheme.primaryGold,
                                width: 3,
                              ),
                            ),
                            child: _avatarUrl == null
                                ? const Icon(
                                    Icons.camera_alt,
                                    size: 40,
                                    color: Colors.black,
                                  )
                                : null,
                          ),
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        LocalizationService.isArabic
                            ? 'اضغط لإضافة صورة شخصية'
                            : 'Tap to add profile picture',
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          color: AppTheme.textSecondary,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 30),

                      // Bio Field
                      TextFormField(
                        controller: _bioController,
                        maxLines: 4,
                        decoration: InputDecoration(
                          labelText: LocalizationService.isArabic
                              ? 'نبذة عنك'
                              : 'About You',
                          prefixIcon: const Icon(Icons.person_outline,
                              color: AppTheme.primaryGold),
                          hintText: LocalizationService.isArabic
                              ? 'اكتب نبذة مختصرة عن خبرتك وأسلوبك في التدريب'
                              : 'Write a brief description about your experience and training style',
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return LocalizationService.isArabic
                                ? 'يرجى كتابة نبذة عنك'
                                : 'Please write about yourself';
                          }
                          if (value.length < 50) {
                            return LocalizationService.isArabic
                                ? 'النبذة يجب أن تكون 50 حرف على الأقل'
                                : 'Bio must be at least 50 characters';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 20),

                      // Specializations (Multiple Selection)
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(
                              color: AppTheme.primaryGold.withOpacity(0.3)),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              LocalizationService.isArabic
                                  ? 'التخصصات (يمكن اختيار أكثر من واحد)'
                                  : 'Specializations (Select multiple)',
                              style: const TextStyle(
                                color: AppTheme.primaryGold,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 10),
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: _availableSpecializations.map((spec) {
                                final isSelected =
                                    _selectedSpecializations.contains(spec);
                                return FilterChip(
                                  label: Text(
                                    LocalizationService.isArabic
                                        ? _getArabicSpecialization(spec)
                                        : spec,
                                    style: TextStyle(
                                      color: isSelected
                                          ? Colors.black
                                          : AppTheme.textPrimary,
                                    ),
                                  ),
                                  selected: isSelected,
                                  onSelected: (selected) {
                                    setState(() {
                                      if (selected) {
                                        _selectedSpecializations.add(spec);
                                      } else {
                                        _selectedSpecializations.remove(spec);
                                      }
                                    });
                                  },
                                  selectedColor: AppTheme.primaryGold,
                                  backgroundColor: AppTheme.cardBackground,
                                  checkmarkColor: Colors.black,
                                );
                              }).toList(),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 20),

                      // Languages (Multiple Selection)
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(
                              color: AppTheme.primaryGold.withOpacity(0.3)),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              LocalizationService.isArabic
                                  ? 'اللغات المتحدث بها'
                                  : 'Languages Spoken',
                              style: const TextStyle(
                                color: AppTheme.primaryGold,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 10),
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: _availableLanguages.map((lang) {
                                final isSelected =
                                    _selectedLanguages.contains(lang);
                                return FilterChip(
                                  label: Text(
                                    LocalizationService.isArabic
                                        ? _getArabicLanguage(lang)
                                        : lang,
                                    style: TextStyle(
                                      color: isSelected
                                          ? Colors.black
                                          : AppTheme.textPrimary,
                                    ),
                                  ),
                                  selected: isSelected,
                                  onSelected: (selected) {
                                    setState(() {
                                      if (selected) {
                                        _selectedLanguages.add(lang);
                                      } else {
                                        _selectedLanguages.remove(lang);
                                      }
                                    });
                                  },
                                  selectedColor: AppTheme.primaryGold,
                                  backgroundColor: AppTheme.cardBackground,
                                  checkmarkColor: Colors.black,
                                );
                              }).toList(),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 20),

                      // Experience Years
                      TextFormField(
                        controller: _experienceController,
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          labelText: LocalizationService.isArabic
                              ? 'سنوات الخبرة'
                              : 'Years of Experience',
                          prefixIcon: const Icon(Icons.timeline,
                              color: AppTheme.primaryGold),
                          hintText: LocalizationService.isArabic
                              ? 'أدخل عدد سنوات الخبرة'
                              : 'Enter years of experience',
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return LocalizationService.isArabic
                                ? 'يرجى إدخال سنوات الخبرة'
                                : 'Please enter years of experience';
                          }
                          final years = int.tryParse(value);
                          if (years == null || years < 0) {
                            return LocalizationService.isArabic
                                ? 'يرجى إدخال رقم صحيح'
                                : 'Please enter a valid number';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 20),

                      // Certifications (one per line)
                      TextFormField(
                        controller: _certificationsController,
                        maxLines: 4,
                        decoration: InputDecoration(
                          labelText: LocalizationService.isArabic
                              ? 'الشهادات والمؤهلات'
                              : 'Certifications & Qualifications',
                          prefixIcon: const Icon(Icons.school,
                              color: AppTheme.primaryGold),
                          hintText: LocalizationService.isArabic
                              ? 'اكتب كل شهادة في سطر منفصل'
                              : 'Write each certification on a separate line',
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return LocalizationService.isArabic
                                ? 'يرجى إدخال الشهادات'
                                : 'Please enter your certifications';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 20),

                      // Price per Session
                      TextFormField(
                        controller: _pricePerSessionController,
                        keyboardType: const TextInputType.numberWithOptions(
                            decimal: true),
                        decoration: InputDecoration(
                          labelText: LocalizationService.isArabic
                              ? 'سعر الجلسة الواحدة (ريال)'
                              : 'Price per Session (SAR)',
                          prefixIcon: const Icon(Icons.attach_money,
                              color: AppTheme.primaryGold),
                          hintText: LocalizationService.isArabic
                              ? 'أدخل سعر الجلسة التدريبية'
                              : 'Enter your session price',
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return LocalizationService.isArabic
                                ? 'يرجى إدخال سعر الجلسة'
                                : 'Please enter session price';
                          }
                          final price = double.tryParse(value);
                          if (price == null || price <= 0) {
                            return LocalizationService.isArabic
                                ? 'يرجى إدخال سعر صحيح'
                                : 'Please enter a valid price';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 20),

                      // Price per Month
                      TextFormField(
                        controller: _pricePerMonthController,
                        keyboardType: const TextInputType.numberWithOptions(
                            decimal: true),
                        decoration: InputDecoration(
                          labelText: LocalizationService.isArabic
                              ? 'السعر الشهري (ريال)'
                              : 'Monthly Price (SAR)',
                          prefixIcon: const Icon(Icons.calendar_month,
                              color: AppTheme.primaryGold),
                          hintText: LocalizationService.isArabic
                              ? 'أدخل السعر الشهري للاشتراك'
                              : 'Enter your monthly subscription price',
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return LocalizationService.isArabic
                                ? 'يرجى إدخال السعر الشهري'
                                : 'Please enter monthly price';
                          }
                          final price = double.tryParse(value);
                          if (price == null || price <= 0) {
                            return LocalizationService.isArabic
                                ? 'يرجى إدخال سعر صحيح'
                                : 'Please enter a valid price';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 40),

                      // Complete Profile Button
                      ElevatedButton(
                        onPressed: _isLoading ? null : _completeProfile,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: _isLoading
                            ? const CircularProgressIndicator(
                                color: Colors.black)
                            : Text(
                                LocalizationService.isArabic
                                    ? 'إكمال الملف الشخصي'
                                    : 'Complete Profile',
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  String _getArabicSpecialization(String specialization) {
    switch (specialization) {
      case 'Weight Loss':
        return 'إنقاص الوزن';
      case 'Muscle Building':
        return 'بناء العضلات';
      case 'Cardio Training':
        return 'تدريب القلب';
      case 'Strength Training':
        return 'تدريب القوة';
      case 'Yoga':
        return 'اليوغا';
      case 'Pilates':
        return 'البيلاتس';
      case 'CrossFit':
        return 'كروس فت';
      case 'Nutrition Coaching':
        return 'التغذية الرياضية';
      case 'Sports Training':
        return 'التدريب الرياضي';
      case 'Rehabilitation':
        return 'العلاج الطبيعي';
      case 'Bodybuilding':
        return 'كمال الأجسام';
      case 'Powerlifting':
        return 'رفع الأثقال';
      case 'Functional Training':
        return 'التدريب الوظيفي';
      case 'HIIT Training':
        return 'تدريب عالي الكثافة';
      default:
        return specialization;
    }
  }

  String _getArabicLanguage(String language) {
    switch (language) {
      case 'Arabic':
        return 'العربية';
      case 'English':
        return 'الإنجليزية';
      case 'French':
        return 'الفرنسية';
      case 'Spanish':
        return 'الإسبانية';
      case 'German':
        return 'الألمانية';
      default:
        return language;
    }
  }
}
