class SubscriptionModel {
  final String id;
  final String traineeId;
  final String trainerId;
  final String planType;
  final double price;
  final String billingCycle;
  final DateTime startDate;
  final DateTime endDate;
  final String status;
  final bool autoRenew;
  final String? cancellationReason;
  final DateTime? cancelledAt;
  final int? trialDays;
  final bool isTrialUsed;
  final double? discountAmount;
  final String? couponCode;
  final Map<String, dynamic>? subscriptionData;
  final DateTime createdAt;
  final DateTime updatedAt;

  SubscriptionModel({
    required this.id,
    required this.traineeId,
    required this.trainerId,
    required this.planType,
    required this.price,
    required this.billingCycle,
    required this.startDate,
    required this.endDate,
    required this.status,
    this.autoRenew = true,
    this.cancellationReason,
    this.cancelledAt,
    this.trialDays,
    this.isTrialUsed = false,
    this.discountAmount,
    this.couponCode,
    this.subscriptionData,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SubscriptionModel.fromJson(Map<String, dynamic> json) {
    return SubscriptionModel(
      id: json['id'] as String,
      traineeId: json['trainee_id'] as String,
      trainerId: json['trainer_id'] as String,
      planType: json['plan_type'] as String,
      price: (json['price'] as num).toDouble(),
      billingCycle: json['billing_cycle'] as String,
      startDate: DateTime.parse(json['start_date'] as String),
      endDate: DateTime.parse(json['end_date'] as String),
      status: json['status'] as String,
      autoRenew: json['auto_renew'] as bool? ?? true,
      cancellationReason: json['cancellation_reason'] as String?,
      cancelledAt: json['cancelled_at'] != null 
          ? DateTime.parse(json['cancelled_at'] as String) 
          : null,
      trialDays: json['trial_days'] as int?,
      isTrialUsed: json['is_trial_used'] as bool? ?? false,
      discountAmount: json['discount_amount'] != null 
          ? (json['discount_amount'] as num).toDouble() 
          : null,
      couponCode: json['coupon_code'] as String?,
      subscriptionData: json['subscription_data'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trainee_id': traineeId,
      'trainer_id': trainerId,
      'plan_type': planType,
      'price': price,
      'billing_cycle': billingCycle,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'status': status,
      'auto_renew': autoRenew,
      'cancellation_reason': cancellationReason,
      'cancelled_at': cancelledAt?.toIso8601String(),
      'trial_days': trialDays,
      'is_trial_used': isTrialUsed,
      'discount_amount': discountAmount,
      'coupon_code': couponCode,
      'subscription_data': subscriptionData,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Helper methods
  bool get isActive => status == 'active';
  bool get isCancelled => status == 'cancelled';
  bool get isExpired => status == 'expired';
  bool get isPending => status == 'pending';
  bool get isTrial => trialDays != null && trialDays! > 0;
  
  bool get isExpiringSoon {
    final daysUntilExpiry = endDate.difference(DateTime.now()).inDays;
    return daysUntilExpiry <= 7 && daysUntilExpiry > 0;
  }
  
  int get daysRemaining => endDate.difference(DateTime.now()).inDays;
  
  double get finalPrice => price - (discountAmount ?? 0);
  
  String get statusDisplayName {
    switch (status) {
      case 'active':
        return 'نشط';
      case 'cancelled':
        return 'ملغي';
      case 'expired':
        return 'منتهي الصلاحية';
      case 'pending':
        return 'في الانتظار';
      case 'suspended':
        return 'معلق';
      default:
        return status;
    }
  }
  
  String get billingCycleDisplayName {
    switch (billingCycle) {
      case 'monthly':
        return 'شهري';
      case 'quarterly':
        return 'ربع سنوي';
      case 'yearly':
        return 'سنوي';
      case 'weekly':
        return 'أسبوعي';
      default:
        return billingCycle;
    }
  }
}

// Payment Model
class PaymentModel {
  final String id;
  final String subscriptionId;
  final double amount;
  final String currency;
  final String status;
  final String paymentMethod;
  final String? transactionId;
  final String? gatewayResponse;
  final DateTime? paidAt;
  final String? failureReason;
  final Map<String, dynamic>? paymentData;
  final DateTime createdAt;
  final DateTime updatedAt;

  PaymentModel({
    required this.id,
    required this.subscriptionId,
    required this.amount,
    this.currency = 'SAR',
    required this.status,
    required this.paymentMethod,
    this.transactionId,
    this.gatewayResponse,
    this.paidAt,
    this.failureReason,
    this.paymentData,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PaymentModel.fromJson(Map<String, dynamic> json) {
    return PaymentModel(
      id: json['id'] as String,
      subscriptionId: json['subscription_id'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String? ?? 'SAR',
      status: json['status'] as String,
      paymentMethod: json['payment_method'] as String,
      transactionId: json['transaction_id'] as String?,
      gatewayResponse: json['gateway_response'] as String?,
      paidAt: json['paid_at'] != null 
          ? DateTime.parse(json['paid_at'] as String) 
          : null,
      failureReason: json['failure_reason'] as String?,
      paymentData: json['payment_data'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'subscription_id': subscriptionId,
      'amount': amount,
      'currency': currency,
      'status': status,
      'payment_method': paymentMethod,
      'transaction_id': transactionId,
      'gateway_response': gatewayResponse,
      'paid_at': paidAt?.toIso8601String(),
      'failure_reason': failureReason,
      'payment_data': paymentData,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Helper methods
  bool get isCompleted => status == 'completed';
  bool get isPending => status == 'pending';
  bool get isFailed => status == 'failed';
  bool get isRefunded => status == 'refunded';
  
  String get statusDisplayName {
    switch (status) {
      case 'completed':
        return 'مكتمل';
      case 'pending':
        return 'في الانتظار';
      case 'failed':
        return 'فشل';
      case 'refunded':
        return 'مسترد';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  }
  
  String get paymentMethodDisplayName {
    switch (paymentMethod) {
      case 'credit_card':
        return 'بطاقة ائتمان';
      case 'debit_card':
        return 'بطاقة خصم';
      case 'bank_transfer':
        return 'تحويل بنكي';
      case 'wallet':
        return 'محفظة إلكترونية';
      case 'cash':
        return 'نقداً';
      default:
        return paymentMethod;
    }
  }
}
