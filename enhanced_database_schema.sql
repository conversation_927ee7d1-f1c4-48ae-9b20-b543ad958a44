-- Enhanced Professional Trainer App Database Schema
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create custom types
CREATE TYPE user_type AS ENUM ('trainee', 'trainer', 'admin', 'nutritionist', 'physiotherapist');
CREATE TYPE gender_type AS ENUM ('male', 'female');
CREATE TYPE fitness_goal AS ENUM ('lose_weight', 'gain_weight', 'maintain_weight', 'build_muscle', 'improve_endurance', 'general_fitness', 'rehabilitation', 'sports_performance');
CREATE TYPE session_status AS ENUM ('scheduled', 'completed', 'cancelled', 'reschedule_requested', 'no_show', 'in_progress');
CREATE TYPE assignment_status AS ENUM ('pending', 'active', 'completed', 'cancelled', 'paused');
CREATE TYPE subscription_status AS ENUM ('active', 'expired', 'cancelled', 'pending', 'trial', 'suspended');
CREATE TYPE payment_status AS ENUM ('pending', 'completed', 'failed', 'refunded', 'partially_refunded');
CREATE TYPE notification_type AS ENUM ('plan_assigned', 'session_reminder', 'session_booked', 'payment_due', 'general', 'achievement', 'promotion', 'system_update');
CREATE TYPE content_type AS ENUM ('article', 'video', 'course', 'exercise', 'recipe', 'tip');
CREATE TYPE difficulty_level AS ENUM ('beginner', 'intermediate', 'advanced', 'expert');
CREATE TYPE booking_type AS ENUM ('single', 'recurring', 'package');
CREATE TYPE promotion_type AS ENUM ('percentage', 'fixed_amount', 'free_trial', 'buy_one_get_one');
CREATE TYPE certificate_type AS ENUM ('completion', 'achievement', 'milestone', 'specialization');
CREATE TYPE audit_action AS ENUM ('create', 'update', 'delete', 'login', 'logout', 'payment', 'booking');

-- Enhanced Users table
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    phone TEXT,
    avatar_url TEXT,
    user_type user_type NOT NULL DEFAULT 'trainee',
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    verification_token TEXT,
    last_login TIMESTAMP WITH TIME ZONE,
    login_count INTEGER DEFAULT 0,
    timezone TEXT DEFAULT 'Asia/Riyadh',
    language TEXT DEFAULT 'ar',
    date_of_birth DATE,
    address JSONB DEFAULT '{}',
    social_links JSONB DEFAULT '{}',
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced Trainers table
CREATE TABLE public.trainers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE UNIQUE NOT NULL,
    specialization TEXT[] DEFAULT '{}',
    bio TEXT,
    experience_years INTEGER DEFAULT 0,
    certifications TEXT[] DEFAULT '{}',
    languages TEXT[] DEFAULT '{"Arabic"}',
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INTEGER DEFAULT 0,
    price_per_session DECIMAL(10,2) DEFAULT 0.00,
    price_per_month DECIMAL(10,2) DEFAULT 0.00,
    availability JSONB DEFAULT '{}',
    working_hours JSONB DEFAULT '{}',
    break_duration INTEGER DEFAULT 15, -- minutes between sessions
    max_daily_sessions INTEGER DEFAULT 8,
    is_verified BOOLEAN DEFAULT false,
    is_available BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    commission_rate DECIMAL(5,2) DEFAULT 10.00, -- platform commission percentage
    bank_details JSONB DEFAULT '{}',
    tax_info JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced Trainee profiles
CREATE TABLE public.trainees_profiles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE UNIQUE NOT NULL,
    age INTEGER NOT NULL CHECK (age >= 16 AND age <= 80),
    gender gender_type NOT NULL,
    weight DECIMAL(5,2) NOT NULL CHECK (weight > 0),
    height DECIMAL(5,2) NOT NULL CHECK (height > 0),
    fitness_goal fitness_goal NOT NULL,
    activity_level INTEGER DEFAULT 1 CHECK (activity_level >= 1 AND activity_level <= 5),
    health_conditions TEXT[] DEFAULT '{}',
    dietary_preferences TEXT[] DEFAULT '{}',
    allergies TEXT[] DEFAULT '{}',
    medications TEXT[] DEFAULT '{}',
    emergency_contact_name TEXT,
    emergency_contact_phone TEXT,
    target_weight DECIMAL(5,2),
    target_date DATE,
    fitness_level difficulty_level DEFAULT 'beginner',
    preferred_workout_time TEXT[] DEFAULT '{}',
    workout_location_preference TEXT[] DEFAULT '{}',
    medical_clearance BOOLEAN DEFAULT false,
    medical_clearance_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced Trainer assignments
CREATE TABLE public.trainer_assignments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    status assignment_status DEFAULT 'pending',
    start_date DATE DEFAULT CURRENT_DATE,
    end_date DATE,
    notes TEXT,
    goals JSONB DEFAULT '[]',
    progress_milestones JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(trainee_id, trainer_id, status)
);

-- Enhanced Subscription plans
CREATE TABLE public.subscription_plans (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL CHECK (name IN ('Basic', 'Standard', 'Premium', 'VIP', 'Custom')),
    description TEXT,
    duration_days INTEGER NOT NULL CHECK (duration_days > 0),
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    features TEXT[] DEFAULT '{}',
    session_count INTEGER DEFAULT 0, -- 0 means unlimited
    nutrition_plan_included BOOLEAN DEFAULT false,
    workout_plan_included BOOLEAN DEFAULT true,
    chat_support BOOLEAN DEFAULT true,
    video_calls BOOLEAN DEFAULT false,
    progress_tracking BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(trainer_id, name)
);

-- Educational Content Management Tables

-- Content Categories
CREATE TABLE public.content_categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    icon TEXT,
    color TEXT,
    parent_id UUID REFERENCES public.content_categories(id) ON DELETE SET NULL,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Educational Content
CREATE TABLE public.educational_content (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    content TEXT,
    content_type content_type NOT NULL,
    category_id UUID REFERENCES public.content_categories(id) ON DELETE SET NULL,
    author_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    difficulty_level difficulty_level DEFAULT 'beginner',
    duration_minutes INTEGER DEFAULT 0,
    thumbnail_url TEXT,
    media_urls TEXT[] DEFAULT '{}',
    tags TEXT[] DEFAULT '{}',
    prerequisites TEXT[] DEFAULT '{}',
    learning_objectives TEXT[] DEFAULT '{}',
    is_premium BOOLEAN DEFAULT false,
    is_published BOOLEAN DEFAULT false,
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.00,
    rating_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    published_at TIMESTAMP WITH TIME ZONE
);

-- Training Courses
CREATE TABLE public.training_courses (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    instructor_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    category_id UUID REFERENCES public.content_categories(id) ON DELETE SET NULL,
    difficulty_level difficulty_level DEFAULT 'beginner',
    duration_hours INTEGER DEFAULT 0,
    price DECIMAL(10,2) DEFAULT 0.00,
    thumbnail_url TEXT,
    trailer_url TEXT,
    syllabus JSONB DEFAULT '[]',
    requirements TEXT[] DEFAULT '{}',
    what_you_learn TEXT[] DEFAULT '{}',
    is_published BOOLEAN DEFAULT false,
    enrollment_count INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.00,
    rating_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    published_at TIMESTAMP WITH TIME ZONE
);

-- Course Lessons
CREATE TABLE public.course_lessons (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    course_id UUID REFERENCES public.training_courses(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    content TEXT,
    video_url TEXT,
    duration_minutes INTEGER DEFAULT 0,
    sort_order INTEGER DEFAULT 0,
    is_preview BOOLEAN DEFAULT false,
    resources JSONB DEFAULT '[]',
    quiz_questions JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Course Enrollments
CREATE TABLE public.course_enrollments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    course_id UUID REFERENCES public.training_courses(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    enrolled_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    last_accessed_lesson_id UUID REFERENCES public.course_lessons(id) ON DELETE SET NULL,
    certificate_issued BOOLEAN DEFAULT false,
    certificate_url TEXT,
    UNIQUE(course_id, user_id)
);

-- Lesson Progress
CREATE TABLE public.lesson_progress (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    enrollment_id UUID REFERENCES public.course_enrollments(id) ON DELETE CASCADE NOT NULL,
    lesson_id UUID REFERENCES public.course_lessons(id) ON DELETE CASCADE NOT NULL,
    completed BOOLEAN DEFAULT false,
    completion_percentage DECIMAL(5,2) DEFAULT 0.00,
    time_spent_minutes INTEGER DEFAULT 0,
    quiz_score DECIMAL(5,2),
    notes TEXT,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(enrollment_id, lesson_id)
);

-- Exercise Library
CREATE TABLE public.exercise_library (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    instructions TEXT,
    muscle_groups TEXT[] DEFAULT '{}',
    equipment_needed TEXT[] DEFAULT '{}',
    difficulty_level difficulty_level DEFAULT 'beginner',
    category TEXT,
    video_url TEXT,
    image_urls TEXT[] DEFAULT '{}',
    duration_seconds INTEGER DEFAULT 0,
    calories_per_minute DECIMAL(5,2) DEFAULT 0.00,
    safety_tips TEXT[] DEFAULT '{}',
    variations JSONB DEFAULT '[]',
    is_approved BOOLEAN DEFAULT false,
    created_by UUID REFERENCES public.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Advanced Business Management Tables

-- Financial Reports
CREATE TABLE public.financial_reports (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    report_type TEXT NOT NULL CHECK (report_type IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')),
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    total_revenue DECIMAL(12,2) DEFAULT 0.00,
    total_expenses DECIMAL(12,2) DEFAULT 0.00,
    net_profit DECIMAL(12,2) DEFAULT 0.00,
    session_count INTEGER DEFAULT 0,
    new_clients INTEGER DEFAULT 0,
    active_clients INTEGER DEFAULT 0,
    retention_rate DECIMAL(5,2) DEFAULT 0.00,
    average_session_price DECIMAL(10,2) DEFAULT 0.00,
    platform_commission DECIMAL(10,2) DEFAULT 0.00,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    report_data JSONB DEFAULT '{}',
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Performance Analytics
CREATE TABLE public.performance_analytics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    metric_name TEXT NOT NULL,
    metric_value DECIMAL(15,4) NOT NULL,
    metric_type TEXT NOT NULL CHECK (metric_type IN ('count', 'percentage', 'amount', 'rating', 'duration')),
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    comparison_period_value DECIMAL(15,4),
    trend TEXT CHECK (trend IN ('up', 'down', 'stable')),
    metadata JSONB DEFAULT '{}',
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Client Relationship Management
CREATE TABLE public.client_interactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    client_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    interaction_type TEXT NOT NULL CHECK (interaction_type IN ('call', 'email', 'message', 'meeting', 'session', 'follow_up')),
    subject TEXT,
    description TEXT,
    outcome TEXT,
    follow_up_required BOOLEAN DEFAULT false,
    follow_up_date DATE,
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    status TEXT DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'cancelled')),
    duration_minutes INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Lead Management
CREATE TABLE public.leads (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    email TEXT,
    phone TEXT,
    source TEXT, -- 'website', 'referral', 'social_media', 'advertisement'
    status TEXT DEFAULT 'new' CHECK (status IN ('new', 'contacted', 'qualified', 'proposal_sent', 'negotiation', 'closed_won', 'closed_lost')),
    fitness_goals TEXT[] DEFAULT '{}',
    budget_range TEXT,
    preferred_schedule TEXT,
    notes TEXT,
    assigned_to UUID REFERENCES public.users(id) ON DELETE SET NULL,
    last_contact_date DATE,
    next_follow_up_date DATE,
    conversion_probability INTEGER DEFAULT 0 CHECK (conversion_probability >= 0 AND conversion_probability <= 100),
    estimated_value DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Business Goals and KPIs
CREATE TABLE public.business_goals (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    goal_name TEXT NOT NULL,
    description TEXT,
    target_value DECIMAL(15,4) NOT NULL,
    current_value DECIMAL(15,4) DEFAULT 0.00,
    unit TEXT, -- 'clients', 'revenue', 'sessions', 'percentage'
    target_date DATE NOT NULL,
    category TEXT, -- 'financial', 'client_acquisition', 'retention', 'quality'
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'paused', 'cancelled')),
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    milestones JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Expense Tracking
CREATE TABLE public.expenses (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    category TEXT NOT NULL, -- 'equipment', 'marketing', 'education', 'software', 'travel', 'other'
    description TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    currency TEXT DEFAULT 'SAR',
    expense_date DATE NOT NULL,
    receipt_url TEXT,
    is_business_expense BOOLEAN DEFAULT true,
    is_tax_deductible BOOLEAN DEFAULT false,
    vendor TEXT,
    payment_method TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced Booking and Scheduling System

-- Booking Templates for Recurring Sessions
CREATE TABLE public.booking_templates (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    trainee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    session_type TEXT DEFAULT 'training',
    duration_minutes INTEGER DEFAULT 60,
    booking_type booking_type DEFAULT 'recurring',
    recurrence_pattern JSONB DEFAULT '{}', -- days of week, frequency, etc.
    start_date DATE NOT NULL,
    end_date DATE,
    preferred_times TEXT[] DEFAULT '{}',
    location TEXT,
    meeting_link TEXT,
    max_sessions INTEGER, -- for package bookings
    price_per_session DECIMAL(10,2),
    total_price DECIMAL(10,2),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Waiting Lists
CREATE TABLE public.waiting_lists (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    trainee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    preferred_date DATE,
    preferred_time_start TIME,
    preferred_time_end TIME,
    flexible_timing BOOLEAN DEFAULT false,
    session_type TEXT DEFAULT 'training',
    duration_minutes INTEGER DEFAULT 60,
    priority INTEGER DEFAULT 1, -- 1 = highest priority
    notes TEXT,
    status TEXT DEFAULT 'waiting' CHECK (status IN ('waiting', 'notified', 'booked', 'expired', 'cancelled')),
    notification_sent_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Session Packages
CREATE TABLE public.session_packages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    session_count INTEGER NOT NULL CHECK (session_count > 0),
    duration_minutes INTEGER DEFAULT 60,
    total_price DECIMAL(10,2) NOT NULL,
    price_per_session DECIMAL(10,2) NOT NULL,
    validity_days INTEGER DEFAULT 90, -- package expires after X days
    is_transferable BOOLEAN DEFAULT false,
    is_refundable BOOLEAN DEFAULT false,
    cancellation_policy TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Package Purchases
CREATE TABLE public.package_purchases (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    package_id UUID REFERENCES public.session_packages(id) ON DELETE CASCADE NOT NULL,
    trainee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    sessions_total INTEGER NOT NULL,
    sessions_used INTEGER DEFAULT 0,
    sessions_remaining INTEGER NOT NULL,
    purchase_price DECIMAL(10,2) NOT NULL,
    purchased_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Cancellation Policies
CREATE TABLE public.cancellation_policies (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    advance_notice_hours INTEGER NOT NULL, -- minimum hours before session
    refund_percentage DECIMAL(5,2) DEFAULT 0.00,
    reschedule_allowed BOOLEAN DEFAULT true,
    reschedule_limit INTEGER DEFAULT 1, -- max reschedules per session
    no_show_penalty DECIMAL(10,2) DEFAULT 0.00,
    late_cancellation_fee DECIMAL(10,2) DEFAULT 0.00,
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Session Reschedule Requests
CREATE TABLE public.reschedule_requests (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    session_id UUID REFERENCES public.sessions(id) ON DELETE CASCADE NOT NULL,
    requested_by UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    original_datetime TIMESTAMP WITH TIME ZONE NOT NULL,
    requested_datetime TIMESTAMP WITH TIME ZONE NOT NULL,
    reason TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'expired')),
    response_message TEXT,
    responded_by UUID REFERENCES public.users(id) ON DELETE SET NULL,
    responded_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Assessment and Certification System

-- Assessment Templates
CREATE TABLE public.assessment_templates (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    assessment_type TEXT CHECK (assessment_type IN ('fitness_test', 'knowledge_quiz', 'practical_exam', 'progress_evaluation')),
    category TEXT, -- 'strength', 'cardio', 'flexibility', 'nutrition', 'general'
    difficulty_level difficulty_level DEFAULT 'beginner',
    duration_minutes INTEGER DEFAULT 30,
    passing_score DECIMAL(5,2) DEFAULT 70.00,
    questions JSONB DEFAULT '[]',
    exercises JSONB DEFAULT '[]',
    scoring_criteria JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trainee Assessments
CREATE TABLE public.trainee_assessments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    template_id UUID REFERENCES public.assessment_templates(id) ON DELETE CASCADE NOT NULL,
    trainee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    scheduled_at TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    score DECIMAL(5,2),
    max_score DECIMAL(5,2),
    percentage DECIMAL(5,2),
    passed BOOLEAN DEFAULT false,
    answers JSONB DEFAULT '{}',
    results JSONB DEFAULT '{}',
    feedback TEXT,
    recommendations TEXT,
    status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'in_progress', 'completed', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Achievements and Badges
CREATE TABLE public.achievements (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    category TEXT, -- 'fitness', 'consistency', 'improvement', 'milestone'
    icon TEXT,
    badge_color TEXT,
    criteria JSONB DEFAULT '{}', -- conditions to earn this achievement
    points INTEGER DEFAULT 0,
    rarity TEXT DEFAULT 'common' CHECK (rarity IN ('common', 'rare', 'epic', 'legendary')),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Achievements
CREATE TABLE public.user_achievements (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    achievement_id UUID REFERENCES public.achievements(id) ON DELETE CASCADE NOT NULL,
    earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    progress_data JSONB DEFAULT '{}',
    is_featured BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, achievement_id)
);

-- Certificates
CREATE TABLE public.certificates (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    certificate_type certificate_type NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    course_id UUID REFERENCES public.training_courses(id) ON DELETE SET NULL,
    assessment_id UUID REFERENCES public.trainee_assessments(id) ON DELETE SET NULL,
    achievement_id UUID REFERENCES public.achievements(id) ON DELETE SET NULL,
    certificate_number TEXT UNIQUE NOT NULL,
    issue_date DATE DEFAULT CURRENT_DATE,
    expiry_date DATE,
    certificate_url TEXT,
    verification_code TEXT UNIQUE,
    is_verified BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Fitness Milestones
CREATE TABLE public.fitness_milestones (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    milestone_type TEXT NOT NULL, -- 'weight_loss', 'weight_gain', 'strength_gain', 'endurance_improvement'
    target_value DECIMAL(10,2) NOT NULL,
    current_value DECIMAL(10,2) DEFAULT 0.00,
    unit TEXT NOT NULL, -- 'kg', 'lbs', 'minutes', 'reps'
    target_date DATE,
    achieved_date DATE,
    is_achieved BOOLEAN DEFAULT false,
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    notes TEXT,
    celebration_message TEXT,
    reward_points INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Skill Assessments
CREATE TABLE public.skill_assessments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    skill_name TEXT NOT NULL,
    skill_category TEXT, -- 'strength', 'cardio', 'flexibility', 'balance', 'coordination'
    current_level INTEGER DEFAULT 1 CHECK (current_level >= 1 AND current_level <= 10),
    target_level INTEGER CHECK (target_level >= 1 AND target_level <= 10),
    assessment_date DATE DEFAULT CURRENT_DATE,
    notes TEXT,
    improvement_plan TEXT,
    next_assessment_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Marketing and Promotions System

-- Promotional Campaigns
CREATE TABLE public.promotional_campaigns (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    campaign_type TEXT CHECK (campaign_type IN ('discount', 'free_trial', 'referral', 'seasonal', 'new_client')),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    target_audience JSONB DEFAULT '{}', -- criteria for who can use this campaign
    usage_limit INTEGER, -- max number of uses
    usage_count INTEGER DEFAULT 0,
    budget DECIMAL(10,2),
    spent_amount DECIMAL(10,2) DEFAULT 0.00,
    conversion_rate DECIMAL(5,2) DEFAULT 0.00,
    roi DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Discount Coupons
CREATE TABLE public.discount_coupons (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    campaign_id UUID REFERENCES public.promotional_campaigns(id) ON DELETE SET NULL,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE,
    code TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    discount_type promotion_type NOT NULL,
    discount_value DECIMAL(10,2) NOT NULL,
    minimum_purchase DECIMAL(10,2) DEFAULT 0.00,
    maximum_discount DECIMAL(10,2),
    applicable_services TEXT[] DEFAULT '{}', -- which services this applies to
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    usage_limit INTEGER,
    usage_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    is_stackable BOOLEAN DEFAULT false, -- can be combined with other offers
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Coupon Usage
CREATE TABLE public.coupon_usage (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    coupon_id UUID REFERENCES public.discount_coupons(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    subscription_id UUID REFERENCES public.subscriptions(id) ON DELETE SET NULL,
    payment_id UUID REFERENCES public.payments(id) ON DELETE SET NULL,
    original_amount DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) NOT NULL,
    final_amount DECIMAL(10,2) NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Referral Program
CREATE TABLE public.referral_programs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    referrer_reward_type TEXT CHECK (referrer_reward_type IN ('discount', 'cash', 'free_session', 'points')),
    referrer_reward_value DECIMAL(10,2) NOT NULL,
    referee_reward_type TEXT CHECK (referee_reward_type IN ('discount', 'cash', 'free_session', 'points')),
    referee_reward_value DECIMAL(10,2) NOT NULL,
    minimum_purchase DECIMAL(10,2) DEFAULT 0.00,
    max_referrals_per_user INTEGER,
    is_active BOOLEAN DEFAULT true,
    start_date DATE NOT NULL,
    end_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Referrals
CREATE TABLE public.referrals (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    program_id UUID REFERENCES public.referral_programs(id) ON DELETE CASCADE NOT NULL,
    referrer_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    referee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    referral_code TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'rewarded', 'cancelled')),
    referee_signup_date TIMESTAMP WITH TIME ZONE,
    referee_first_purchase_date TIMESTAMP WITH TIME ZONE,
    purchase_amount DECIMAL(10,2),
    referrer_reward_given BOOLEAN DEFAULT false,
    referee_reward_given BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(referrer_id, referee_id)
);

-- Marketing Analytics
CREATE TABLE public.marketing_analytics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE,
    campaign_id UUID REFERENCES public.promotional_campaigns(id) ON DELETE CASCADE,
    metric_name TEXT NOT NULL,
    metric_value DECIMAL(15,4) NOT NULL,
    date_recorded DATE DEFAULT CURRENT_DATE,
    additional_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email Marketing
CREATE TABLE public.email_campaigns (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    subject TEXT NOT NULL,
    content TEXT NOT NULL,
    template_id TEXT,
    target_audience JSONB DEFAULT '{}',
    scheduled_at TIMESTAMP WITH TIME ZONE,
    sent_at TIMESTAMP WITH TIME ZONE,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'sending', 'sent', 'cancelled')),
    total_recipients INTEGER DEFAULT 0,
    delivered_count INTEGER DEFAULT 0,
    opened_count INTEGER DEFAULT 0,
    clicked_count INTEGER DEFAULT 0,
    unsubscribed_count INTEGER DEFAULT 0,
    bounced_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Security and Permissions System

-- Roles and Permissions
CREATE TABLE public.roles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    is_system_role BOOLEAN DEFAULT false,
    permissions JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Roles
CREATE TABLE public.user_roles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    role_id UUID REFERENCES public.roles(id) ON DELETE CASCADE NOT NULL,
    assigned_by UUID REFERENCES public.users(id) ON DELETE SET NULL,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, role_id)
);

-- Audit Log
CREATE TABLE public.audit_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    action audit_action NOT NULL,
    table_name TEXT,
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    additional_info JSONB DEFAULT '{}'
);

-- Security Settings
CREATE TABLE public.security_settings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    two_factor_enabled BOOLEAN DEFAULT false,
    two_factor_secret TEXT,
    backup_codes TEXT[] DEFAULT '{}',
    login_notifications BOOLEAN DEFAULT true,
    session_timeout_minutes INTEGER DEFAULT 480, -- 8 hours
    allowed_ip_addresses INET[] DEFAULT '{}',
    password_changed_at TIMESTAMP WITH TIME ZONE,
    failed_login_attempts INTEGER DEFAULT 0,
    account_locked_until TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- API Keys
CREATE TABLE public.api_keys (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    key_hash TEXT UNIQUE NOT NULL,
    permissions TEXT[] DEFAULT '{}',
    rate_limit INTEGER DEFAULT 1000, -- requests per hour
    is_active BOOLEAN DEFAULT true,
    last_used_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Data Privacy and Consent
CREATE TABLE public.privacy_consents (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    consent_type TEXT NOT NULL, -- 'data_processing', 'marketing', 'analytics', 'cookies'
    consent_given BOOLEAN NOT NULL,
    consent_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    consent_version TEXT,
    ip_address INET,
    user_agent TEXT,
    withdrawn_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Data Retention Policies
CREATE TABLE public.data_retention_policies (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    table_name TEXT NOT NULL,
    retention_period_days INTEGER NOT NULL,
    policy_description TEXT,
    is_active BOOLEAN DEFAULT true,
    last_cleanup_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Session Management
CREATE TABLE public.user_sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    session_token TEXT UNIQUE NOT NULL,
    device_info JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT true,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- External Integrations and APIs

-- Third-party Integrations
CREATE TABLE public.integrations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    provider TEXT NOT NULL, -- 'zoom', 'google_calendar', 'stripe', 'paypal', 'whatsapp', 'telegram'
    description TEXT,
    api_endpoint TEXT,
    auth_type TEXT CHECK (auth_type IN ('oauth', 'api_key', 'basic_auth', 'bearer_token')),
    is_active BOOLEAN DEFAULT true,
    configuration JSONB DEFAULT '{}',
    rate_limits JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Integration Settings
CREATE TABLE public.user_integrations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    integration_id UUID REFERENCES public.integrations(id) ON DELETE CASCADE NOT NULL,
    is_enabled BOOLEAN DEFAULT true,
    credentials JSONB DEFAULT '{}', -- encrypted credentials
    settings JSONB DEFAULT '{}',
    last_sync_at TIMESTAMP WITH TIME ZONE,
    sync_status TEXT DEFAULT 'active' CHECK (sync_status IN ('active', 'error', 'disabled', 'expired')),
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, integration_id)
);

-- Webhook Endpoints
CREATE TABLE public.webhook_endpoints (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    url TEXT NOT NULL,
    events TEXT[] DEFAULT '{}', -- which events to send
    secret_key TEXT,
    is_active BOOLEAN DEFAULT true,
    last_delivery_at TIMESTAMP WITH TIME ZONE,
    delivery_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Webhook Deliveries
CREATE TABLE public.webhook_deliveries (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    endpoint_id UUID REFERENCES public.webhook_endpoints(id) ON DELETE CASCADE NOT NULL,
    event_type TEXT NOT NULL,
    payload JSONB NOT NULL,
    response_status INTEGER,
    response_body TEXT,
    delivery_attempts INTEGER DEFAULT 1,
    delivered_at TIMESTAMP WITH TIME ZONE,
    next_retry_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Data Synchronization
CREATE TABLE public.sync_jobs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    integration_id UUID REFERENCES public.integrations(id) ON DELETE CASCADE NOT NULL,
    sync_type TEXT NOT NULL, -- 'full', 'incremental', 'manual'
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    records_processed INTEGER DEFAULT 0,
    records_updated INTEGER DEFAULT 0,
    records_created INTEGER DEFAULT 0,
    records_failed INTEGER DEFAULT 0,
    error_message TEXT,
    sync_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced Tables from Original Schema

-- Enhanced Subscriptions
CREATE TABLE public.subscriptions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    plan_id UUID REFERENCES public.subscription_plans(id) ON DELETE SET NULL,
    plan_type TEXT,
    price DECIMAL(10,2) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status subscription_status DEFAULT 'pending',
    auto_renew BOOLEAN DEFAULT false,
    cancellation_reason TEXT,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    trial_end_date DATE,
    discount_applied DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced Payments
CREATE TABLE public.payments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    subscription_id UUID REFERENCES public.subscriptions(id) ON DELETE CASCADE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT DEFAULT 'SAR',
    payment_method TEXT,
    payment_gateway TEXT,
    transaction_id TEXT UNIQUE,
    gateway_transaction_id TEXT,
    status payment_status DEFAULT 'pending',
    failure_reason TEXT,
    refund_amount DECIMAL(10,2) DEFAULT 0.00,
    refund_reason TEXT,
    processing_fee DECIMAL(10,2) DEFAULT 0.00,
    net_amount DECIMAL(10,2),
    paid_at TIMESTAMP WITH TIME ZONE,
    refunded_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced Sessions
CREATE TABLE public.sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    package_purchase_id UUID REFERENCES public.package_purchases(id) ON DELETE SET NULL,
    title TEXT NOT NULL,
    description TEXT,
    session_type TEXT DEFAULT 'training',
    scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
    duration_minutes INTEGER DEFAULT 60,
    location TEXT,
    meeting_link TEXT,
    meeting_id TEXT,
    meeting_password TEXT,
    status session_status DEFAULT 'scheduled',
    check_in_time TIMESTAMP WITH TIME ZONE,
    check_out_time TIMESTAMP WITH TIME ZONE,
    actual_duration_minutes INTEGER,
    notes TEXT,
    trainee_feedback TEXT,
    trainer_feedback TEXT,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    cancellation_reason TEXT,
    no_show_reason TEXT,
    rescheduled_from UUID REFERENCES public.sessions(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced Nutrition Plans
CREATE TABLE public.nutrition_plans (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    start_date DATE NOT NULL,
    end_date DATE,
    daily_calories INTEGER,
    daily_protein DECIMAL(5,2),
    daily_carbs DECIMAL(5,2),
    daily_fats DECIMAL(5,2),
    daily_fiber DECIMAL(5,2),
    daily_water_liters DECIMAL(4,2),
    meals JSONB DEFAULT '[]',
    supplements JSONB DEFAULT '[]',
    restrictions JSONB DEFAULT '[]',
    instructions TEXT,
    shopping_list JSONB DEFAULT '[]',
    is_active BOOLEAN DEFAULT true,
    completed BOOLEAN DEFAULT false,
    completed_at TIMESTAMP WITH TIME ZONE,
    adherence_score DECIMAL(5,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced Workout Plans
CREATE TABLE public.workout_plans (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    start_date DATE NOT NULL,
    end_date DATE,
    difficulty_level INTEGER DEFAULT 1 CHECK (difficulty_level >= 1 AND difficulty_level <= 5),
    workout_frequency INTEGER DEFAULT 3, -- times per week
    estimated_duration_minutes INTEGER DEFAULT 60,
    exercises JSONB DEFAULT '[]',
    rest_days TEXT[] DEFAULT '{}',
    instructions TEXT,
    equipment_needed TEXT[] DEFAULT '{}',
    warm_up JSONB DEFAULT '[]',
    cool_down JSONB DEFAULT '[]',
    is_active BOOLEAN DEFAULT true,
    completed BOOLEAN DEFAULT false,
    completed_at TIMESTAMP WITH TIME ZONE,
    adherence_score DECIMAL(5,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced Workout Media
CREATE TABLE public.workout_media (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    workout_plan_id UUID REFERENCES public.workout_plans(id) ON DELETE CASCADE NOT NULL,
    media_type TEXT CHECK (media_type IN ('image', 'video', 'audio', 'document')) NOT NULL,
    media_url TEXT NOT NULL,
    thumbnail_url TEXT,
    title TEXT,
    description TEXT,
    duration_seconds INTEGER DEFAULT 0,
    file_size_bytes BIGINT DEFAULT 0,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced Nutrition Media
CREATE TABLE public.nutrition_media (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    nutrition_plan_id UUID REFERENCES public.nutrition_plans(id) ON DELETE CASCADE NOT NULL,
    media_type TEXT CHECK (media_type IN ('image', 'video', 'audio', 'document')) NOT NULL,
    media_url TEXT NOT NULL,
    thumbnail_url TEXT,
    title TEXT,
    description TEXT,
    duration_seconds INTEGER DEFAULT 0,
    file_size_bytes BIGINT DEFAULT 0,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced Progress Tracking
CREATE TABLE public.progress_tracking (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE SET NULL,
    weight DECIMAL(5,2),
    body_fat_percentage DECIMAL(4,2),
    muscle_mass DECIMAL(5,2),
    bmi DECIMAL(4,2),
    measurements JSONB DEFAULT '{}',
    photos TEXT[] DEFAULT '{}',
    mood_score INTEGER CHECK (mood_score >= 1 AND mood_score <= 10),
    energy_level INTEGER CHECK (energy_level >= 1 AND energy_level <= 10),
    sleep_hours DECIMAL(3,1),
    water_intake_liters DECIMAL(4,2),
    notes TEXT,
    goals_progress JSONB DEFAULT '{}',
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced Reviews
CREATE TABLE public.reviews (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    session_id UUID REFERENCES public.sessions(id) ON DELETE SET NULL,
    course_id UUID REFERENCES public.training_courses(id) ON DELETE SET NULL,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title TEXT,
    comment TEXT,
    pros TEXT[] DEFAULT '{}',
    cons TEXT[] DEFAULT '{}',
    is_anonymous BOOLEAN DEFAULT false,
    is_verified BOOLEAN DEFAULT false,
    is_featured BOOLEAN DEFAULT false,
    helpful_count INTEGER DEFAULT 0,
    reported_count INTEGER DEFAULT 0,
    trainer_response TEXT,
    trainer_responded_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(trainee_id, trainer_id, session_id)
);

-- Enhanced Notifications
CREATE TABLE public.notifications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type notification_type DEFAULT 'general',
    priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    data JSONB DEFAULT '{}',
    action_url TEXT,
    action_text TEXT,
    is_read BOOLEAN DEFAULT false,
    read_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    sent_via TEXT[] DEFAULT '{"app"}', -- 'app', 'email', 'sms', 'push'
    delivery_status JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced Chat Messages
CREATE TABLE public.chat_messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    conversation_id UUID NOT NULL, -- group messages by conversation
    sender_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    receiver_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    message TEXT NOT NULL,
    message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'video', 'audio', 'file', 'location')),
    file_url TEXT,
    file_name TEXT,
    file_size_bytes BIGINT,
    thumbnail_url TEXT,
    is_read BOOLEAN DEFAULT false,
    read_at TIMESTAMP WITH TIME ZONE,
    is_edited BOOLEAN DEFAULT false,
    edited_at TIMESTAMP WITH TIME ZONE,
    reply_to_id UUID REFERENCES public.chat_messages(id) ON DELETE SET NULL,
    is_deleted BOOLEAN DEFAULT false,
    deleted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced System Settings
CREATE TABLE public.system_settings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    category TEXT NOT NULL, -- 'general', 'payment', 'notification', 'security', 'integration'
    key TEXT NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT false, -- can be accessed by frontend
    is_encrypted BOOLEAN DEFAULT false,
    validation_rules JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(category, key)
);

-- Additional Professional Features

-- Trainer Availability Slots
CREATE TABLE public.availability_slots (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6), -- 0 = Sunday
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_available BOOLEAN DEFAULT true,
    session_duration_minutes INTEGER DEFAULT 60,
    break_duration_minutes INTEGER DEFAULT 15,
    max_sessions INTEGER DEFAULT 1, -- max sessions in this slot
    price_override DECIMAL(10,2), -- override default price for this slot
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trainer Specializations
CREATE TABLE public.trainer_specializations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    icon TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trainer Specialization Mapping
CREATE TABLE public.trainer_specialization_mapping (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    specialization_id UUID REFERENCES public.trainer_specializations(id) ON DELETE CASCADE NOT NULL,
    proficiency_level INTEGER DEFAULT 1 CHECK (proficiency_level >= 1 AND proficiency_level <= 5),
    years_experience INTEGER DEFAULT 0,
    certification_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(trainer_id, specialization_id)
);

-- Create Indexes for Performance
CREATE INDEX idx_users_email ON public.users(email);
CREATE INDEX idx_users_user_type ON public.users(user_type);
CREATE INDEX idx_trainers_user_id ON public.trainers(user_id);
CREATE INDEX idx_trainers_rating ON public.trainers(rating DESC);
CREATE INDEX idx_trainers_is_available ON public.trainers(is_available);
CREATE INDEX idx_sessions_trainer_id ON public.sessions(trainer_id);
CREATE INDEX idx_sessions_trainee_id ON public.sessions(trainee_id);
CREATE INDEX idx_sessions_scheduled_at ON public.sessions(scheduled_at);
CREATE INDEX idx_sessions_status ON public.sessions(status);
CREATE INDEX idx_subscriptions_trainee_id ON public.subscriptions(trainee_id);
CREATE INDEX idx_subscriptions_trainer_id ON public.subscriptions(trainer_id);
CREATE INDEX idx_subscriptions_status ON public.subscriptions(status);
CREATE INDEX idx_payments_status ON public.payments(status);
CREATE INDEX idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX idx_notifications_is_read ON public.notifications(is_read);
CREATE INDEX idx_chat_messages_conversation_id ON public.chat_messages(conversation_id);
CREATE INDEX idx_audit_logs_user_id ON public.audit_logs(user_id);
CREATE INDEX idx_audit_logs_timestamp ON public.audit_logs(timestamp);

-- Create Functions and Triggers for Updated Timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to relevant tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_trainers_updated_at BEFORE UPDATE ON public.trainers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_trainees_profiles_updated_at BEFORE UPDATE ON public.trainees_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sessions_updated_at BEFORE UPDATE ON public.sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) Policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trainers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trainees_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Basic RLS Policies (can be customized based on requirements)
CREATE POLICY "Users can view own profile" ON public.users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON public.users FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Trainers can view own data" ON public.trainers FOR ALL USING (user_id = auth.uid());
CREATE POLICY "Trainees can view own profile" ON public.trainees_profiles FOR ALL USING (user_id = auth.uid());

-- Insert default system settings
INSERT INTO public.system_settings (category, key, value, description, is_public) VALUES
('general', 'app_name', '"FitGold Trainer"', 'Application name', true),
('general', 'app_version', '"1.0.0"', 'Application version', true),
('general', 'default_currency', '"SAR"', 'Default currency', true),
('general', 'default_timezone', '"Asia/Riyadh"', 'Default timezone', true),
('general', 'default_language', '"ar"', 'Default language', true),
('payment', 'platform_commission_rate', '10.0', 'Platform commission percentage', false),
('notification', 'email_enabled', 'true', 'Enable email notifications', false),
('notification', 'sms_enabled', 'true', 'Enable SMS notifications', false),
('security', 'session_timeout_minutes', '480', 'Session timeout in minutes', false),
('security', 'max_login_attempts', '5', 'Maximum login attempts before lockout', false);

-- Insert default roles
INSERT INTO public.roles (name, description, is_system_role, permissions) VALUES
('admin', 'System Administrator', true, '["all"]'),
('trainer', 'Fitness Trainer', true, '["manage_clients", "create_plans", "schedule_sessions", "view_analytics"]'),
('trainee', 'Trainee/Client', true, '["view_plans", "book_sessions", "track_progress", "chat_trainer"]'),
('nutritionist', 'Nutrition Specialist', true, '["create_nutrition_plans", "manage_clients", "view_analytics"]');

-- Insert default achievements
INSERT INTO public.achievements (name, description, category, icon, points, rarity) VALUES
('First Session', 'Complete your first training session', 'milestone', '🎯', 10, 'common'),
('Week Warrior', 'Complete 7 sessions in a week', 'consistency', '💪', 50, 'rare'),
('Month Master', 'Complete 30 sessions in a month', 'consistency', '🏆', 100, 'epic'),
('Weight Loss Champion', 'Lose 5kg or more', 'fitness', '⚖️', 75, 'rare'),
('Strength Builder', 'Increase strength by 25%', 'fitness', '🏋️', 75, 'rare'),
('Perfect Attendance', 'No missed sessions for 30 days', 'consistency', '📅', 100, 'epic');

-- Insert default trainer specializations
INSERT INTO public.trainer_specializations (name, description, icon) VALUES
('Weight Loss', 'Specialized in weight loss and fat burning programs', '⚖️'),
('Muscle Building', 'Expert in muscle building and strength training', '💪'),
('Cardio Training', 'Cardiovascular fitness and endurance training', '❤️'),
('Yoga & Flexibility', 'Yoga instruction and flexibility improvement', '🧘'),
('Sports Performance', 'Athletic performance enhancement', '🏃'),
('Rehabilitation', 'Injury recovery and physical therapy', '🏥'),
('Nutrition Coaching', 'Diet planning and nutrition guidance', '🥗'),
('Senior Fitness', 'Fitness programs for older adults', '👴'),
('Youth Training', 'Fitness programs for children and teenagers', '👶'),
('Functional Training', 'Functional movement and daily life fitness', '🔄');
