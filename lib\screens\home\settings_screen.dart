import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../theme/app_theme.dart';
import '../../services/localization_service.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/premium_widgets.dart';
import '../../providers/auth_provider.dart';
import '../auth/login_screen.dart';

class SettingsScreen extends StatefulWidget {
  final Function(String) onLanguageChanged;

  const SettingsScreen({super.key, required this.onLanguageChanged});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _notificationsEnabled = true;
  bool _darkModeEnabled = true;
  bool _autoBackupEnabled = true;
  String _selectedLanguage =
      LocalizationService.isArabic ? 'العربية' : 'English';

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      appBar: CustomAppBar(
        title: LocalizationService.isArabic ? 'الإعدادات' : 'Settings',
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.darkGradient,
        ),
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppTheme.spacingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSectionHeader(
                    LocalizationService.isArabic ? 'الحساب' : 'Account',
                    Icons.person_outline,
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  _buildAccountSection(),
                  const SizedBox(height: AppTheme.spacingL),
                  _buildSectionHeader(
                    LocalizationService.isArabic ? 'التطبيق' : 'Application',
                    Icons.settings_outlined,
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  _buildAppSection(),
                  const SizedBox(height: AppTheme.spacingL),
                  _buildSectionHeader(
                    LocalizationService.isArabic
                        ? 'الخصوصية والأمان'
                        : 'Privacy & Security',
                    Icons.security_outlined,
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  _buildPrivacySection(),
                  const SizedBox(height: AppTheme.spacingL),
                  _buildSectionHeader(
                    LocalizationService.isArabic ? 'الدعم' : 'Support',
                    Icons.help_outline,
                  ),
                  const SizedBox(height: AppTheme.spacingM),
                  _buildSupportSection(),
                  const SizedBox(height: AppTheme.spacingXL),
                  _buildLogoutSection(),
                  const SizedBox(height: AppTheme.spacingXL),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(AppTheme.spacingS),
          decoration: BoxDecoration(
            gradient: AppTheme.premiumGoldGradient,
            borderRadius: AppTheme.smallRadius,
          ),
          child: Icon(
            icon,
            color: AppTheme.darkBackground,
            size: 20,
          ),
        ),
        const SizedBox(width: AppTheme.spacingM),
        Text(
          title,
          style: AppTheme.headlineSmall.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildAccountSection() {
    return Column(
      children: [
        _buildSettingsTile(
          icon: Icons.edit_outlined,
          title: LocalizationService.isArabic
              ? 'تعديل الملف الشخصي'
              : 'Edit Profile',
          subtitle: LocalizationService.isArabic
              ? 'تحديث معلوماتك الشخصية'
              : 'Update your personal information',
          onTap: () {
            // Navigate to edit profile
          },
        ),
        _buildSettingsTile(
          icon: Icons.subscriptions_outlined,
          title: LocalizationService.isArabic
              ? 'إدارة الاشتراكات'
              : 'Manage Subscriptions',
          subtitle: LocalizationService.isArabic
              ? 'عرض وتعديل خطط الاشتراك'
              : 'View and modify subscription plans',
          onTap: () {
            // Navigate to subscriptions
          },
        ),
        _buildSettingsTile(
          icon: Icons.payment_outlined,
          title: LocalizationService.isArabic ? 'طرق الدفع' : 'Payment Methods',
          subtitle: LocalizationService.isArabic
              ? 'إدارة طرق الدفع المحفوظة'
              : 'Manage saved payment methods',
          onTap: () {
            // Navigate to payment methods
          },
        ),
      ],
    );
  }

  Widget _buildAppSection() {
    return Column(
      children: [
        _buildSwitchTile(
          icon: Icons.notifications_outlined,
          title: LocalizationService.isArabic ? 'الإشعارات' : 'Notifications',
          subtitle: LocalizationService.isArabic
              ? 'تلقي إشعارات التطبيق'
              : 'Receive app notifications',
          value: _notificationsEnabled,
          onChanged: (value) {
            setState(() {
              _notificationsEnabled = value;
            });
          },
        ),
        _buildSwitchTile(
          icon: Icons.dark_mode_outlined,
          title: LocalizationService.isArabic ? 'الوضع المظلم' : 'Dark Mode',
          subtitle: LocalizationService.isArabic
              ? 'استخدام المظهر المظلم'
              : 'Use dark theme',
          value: _darkModeEnabled,
          onChanged: (value) {
            setState(() {
              _darkModeEnabled = value;
            });
          },
        ),
        _buildLanguageTile(),
        _buildSwitchTile(
          icon: Icons.backup_outlined,
          title: LocalizationService.isArabic
              ? 'النسخ الاحتياطي التلقائي'
              : 'Auto Backup',
          subtitle: LocalizationService.isArabic
              ? 'نسخ احتياطي تلقائي للبيانات'
              : 'Automatic data backup',
          value: _autoBackupEnabled,
          onChanged: (value) {
            setState(() {
              _autoBackupEnabled = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildPrivacySection() {
    return Column(
      children: [
        _buildSettingsTile(
          icon: Icons.lock_outlined,
          title: LocalizationService.isArabic
              ? 'تغيير كلمة المرور'
              : 'Change Password',
          subtitle: LocalizationService.isArabic
              ? 'تحديث كلمة مرور الحساب'
              : 'Update your account password',
          onTap: () {
            _showChangePasswordDialog();
          },
        ),
        _buildSettingsTile(
          icon: Icons.privacy_tip_outlined,
          title: LocalizationService.isArabic
              ? 'سياسة الخصوصية'
              : 'Privacy Policy',
          subtitle: LocalizationService.isArabic
              ? 'اقرأ سياسة الخصوصية'
              : 'Read our privacy policy',
          onTap: () {
            // Navigate to privacy policy
          },
        ),
        _buildSettingsTile(
          icon: Icons.description_outlined,
          title: LocalizationService.isArabic
              ? 'شروط الاستخدام'
              : 'Terms of Service',
          subtitle: LocalizationService.isArabic
              ? 'اقرأ شروط الاستخدام'
              : 'Read terms of service',
          onTap: () {
            // Navigate to terms
          },
        ),
      ],
    );
  }

  Widget _buildSupportSection() {
    return Column(
      children: [
        _buildSettingsTile(
          icon: Icons.help_center_outlined,
          title: LocalizationService.isArabic ? 'مركز المساعدة' : 'Help Center',
          subtitle: LocalizationService.isArabic
              ? 'الأسئلة الشائعة والدعم'
              : 'FAQs and support',
          onTap: () {
            // Navigate to help center
          },
        ),
        _buildSettingsTile(
          icon: Icons.contact_support_outlined,
          title: LocalizationService.isArabic ? 'تواصل معنا' : 'Contact Us',
          subtitle: LocalizationService.isArabic
              ? 'تواصل مع فريق الدعم'
              : 'Contact support team',
          onTap: () {
            // Navigate to contact
          },
        ),
        _buildSettingsTile(
          icon: Icons.star_rate_outlined,
          title: LocalizationService.isArabic ? 'قيم التطبيق' : 'Rate App',
          subtitle: LocalizationService.isArabic
              ? 'قيم تجربتك مع التطبيق'
              : 'Rate your app experience',
          onTap: () {
            // Open app store rating
          },
        ),
        _buildSettingsTile(
          icon: Icons.info_outlined,
          title: LocalizationService.isArabic ? 'حول التطبيق' : 'About App',
          subtitle: LocalizationService.isArabic
              ? 'معلومات التطبيق والإصدار'
              : 'App information and version',
          onTap: () {
            _showAboutDialog();
          },
        ),
      ],
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Widget? trailing,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      child: PremiumGlassCard(
        child: ListTile(
          contentPadding: const EdgeInsets.all(AppTheme.spacingM),
          leading: Container(
            padding: const EdgeInsets.all(AppTheme.spacingS),
            decoration: BoxDecoration(
              color: AppTheme.primaryGold.withValues(alpha: 0.2),
              borderRadius: AppTheme.smallRadius,
            ),
            child: Icon(
              icon,
              color: AppTheme.primaryGold,
              size: 24,
            ),
          ),
          title: Text(
            title,
            style: AppTheme.bodyLarge.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          subtitle: Text(
            subtitle,
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.textSecondary,
            ),
          ),
          trailing: trailing ??
              Icon(
                Icons.arrow_forward_ios,
                color: AppTheme.textSecondary,
                size: 16,
              ),
          onTap: onTap,
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      child: PremiumGlassCard(
        child: ListTile(
          contentPadding: const EdgeInsets.all(AppTheme.spacingM),
          leading: Container(
            padding: const EdgeInsets.all(AppTheme.spacingS),
            decoration: BoxDecoration(
              color: AppTheme.primaryGold.withValues(alpha: 0.2),
              borderRadius: AppTheme.smallRadius,
            ),
            child: Icon(
              icon,
              color: AppTheme.primaryGold,
              size: 24,
            ),
          ),
          title: Text(
            title,
            style: AppTheme.bodyLarge.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          subtitle: Text(
            subtitle,
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.textSecondary,
            ),
          ),
          trailing: Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppTheme.primaryGold,
            activeTrackColor: AppTheme.primaryGold.withValues(alpha: 0.3),
            inactiveThumbColor: AppTheme.textSecondary,
            inactiveTrackColor: AppTheme.glassColor,
          ),
        ),
      ),
    );
  }

  Widget _buildLanguageTile() {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      child: PremiumGlassCard(
        child: ListTile(
          contentPadding: const EdgeInsets.all(AppTheme.spacingM),
          leading: Container(
            padding: const EdgeInsets.all(AppTheme.spacingS),
            decoration: BoxDecoration(
              color: AppTheme.primaryGold.withValues(alpha: 0.2),
              borderRadius: AppTheme.smallRadius,
            ),
            child: const Icon(
              Icons.language,
              color: AppTheme.primaryGold,
              size: 24,
            ),
          ),
          title: Text(
            LocalizationService.isArabic ? 'اللغة' : 'Language',
            style: AppTheme.bodyLarge.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          subtitle: Text(
            LocalizationService.isArabic
                ? 'اختر لغة التطبيق'
                : 'Choose app language',
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.textSecondary,
            ),
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                _selectedLanguage,
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.primaryGold,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: AppTheme.spacingS),
              const Icon(
                Icons.arrow_forward_ios,
                color: AppTheme.textSecondary,
                size: 16,
              ),
            ],
          ),
          onTap: _showLanguageDialog,
        ),
      ),
    );
  }

  Widget _buildLogoutSection() {
    return PremiumGlassCard(
      child: ListTile(
        contentPadding: const EdgeInsets.all(AppTheme.spacingM),
        leading: Container(
          padding: const EdgeInsets.all(AppTheme.spacingS),
          decoration: BoxDecoration(
            color: AppTheme.errorRed.withValues(alpha: 0.2),
            borderRadius: AppTheme.smallRadius,
          ),
          child: const Icon(
            Icons.logout,
            color: AppTheme.errorRed,
            size: 24,
          ),
        ),
        title: Text(
          LocalizationService.isArabic ? 'تسجيل الخروج' : 'Logout',
          style: AppTheme.bodyLarge.copyWith(
            color: AppTheme.errorRed,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          LocalizationService.isArabic
              ? 'الخروج من الحساب الحالي'
              : 'Sign out of current account',
          style: AppTheme.bodySmall.copyWith(
            color: AppTheme.textSecondary,
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          color: AppTheme.errorRed,
          size: 16,
        ),
        onTap: _showLogoutDialog,
      ),
    );
  }

  void _showLanguageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.cardBackground,
        shape: RoundedRectangleBorder(
          borderRadius: AppTheme.largeRadius,
        ),
        title: Text(
          LocalizationService.isArabic ? 'اختر اللغة' : 'Choose Language',
          style: AppTheme.headlineSmall.copyWith(
            color: AppTheme.textPrimary,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildLanguageOption('العربية', 'ar'),
            const SizedBox(height: AppTheme.spacingS),
            _buildLanguageOption('English', 'en'),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageOption(String language, String code) {
    final isSelected = (code == 'ar' && LocalizationService.isArabic) ||
        (code == 'en' && !LocalizationService.isArabic);

    return Container(
      width: double.infinity,
      child: PremiumActionButton(
        text: language,
        icon: isSelected ? Icons.check_circle : Icons.radio_button_unchecked,
        isPrimary: isSelected,
        onPressed: () {
          if (!isSelected) {
            widget.onLanguageChanged(code);
            setState(() {
              _selectedLanguage = language;
            });
          }
          Navigator.pop(context);
        },
      ),
    );
  }

  void _showChangePasswordDialog() {
    final oldPasswordController = TextEditingController();
    final newPasswordController = TextEditingController();
    final confirmPasswordController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.cardBackground,
        shape: RoundedRectangleBorder(
          borderRadius: AppTheme.largeRadius,
        ),
        title: Text(
          LocalizationService.isArabic
              ? 'تغيير كلمة المرور'
              : 'Change Password',
          style: AppTheme.headlineSmall.copyWith(
            color: AppTheme.textPrimary,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildPasswordField(
              controller: oldPasswordController,
              label: LocalizationService.isArabic
                  ? 'كلمة المرور الحالية'
                  : 'Current Password',
            ),
            const SizedBox(height: AppTheme.spacingM),
            _buildPasswordField(
              controller: newPasswordController,
              label: LocalizationService.isArabic
                  ? 'كلمة المرور الجديدة'
                  : 'New Password',
            ),
            const SizedBox(height: AppTheme.spacingM),
            _buildPasswordField(
              controller: confirmPasswordController,
              label: LocalizationService.isArabic
                  ? 'تأكيد كلمة المرور'
                  : 'Confirm Password',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              LocalizationService.isArabic ? 'إلغاء' : 'Cancel',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
          ),
          PremiumActionButton(
            text: LocalizationService.isArabic ? 'تحديث' : 'Update',
            icon: Icons.save,
            width: 100,
            height: 40,
            onPressed: () {
              // Handle password change
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPasswordField({
    required TextEditingController controller,
    required String label,
  }) {
    return TextFormField(
      controller: controller,
      obscureText: true,
      style: AppTheme.bodyMedium.copyWith(color: AppTheme.textPrimary),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
        filled: true,
        fillColor: AppTheme.glassColor,
        border: OutlineInputBorder(
          borderRadius: AppTheme.mediumRadius,
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: AppTheme.mediumRadius,
          borderSide: const BorderSide(color: AppTheme.primaryGold),
        ),
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.cardBackground,
        shape: RoundedRectangleBorder(
          borderRadius: AppTheme.largeRadius,
        ),
        title: Text(
          LocalizationService.isArabic ? 'تسجيل الخروج' : 'Logout',
          style: AppTheme.headlineSmall.copyWith(
            color: AppTheme.textPrimary,
          ),
        ),
        content: Text(
          LocalizationService.isArabic
              ? 'هل أنت متأكد من تسجيل الخروج؟'
              : 'Are you sure you want to logout?',
          style: AppTheme.bodyMedium.copyWith(
            color: AppTheme.textSecondary,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              LocalizationService.isArabic ? 'إلغاء' : 'Cancel',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
          ),
          PremiumActionButton(
            text: LocalizationService.isArabic ? 'تسجيل الخروج' : 'Logout',
            icon: Icons.logout,
            isPrimary: false,
            width: 120,
            height: 40,
            onPressed: () async {
              final authProvider =
                  Provider.of<AuthProvider>(context, listen: false);
              await authProvider.signOut();
              if (mounted) {
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(
                    builder: (context) => LoginScreen(
                      onLanguageChanged: widget.onLanguageChanged,
                    ),
                  ),
                  (route) => false,
                );
              }
            },
          ),
        ],
      ),
    );
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.cardBackground,
        shape: RoundedRectangleBorder(
          borderRadius: AppTheme.largeRadius,
        ),
        title: Text(
          LocalizationService.isArabic ? 'حول التطبيق' : 'About App',
          style: AppTheme.headlineSmall.copyWith(
            color: AppTheme.textPrimary,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingL),
              decoration: BoxDecoration(
                gradient: AppTheme.premiumGoldGradient,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.fitness_center,
                size: 48,
                color: AppTheme.darkBackground,
              ),
            ),
            const SizedBox(height: AppTheme.spacingL),
            Text(
              'FitGold Trainer',
              style: AppTheme.headlineMedium.copyWith(
                color: AppTheme.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppTheme.spacingS),
            Text(
              LocalizationService.isArabic ? 'الإصدار 1.0.0' : 'Version 1.0.0',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
            const SizedBox(height: AppTheme.spacingM),
            Text(
              LocalizationService.isArabic
                  ? 'تطبيق احترافي لإدارة التدريب الرياضي'
                  : 'Professional fitness training management app',
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          PremiumActionButton(
            text: LocalizationService.isArabic ? 'موافق' : 'OK',
            icon: Icons.check,
            width: 80,
            height: 40,
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }
}
