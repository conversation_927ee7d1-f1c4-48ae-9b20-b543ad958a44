import 'package:flutter/material.dart';
import '../../main.dart';
import '../../models/subscription_plan_model.dart';
import '../../services/localization_service.dart';
import '../../theme/app_theme.dart';
import '../../widgets/custom_app_bar.dart';

class SubscriptionPlansScreen extends StatefulWidget {
  const SubscriptionPlansScreen({super.key});

  @override
  State<SubscriptionPlansScreen> createState() =>
      _SubscriptionPlansScreenState();
}

class _SubscriptionPlansScreenState extends State<SubscriptionPlansScreen> {
  List<SubscriptionPlanModel> _plans = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPlans();
  }

  Future<void> _loadPlans() async {
    setState(() => _isLoading = true);
    try {
      final user = supabase.auth.currentUser;
      debugPrint('Current user: ${user?.id}');
      if (user == null) {
        debugPrint('No authenticated user found');
        return;
      }

      final trainer = await supabase
          .from('trainers')
          .select('id')
          .eq('user_id', user.id)
          .maybeSingle();

      debugPrint('Trainer data: $trainer');
      if (trainer == null) {
        debugPrint('No trainer found for user: ${user.id}');
        return;
      }

      debugPrint('Loading subscription plans for trainer: ${trainer['id']}');

      // First, test if the table exists by trying a simple query
      try {
        await supabase.from('subscription_plans').select('count').limit(1);
        debugPrint('subscription_plans table exists');
      } catch (tableError) {
        debugPrint('subscription_plans table might not exist: $tableError');
        throw Exception(
            'جدول خطط الاشتراك غير موجود في قاعدة البيانات. يرجى التأكد من إنشاء الجدول أولاً.');
      }

      final plansData = await supabase
          .from('subscription_plans')
          .select('*')
          .eq('trainer_id', trainer['id'])
          .order('created_at');

      debugPrint('Plans data loaded: $plansData');
      debugPrint('Plans count: ${plansData.length}');

      // Parse plans with error handling
      final List<SubscriptionPlanModel> parsedPlans = [];
      for (int i = 0; i < plansData.length; i++) {
        try {
          final plan = SubscriptionPlanModel.fromJson(plansData[i]);
          parsedPlans.add(plan);
          debugPrint('Plan $i parsed successfully: ${plan.name}');
        } catch (parseError) {
          debugPrint('Error parsing plan $i: $parseError');
          debugPrint('Plan data: ${plansData[i]}');
        }
      }

      setState(() {
        _plans = parsedPlans;
      });
      debugPrint('Plans parsed successfully: ${_plans.length} plans');
    } catch (e, stackTrace) {
      debugPrint('Error loading plans: $e');
      debugPrint('Stack trace: $stackTrace');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocalizationService.isArabic
                ? 'فشل في تحميل الخطط: $e'
                : 'Failed to load plans: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showAddPlanDialog() {
    showDialog(
      context: context,
      builder: (context) => AddSubscriptionPlanDialog(
        onPlanAdded: _loadPlans,
      ),
    );
  }

  void _showEditPlanDialog(SubscriptionPlanModel plan) {
    showDialog(
      context: context,
      builder: (context) => AddSubscriptionPlanDialog(
        plan: plan,
        onPlanAdded: _loadPlans,
      ),
    );
  }

  Future<void> _createSamplePlans() async {
    try {
      final user = supabase.auth.currentUser;
      if (user == null) return;

      final trainer = await supabase
          .from('trainers')
          .select('id')
          .eq('user_id', user.id)
          .maybeSingle();

      if (trainer == null) return;

      // Create sample plans
      final samplePlans = [
        {
          'trainer_id': trainer['id'],
          'name': 'Basic',
          'description': 'خطة أساسية للمبتدئين',
          'duration_days': 30,
          'price': 299.00,
          'features': ['خطة تمارين أساسية', 'دعم المحادثة'],
        },
        {
          'trainer_id': trainer['id'],
          'name': 'Standard',
          'description': 'خطة متوسطة مع مميزات إضافية',
          'duration_days': 30,
          'price': 499.00,
          'features': [
            'خطة تمارين متقدمة',
            'خطة تغذية',
            'دعم المحادثة',
            'متابعة أسبوعية'
          ],
        },
        {
          'trainer_id': trainer['id'],
          'name': 'Premium',
          'description': 'خطة شاملة مع جميع المميزات',
          'duration_days': 30,
          'price': 799.00,
          'features': [
            'خطة تمارين مخصصة',
            'خطة تغذية شاملة',
            'دعم المحادثة',
            'مكالمات فيديو',
            'متابعة يومية'
          ],
        },
      ];

      await supabase.from('subscription_plans').insert(samplePlans);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocalizationService.isArabic
                ? 'تم إنشاء الخطط التجريبية بنجاح'
                : 'Sample plans created successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }

      _loadPlans(); // Reload plans
    } catch (e) {
      debugPrint('Error creating sample plans: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocalizationService.isArabic
                ? 'فشل في إنشاء الخطط التجريبية: $e'
                : 'Failed to create sample plans: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deletePlan(SubscriptionPlanModel plan) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(LocalizationService.isArabic ? 'حذف الخطة' : 'Delete Plan'),
        content: Text(LocalizationService.isArabic
            ? 'هل أنت متأكد من حذف خطة ${plan.nameInArabic}؟'
            : 'Are you sure you want to delete ${plan.name} plan?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(LocalizationService.isArabic ? 'إلغاء' : 'Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(LocalizationService.isArabic ? 'حذف' : 'Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await supabase.from('subscription_plans').delete().eq('id', plan.id);
        _loadPlans();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(LocalizationService.isArabic
                  ? 'تم حذف الخطة بنجاح'
                  : 'Plan deleted successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(LocalizationService.isArabic
                  ? 'فشل في حذف الخطة'
                  : 'Failed to delete plan'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: LocalizationService.isArabic
            ? 'خطط الاشتراك'
            : 'Subscription Plans',
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: AppTheme.primaryGold))
          : _plans.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.subscriptions_outlined,
                        size: 80,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        LocalizationService.isArabic
                            ? 'لا توجد خطط اشتراك'
                            : 'No subscription plans',
                        style:
                            const TextStyle(fontSize: 20, color: Colors.grey),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        LocalizationService.isArabic
                            ? 'قم بإضافة خطط الاشتراك الخاصة بك'
                            : 'Add your subscription plans',
                        style:
                            const TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _createSamplePlans,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryGold,
                          foregroundColor: Colors.black,
                        ),
                        child: Text(LocalizationService.isArabic
                            ? 'إنشاء خطط تجريبية'
                            : 'Create Sample Plans'),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _plans.length,
                  itemBuilder: (context, index) {
                    final plan = _plans[index];
                    return _buildPlanCard(plan);
                  },
                ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddPlanDialog,
        backgroundColor: AppTheme.primaryGold,
        child: const Icon(Icons.add, color: Colors.black),
      ),
    );
  }

  Widget _buildPlanCard(SubscriptionPlanModel plan) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              _getPlanColor(plan.name),
              _getPlanColor(plan.name).withOpacity(0.8),
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          plan.nameInArabic,
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        if (plan.description != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            plan.description!,
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  PopupMenuButton<String>(
                    icon: const Icon(Icons.more_vert, color: Colors.white),
                    onSelected: (value) {
                      if (value == 'edit') {
                        _showEditPlanDialog(plan);
                      } else if (value == 'delete') {
                        _deletePlan(plan);
                      }
                    },
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            const Icon(Icons.edit, size: 20),
                            const SizedBox(width: 8),
                            Text(LocalizationService.isArabic
                                ? 'تعديل'
                                : 'Edit'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            const Icon(Icons.delete,
                                size: 20, color: Colors.red),
                            const SizedBox(width: 8),
                            Text(
                              LocalizationService.isArabic ? 'حذف' : 'Delete',
                              style: const TextStyle(color: Colors.red),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Text(
                    '${plan.price.toStringAsFixed(0)} ريال',
                    style: const TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '/ ${plan.durationDisplayName}',
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              _buildFeaturesList(plan),
              const SizedBox(height: 16),
              Row(
                children: [
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: plan.isActive
                          ? Colors.green.withOpacity(0.3)
                          : Colors.red.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      plan.isActive
                          ? (LocalizationService.isArabic ? 'نشط' : 'Active')
                          : (LocalizationService.isArabic
                              ? 'غير نشط'
                              : 'Inactive'),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeaturesList(SubscriptionPlanModel plan) {
    final features = plan.featuresInArabic;

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: features
          .map((feature) => Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  feature,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                ),
              ))
          .toList(),
    );
  }

  Color _getPlanColor(String planName) {
    switch (planName) {
      case 'Basic':
        return Colors.blue;
      case 'Standard':
        return Colors.orange;
      case 'Premium':
        return Colors.purple;
      default:
        return AppTheme.primaryGold;
    }
  }
}

// Dialog for adding/editing subscription plans
class AddSubscriptionPlanDialog extends StatefulWidget {
  final SubscriptionPlanModel? plan;
  final VoidCallback onPlanAdded;

  const AddSubscriptionPlanDialog({
    super.key,
    this.plan,
    required this.onPlanAdded,
  });

  @override
  State<AddSubscriptionPlanDialog> createState() =>
      _AddSubscriptionPlanDialogState();
}

class _AddSubscriptionPlanDialogState extends State<AddSubscriptionPlanDialog> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _sessionCountController = TextEditingController();
  final _featuresController = TextEditingController();

  String _selectedPlanType = 'Basic';
  int _selectedDuration = 30;
  bool _nutritionPlanIncluded = false;
  bool _workoutPlanIncluded = true;
  bool _chatSupport = true;
  bool _videoCalls = false;
  bool _progressTracking = true;
  bool _isActive = true;
  bool _isLoading = false;

  final List<String> _planTypes = ['Basic', 'Standard', 'Premium'];
  final List<int> _durations = [7, 30, 90, 180, 365]; // days

  @override
  void initState() {
    super.initState();
    if (widget.plan != null) {
      _initializeWithExistingPlan();
    }
  }

  void _initializeWithExistingPlan() {
    final plan = widget.plan!;
    _selectedPlanType = plan.name;
    _descriptionController.text = plan.description ?? '';
    _selectedDuration = plan.durationDays;
    _priceController.text = plan.price.toString();
    _sessionCountController.text =
        '0'; // Default value since field doesn't exist
    _featuresController.text = plan.features.join(', ');
    _isActive = plan.isActive;
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _priceController.dispose();
    _sessionCountController.dispose();
    _featuresController.dispose();
    super.dispose();
  }

  Future<void> _savePlan() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);
    try {
      final user = supabase.auth.currentUser;
      if (user == null) throw Exception('User not found');

      final trainer = await supabase
          .from('trainers')
          .select('id')
          .eq('user_id', user.id)
          .maybeSingle();

      if (trainer == null) throw Exception('Trainer not found');

      final features = _featuresController.text
          .split(',')
          .map((f) => f.trim())
          .where((f) => f.isNotEmpty)
          .toList();

      debugPrint('Preparing plan data...');
      debugPrint('Trainer ID: ${trainer['id']}');
      debugPrint('Plan Type: $_selectedPlanType');
      debugPrint('Duration: $_selectedDuration');
      debugPrint('Price: ${_priceController.text}');
      debugPrint('Session Count: ${_sessionCountController.text}');
      debugPrint('Features: $features');

      // Test table access first
      try {
        await supabase.from('subscription_plans').select('count').limit(1);
        debugPrint('subscription_plans table is accessible');
      } catch (tableError) {
        debugPrint('Table access error: $tableError');
        throw Exception(
            'جدول خطط الاشتراك غير متاح. يرجى التأكد من إنشاء الجدول في قاعدة البيانات.');
      }

      final planData = {
        'trainer_id': trainer['id'],
        'name': _selectedPlanType,
        'description': _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        'duration_days': _selectedDuration,
        'price': double.parse(_priceController.text),
        'features': features,
        'is_active': _isActive,
      };

      debugPrint('Plan data to be saved: $planData');

      if (widget.plan != null) {
        // Update existing plan
        await supabase
            .from('subscription_plans')
            .update(planData)
            .eq('id', widget.plan!.id);
      } else {
        // Create new plan
        await supabase.from('subscription_plans').insert(planData);
      }

      if (mounted) {
        widget.onPlanAdded();
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocalizationService.isArabic
                ? widget.plan != null
                    ? 'تم تحديث الخطة بنجاح'
                    : 'تم إضافة الخطة بنجاح'
                : widget.plan != null
                    ? 'Plan updated successfully'
                    : 'Plan added successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e, stackTrace) {
      debugPrint('Error saving plan: $e');
      debugPrint('Stack trace: $stackTrace');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocalizationService.isArabic
                ? 'فشل في حفظ الخطة: $e'
                : 'Failed to save plan: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _getPlanTypeInArabic(String planType) {
    switch (planType) {
      case 'Basic':
        return 'بازيك';
      case 'Standard':
        return 'ستاندرد';
      case 'Premium':
        return 'بريميوم';
      default:
        return planType;
    }
  }

  String _getDurationDisplayName(int days) {
    switch (days) {
      case 7:
        return LocalizationService.isArabic ? 'أسبوع واحد' : '1 Week';
      case 30:
        return LocalizationService.isArabic ? 'شهر واحد' : '1 Month';
      case 90:
        return LocalizationService.isArabic ? '3 أشهر' : '3 Months';
      case 180:
        return LocalizationService.isArabic ? '6 أشهر' : '6 Months';
      case 365:
        return LocalizationService.isArabic ? 'سنة واحدة' : '1 Year';
      default:
        return '$days ${LocalizationService.isArabic ? 'يوم' : 'Days'}';
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        widget.plan != null
            ? (LocalizationService.isArabic
                ? 'تعديل خطة الاشتراك'
                : 'Edit Subscription Plan')
            : (LocalizationService.isArabic
                ? 'إضافة خطة اشتراك'
                : 'Add Subscription Plan'),
      ),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Plan Type Dropdown
                DropdownButtonFormField<String>(
                  value: _selectedPlanType,
                  decoration: InputDecoration(
                    labelText: LocalizationService.isArabic
                        ? 'نوع الخطة'
                        : 'Plan Type',
                    border: const OutlineInputBorder(),
                  ),
                  items: ['Basic', 'Standard', 'Premium', 'VIP', 'Custom']
                      .map((type) => DropdownMenuItem(
                            value: type,
                            child: Text(_getPlanTypeInArabic(type)),
                          ))
                      .toList(),
                  onChanged: (value) {
                    setState(() => _selectedPlanType = value!);
                  },
                ),
                const SizedBox(height: 16),

                // Description
                TextFormField(
                  controller: _descriptionController,
                  decoration: InputDecoration(
                    labelText:
                        LocalizationService.isArabic ? 'الوصف' : 'Description',
                    border: const OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 16),

                // Duration Dropdown
                DropdownButtonFormField<int>(
                  value: _selectedDuration,
                  decoration: InputDecoration(
                    labelText:
                        LocalizationService.isArabic ? 'المدة' : 'Duration',
                    border: const OutlineInputBorder(),
                  ),
                  items: [7, 30, 90, 180, 365]
                      .map((duration) => DropdownMenuItem(
                            value: duration,
                            child: Text(_getDurationDisplayName(duration)),
                          ))
                      .toList(),
                  onChanged: (value) {
                    setState(() => _selectedDuration = value!);
                  },
                ),
                const SizedBox(height: 16),

                // Price
                TextFormField(
                  controller: _priceController,
                  decoration: InputDecoration(
                    labelText: LocalizationService.isArabic
                        ? 'السعر (ريال)'
                        : 'Price (SAR)',
                    border: const OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return LocalizationService.isArabic
                          ? 'السعر مطلوب'
                          : 'Price is required';
                    }
                    if (double.tryParse(value) == null) {
                      return LocalizationService.isArabic
                          ? 'السعر غير صحيح'
                          : 'Invalid price';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Session Count
                TextFormField(
                  controller: _sessionCountController,
                  decoration: InputDecoration(
                    labelText: LocalizationService.isArabic
                        ? 'عدد الجلسات (0 = غير محدود)'
                        : 'Session Count (0 = Unlimited)',
                    border: const OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return LocalizationService.isArabic
                          ? 'عدد الجلسات مطلوب'
                          : 'Session count is required';
                    }
                    if (int.tryParse(value) == null) {
                      return LocalizationService.isArabic
                          ? 'عدد الجلسات غير صحيح'
                          : 'Invalid session count';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Features
                TextFormField(
                  controller: _featuresController,
                  decoration: InputDecoration(
                    labelText: LocalizationService.isArabic
                        ? 'المميزات (مفصولة بفاصلة)'
                        : 'Features (comma separated)',
                    border: const OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 16),

                // Checkboxes for included services
                _buildCheckboxTile(
                  title: LocalizationService.isArabic
                      ? 'خطة تمارين'
                      : 'Workout Plan',
                  value: _workoutPlanIncluded,
                  onChanged: (value) =>
                      setState(() => _workoutPlanIncluded = value!),
                ),
                _buildCheckboxTile(
                  title: LocalizationService.isArabic
                      ? 'خطة تغذية'
                      : 'Nutrition Plan',
                  value: _nutritionPlanIncluded,
                  onChanged: (value) =>
                      setState(() => _nutritionPlanIncluded = value!),
                ),
                _buildCheckboxTile(
                  title: LocalizationService.isArabic
                      ? 'دعم المحادثة'
                      : 'Chat Support',
                  value: _chatSupport,
                  onChanged: (value) => setState(() => _chatSupport = value!),
                ),
                _buildCheckboxTile(
                  title: LocalizationService.isArabic
                      ? 'مكالمات فيديو'
                      : 'Video Calls',
                  value: _videoCalls,
                  onChanged: (value) => setState(() => _videoCalls = value!),
                ),
                _buildCheckboxTile(
                  title: LocalizationService.isArabic
                      ? 'تتبع التقدم'
                      : 'Progress Tracking',
                  value: _progressTracking,
                  onChanged: (value) =>
                      setState(() => _progressTracking = value!),
                ),
                _buildCheckboxTile(
                  title: LocalizationService.isArabic ? 'نشط' : 'Active',
                  value: _isActive,
                  onChanged: (value) => setState(() => _isActive = value!),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(LocalizationService.isArabic ? 'إلغاء' : 'Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _savePlan,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryGold,
            foregroundColor: Colors.black,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(LocalizationService.isArabic ? 'حفظ' : 'Save'),
        ),
      ],
    );
  }

  Widget _buildCheckboxTile({
    required String title,
    required bool value,
    required ValueChanged<bool?> onChanged,
  }) {
    return CheckboxListTile(
      title: Text(title),
      value: value,
      onChanged: onChanged,
      controlAffinity: ListTileControlAffinity.leading,
      contentPadding: EdgeInsets.zero,
    );
  }
}
