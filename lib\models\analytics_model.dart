class FinancialReportModel {
  final String id;
  final String trainerId;
  final String reportPeriod;
  final DateTime periodStart;
  final DateTime periodEnd;
  final double totalRevenue;
  final double totalExpenses;
  final double netIncome;
  final int totalSessions;
  final int completedSessions;
  final int cancelledSessions;
  final int newClients;
  final int activeClients;
  final double retentionRate;
  final double averageSessionPrice;
  final double platformCommission;
  final Map<String, dynamic>? reportData;
  final DateTime createdAt;
  final DateTime updatedAt;

  FinancialReportModel({
    required this.id,
    required this.trainerId,
    required this.reportPeriod,
    required this.periodStart,
    required this.periodEnd,
    this.totalRevenue = 0.0,
    this.totalExpenses = 0.0,
    this.netIncome = 0.0,
    this.totalSessions = 0,
    this.completedSessions = 0,
    this.cancelledSessions = 0,
    this.newClients = 0,
    this.activeClients = 0,
    this.retentionRate = 0.0,
    this.averageSessionPrice = 0.0,
    this.platformCommission = 0.0,
    this.reportData,
    required this.createdAt,
    required this.updatedAt,
  });

  factory FinancialReportModel.fromJson(Map<String, dynamic> json) {
    return FinancialReportModel(
      id: json['id'] as String,
      trainerId: json['trainer_id'] as String,
      reportPeriod: json['report_period'] as String,
      periodStart: DateTime.parse(json['period_start'] as String),
      periodEnd: DateTime.parse(json['period_end'] as String),
      totalRevenue: (json['total_revenue'] as num?)?.toDouble() ?? 0.0,
      totalExpenses: (json['total_expenses'] as num?)?.toDouble() ?? 0.0,
      netIncome: (json['net_income'] as num?)?.toDouble() ?? 0.0,
      totalSessions: json['total_sessions'] as int? ?? 0,
      completedSessions: json['completed_sessions'] as int? ?? 0,
      cancelledSessions: json['cancelled_sessions'] as int? ?? 0,
      newClients: json['new_clients'] as int? ?? 0,
      activeClients: json['active_clients'] as int? ?? 0,
      retentionRate: (json['retention_rate'] as num?)?.toDouble() ?? 0.0,
      averageSessionPrice: (json['average_session_price'] as num?)?.toDouble() ?? 0.0,
      platformCommission: (json['platform_commission'] as num?)?.toDouble() ?? 0.0,
      reportData: json['report_data'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trainer_id': trainerId,
      'report_period': reportPeriod,
      'period_start': periodStart.toIso8601String(),
      'period_end': periodEnd.toIso8601String(),
      'total_revenue': totalRevenue,
      'total_expenses': totalExpenses,
      'net_income': netIncome,
      'total_sessions': totalSessions,
      'completed_sessions': completedSessions,
      'cancelled_sessions': cancelledSessions,
      'new_clients': newClients,
      'active_clients': activeClients,
      'retention_rate': retentionRate,
      'average_session_price': averageSessionPrice,
      'platform_commission': platformCommission,
      'report_data': reportData,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  double get completionRate {
    if (totalSessions == 0) return 0.0;
    return (completedSessions / totalSessions) * 100;
  }

  double get cancellationRate {
    if (totalSessions == 0) return 0.0;
    return (cancelledSessions / totalSessions) * 100;
  }

  double get profitMargin {
    if (totalRevenue == 0) return 0.0;
    return (netIncome / totalRevenue) * 100;
  }

  String get reportPeriodDisplayName {
    switch (reportPeriod) {
      case 'daily':
        return 'يومي';
      case 'weekly':
        return 'أسبوعي';
      case 'monthly':
        return 'شهري';
      case 'quarterly':
        return 'ربع سنوي';
      case 'yearly':
        return 'سنوي';
      default:
        return reportPeriod;
    }
  }
}

class PerformanceAnalyticsModel {
  final String id;
  final String trainerId;
  final String metricType;
  final String period;
  final DateTime periodStart;
  final DateTime periodEnd;
  final double value;
  final double? previousValue;
  final double? changePercentage;
  final String? trend;
  final Map<String, dynamic>? analyticsData;
  final DateTime createdAt;
  final DateTime updatedAt;

  PerformanceAnalyticsModel({
    required this.id,
    required this.trainerId,
    required this.metricType,
    required this.period,
    required this.periodStart,
    required this.periodEnd,
    required this.value,
    this.previousValue,
    this.changePercentage,
    this.trend,
    this.analyticsData,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PerformanceAnalyticsModel.fromJson(Map<String, dynamic> json) {
    return PerformanceAnalyticsModel(
      id: json['id'] as String,
      trainerId: json['trainer_id'] as String,
      metricType: json['metric_type'] as String,
      period: json['period'] as String,
      periodStart: DateTime.parse(json['period_start'] as String),
      periodEnd: DateTime.parse(json['period_end'] as String),
      value: (json['value'] as num).toDouble(),
      previousValue: (json['previous_value'] as num?)?.toDouble(),
      changePercentage: (json['change_percentage'] as num?)?.toDouble(),
      trend: json['trend'] as String?,
      analyticsData: json['analytics_data'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trainer_id': trainerId,
      'metric_type': metricType,
      'period': period,
      'period_start': periodStart.toIso8601String(),
      'period_end': periodEnd.toIso8601String(),
      'value': value,
      'previous_value': previousValue,
      'change_percentage': changePercentage,
      'trend': trend,
      'analytics_data': analyticsData,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  bool get isImproving => trend == 'up';
  bool get isDeclining => trend == 'down';
  bool get isStable => trend == 'stable';

  String get metricTypeDisplayName {
    switch (metricType) {
      case 'revenue':
        return 'الإيرادات';
      case 'sessions':
        return 'الجلسات';
      case 'clients':
        return 'العملاء';
      case 'rating':
        return 'التقييم';
      case 'retention':
        return 'الاحتفاظ بالعملاء';
      case 'conversion':
        return 'معدل التحويل';
      default:
        return metricType;
    }
  }

  String get trendDisplayName {
    switch (trend) {
      case 'up':
        return 'تحسن';
      case 'down':
        return 'تراجع';
      case 'stable':
        return 'مستقر';
      default:
        return trend ?? 'غير محدد';
    }
  }
}

class BusinessGoalModel {
  final String id;
  final String trainerId;
  final String title;
  final String? description;
  final String goalType;
  final double targetValue;
  final double currentValue;
  final String unit;
  final DateTime targetDate;
  final String status;
  final double progressPercentage;
  final Map<String, dynamic>? goalData;
  final DateTime createdAt;
  final DateTime updatedAt;

  BusinessGoalModel({
    required this.id,
    required this.trainerId,
    required this.title,
    this.description,
    required this.goalType,
    required this.targetValue,
    this.currentValue = 0.0,
    this.unit = '',
    required this.targetDate,
    this.status = 'active',
    this.progressPercentage = 0.0,
    this.goalData,
    required this.createdAt,
    required this.updatedAt,
  });

  factory BusinessGoalModel.fromJson(Map<String, dynamic> json) {
    return BusinessGoalModel(
      id: json['id'] as String,
      trainerId: json['trainer_id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      goalType: json['goal_type'] as String,
      targetValue: (json['target_value'] as num).toDouble(),
      currentValue: (json['current_value'] as num?)?.toDouble() ?? 0.0,
      unit: json['unit'] as String? ?? '',
      targetDate: DateTime.parse(json['target_date'] as String),
      status: json['status'] as String? ?? 'active',
      progressPercentage: (json['progress_percentage'] as num?)?.toDouble() ?? 0.0,
      goalData: json['goal_data'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trainer_id': trainerId,
      'title': title,
      'description': description,
      'goal_type': goalType,
      'target_value': targetValue,
      'current_value': currentValue,
      'unit': unit,
      'target_date': targetDate.toIso8601String(),
      'status': status,
      'progress_percentage': progressPercentage,
      'goal_data': goalData,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  bool get isCompleted => status == 'completed';
  bool get isActive => status == 'active';
  bool get isPaused => status == 'paused';
  bool get isCancelled => status == 'cancelled';

  bool get isOverdue => targetDate.isBefore(DateTime.now()) && !isCompleted;

  int get daysRemaining => targetDate.difference(DateTime.now()).inDays;

  String get statusDisplayName {
    switch (status) {
      case 'active':
        return 'نشط';
      case 'completed':
        return 'مكتمل';
      case 'paused':
        return 'متوقف';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  }

  String get goalTypeDisplayName {
    switch (goalType) {
      case 'revenue':
        return 'الإيرادات';
      case 'clients':
        return 'العملاء';
      case 'sessions':
        return 'الجلسات';
      case 'rating':
        return 'التقييم';
      case 'courses':
        return 'الدورات';
      default:
        return goalType;
    }
  }
}
