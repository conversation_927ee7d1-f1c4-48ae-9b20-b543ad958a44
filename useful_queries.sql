-- مجموعة من الاستعلامات المفيدة لتطبيق المدرب الشخصي

-- 1. إحصائيات المدرب الشاملة
CREATE OR REPLACE VIEW trainer_dashboard_stats AS
SELECT 
    t.id as trainer_id,
    u.full_name as trainer_name,
    t.rating,
    t.total_reviews,
    COUNT(DISTINCT s.trainee_id) as total_clients,
    COUNT(DISTINCT CASE WHEN s.status = 'completed' THEN s.id END) as completed_sessions,
    COUNT(DISTINCT CASE WHEN s.scheduled_at >= CURRENT_DATE - INTERVAL '30 days' THEN s.trainee_id END) as active_clients_30d,
    COALESCE(SUM(CASE WHEN p.status = 'completed' AND p.paid_at >= CURRENT_DATE - INTERVAL '30 days' THEN p.amount END), 0) as revenue_30d,
    COALESCE(AVG(CASE WHEN s.rating IS NOT NULL THEN s.rating END), 0) as avg_session_rating
FROM trainers t
JOIN users u ON t.user_id = u.id
LEFT JOIN sessions s ON t.id = s.trainer_id
LEFT JOIN subscriptions sub ON t.id = sub.trainer_id
LEFT JOIN payments p ON sub.id = p.subscription_id
GROUP BY t.id, u.full_name, t.rating, t.total_reviews;

-- 2. أفضل المدربين حسب التقييم والأداء
SELECT 
    u.full_name,
    t.rating,
    t.total_reviews,
    COUNT(DISTINCT s.trainee_id) as total_clients,
    COUNT(CASE WHEN s.status = 'completed' THEN 1 END) as completed_sessions,
    ROUND(AVG(s.rating), 2) as avg_session_rating,
    t.price_per_session
FROM trainers t
JOIN users u ON t.user_id = u.id
LEFT JOIN sessions s ON t.id = s.trainer_id
WHERE t.is_verified = true AND t.is_available = true
GROUP BY t.id, u.full_name, t.rating, t.total_reviews, t.price_per_session
HAVING COUNT(s.id) > 5 -- المدربين الذين لديهم أكثر من 5 جلسات
ORDER BY t.rating DESC, avg_session_rating DESC, total_clients DESC;

-- 3. تقرير الإيرادات الشهرية
SELECT 
    DATE_TRUNC('month', p.paid_at) as month,
    COUNT(DISTINCT p.subscription_id) as total_subscriptions,
    SUM(p.amount) as total_revenue,
    AVG(p.amount) as avg_payment,
    COUNT(DISTINCT sub.trainee_id) as unique_clients,
    COUNT(DISTINCT sub.trainer_id) as active_trainers
FROM payments p
JOIN subscriptions sub ON p.subscription_id = sub.id
WHERE p.status = 'completed' 
    AND p.paid_at >= CURRENT_DATE - INTERVAL '12 months'
GROUP BY DATE_TRUNC('month', p.paid_at)
ORDER BY month DESC;

-- 4. المتدربين الأكثر نشاطاً
SELECT 
    u.full_name,
    u.email,
    COUNT(s.id) as total_sessions,
    COUNT(CASE WHEN s.status = 'completed' THEN 1 END) as completed_sessions,
    COUNT(CASE WHEN s.status = 'no_show' THEN 1 END) as no_shows,
    ROUND(
        COUNT(CASE WHEN s.status = 'completed' THEN 1 END) * 100.0 / 
        NULLIF(COUNT(CASE WHEN s.status IN ('completed', 'no_show') THEN 1 END), 0), 
        2
    ) as attendance_rate,
    MAX(s.scheduled_at) as last_session_date
FROM users u
JOIN sessions s ON u.id = s.trainee_id
WHERE u.user_type = 'trainee'
GROUP BY u.id, u.full_name, u.email
HAVING COUNT(s.id) > 0
ORDER BY completed_sessions DESC, attendance_rate DESC;

-- 5. تحليل استخدام الكوبونات
SELECT 
    dc.code,
    dc.name,
    dc.discount_type,
    dc.discount_value,
    dc.usage_count,
    dc.usage_limit,
    COALESCE(SUM(cu.discount_amount), 0) as total_discount_given,
    COALESCE(SUM(cu.final_amount), 0) as total_revenue_after_discount,
    ROUND(dc.usage_count * 100.0 / NULLIF(dc.usage_limit, 0), 2) as usage_percentage
FROM discount_coupons dc
LEFT JOIN coupon_usage cu ON dc.id = cu.coupon_id
WHERE dc.is_active = true
GROUP BY dc.id, dc.code, dc.name, dc.discount_type, dc.discount_value, dc.usage_count, dc.usage_limit
ORDER BY total_discount_given DESC;

-- 6. تقرير أداء الدورات التدريبية
SELECT 
    tc.title,
    u.full_name as instructor_name,
    tc.enrollment_count,
    tc.rating,
    tc.rating_count,
    COUNT(ce.id) as total_enrollments,
    COUNT(CASE WHEN ce.completed_at IS NOT NULL THEN 1 END) as completed_enrollments,
    ROUND(
        COUNT(CASE WHEN ce.completed_at IS NOT NULL THEN 1 END) * 100.0 / 
        NULLIF(COUNT(ce.id), 0), 
        2
    ) as completion_rate,
    AVG(ce.progress_percentage) as avg_progress
FROM training_courses tc
JOIN trainers t ON tc.instructor_id = t.id
JOIN users u ON t.user_id = u.id
LEFT JOIN course_enrollments ce ON tc.id = ce.course_id
WHERE tc.is_published = true
GROUP BY tc.id, tc.title, u.full_name, tc.enrollment_count, tc.rating, tc.rating_count
ORDER BY completion_rate DESC, tc.rating DESC;

-- 7. تحليل الجلسات المجدولة للأسبوع القادم
SELECT 
    DATE(s.scheduled_at) as session_date,
    COUNT(*) as total_sessions,
    COUNT(CASE WHEN s.status = 'scheduled' THEN 1 END) as scheduled_sessions,
    COUNT(CASE WHEN s.status = 'reschedule_requested' THEN 1 END) as reschedule_requests,
    COUNT(DISTINCT s.trainer_id) as active_trainers,
    COUNT(DISTINCT s.trainee_id) as active_trainees
FROM sessions s
WHERE s.scheduled_at >= CURRENT_DATE 
    AND s.scheduled_at < CURRENT_DATE + INTERVAL '7 days'
GROUP BY DATE(s.scheduled_at)
ORDER BY session_date;

-- 8. المدربين الذين يحتاجون متابعة (أداء منخفض)
SELECT 
    u.full_name,
    u.email,
    t.rating,
    COUNT(s.id) as total_sessions,
    COUNT(CASE WHEN s.status = 'no_show' THEN 1 END) as no_shows,
    COUNT(CASE WHEN s.status = 'cancelled' THEN 1 END) as cancellations,
    ROUND(AVG(s.rating), 2) as avg_session_rating,
    COUNT(CASE WHEN s.scheduled_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as sessions_last_30d
FROM trainers t
JOIN users u ON t.user_id = u.id
LEFT JOIN sessions s ON t.id = s.trainer_id
WHERE t.is_verified = true
GROUP BY t.id, u.full_name, u.email, t.rating
HAVING 
    (COUNT(s.id) > 10 AND COALESCE(AVG(s.rating), 0) < 3.5) OR
    (COUNT(CASE WHEN s.status = 'no_show' THEN 1 END) > 3) OR
    (COUNT(CASE WHEN s.scheduled_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) = 0 AND COUNT(s.id) > 0)
ORDER BY avg_session_rating ASC, no_shows DESC;

-- 9. تحليل معدل الاحتفاظ بالعملاء
WITH client_retention AS (
    SELECT 
        s.trainee_id,
        s.trainer_id,
        MIN(s.scheduled_at) as first_session,
        MAX(s.scheduled_at) as last_session,
        COUNT(*) as total_sessions,
        EXTRACT(DAYS FROM MAX(s.scheduled_at) - MIN(s.scheduled_at)) as days_active
    FROM sessions s
    WHERE s.status = 'completed'
    GROUP BY s.trainee_id, s.trainer_id
)
SELECT 
    t.id as trainer_id,
    u.full_name as trainer_name,
    COUNT(cr.trainee_id) as total_clients,
    COUNT(CASE WHEN cr.days_active >= 30 THEN 1 END) as clients_30d_plus,
    COUNT(CASE WHEN cr.days_active >= 90 THEN 1 END) as clients_90d_plus,
    ROUND(
        COUNT(CASE WHEN cr.days_active >= 30 THEN 1 END) * 100.0 / 
        NULLIF(COUNT(cr.trainee_id), 0), 
        2
    ) as retention_rate_30d,
    ROUND(AVG(cr.total_sessions), 2) as avg_sessions_per_client
FROM trainers t
JOIN users u ON t.user_id = u.id
LEFT JOIN client_retention cr ON t.id = cr.trainer_id
GROUP BY t.id, u.full_name
HAVING COUNT(cr.trainee_id) > 0
ORDER BY retention_rate_30d DESC;

-- 10. تقرير الإنجازات والشارات
SELECT 
    a.name as achievement_name,
    a.category,
    a.rarity,
    COUNT(ua.user_id) as users_earned,
    ROUND(
        COUNT(ua.user_id) * 100.0 / 
        (SELECT COUNT(*) FROM users WHERE user_type = 'trainee'), 
        2
    ) as earning_percentage
FROM achievements a
LEFT JOIN user_achievements ua ON a.id = ua.achievement_id
WHERE a.is_active = true
GROUP BY a.id, a.name, a.category, a.rarity
ORDER BY earning_percentage DESC;

-- 11. استعلام للحصول على التوفر الحالي للمدربين
SELECT 
    u.full_name,
    t.id as trainer_id,
    t.is_available,
    t.price_per_session,
    t.rating,
    COUNT(CASE WHEN s.scheduled_at >= NOW() AND s.scheduled_at <= NOW() + INTERVAL '7 days' THEN 1 END) as upcoming_sessions,
    t.max_daily_sessions,
    CASE 
        WHEN COUNT(CASE WHEN DATE(s.scheduled_at) = CURRENT_DATE THEN 1 END) >= t.max_daily_sessions 
        THEN false 
        ELSE true 
    END as available_today
FROM trainers t
JOIN users u ON t.user_id = u.id
LEFT JOIN sessions s ON t.id = s.trainer_id AND s.status IN ('scheduled', 'in_progress')
WHERE t.is_verified = true AND t.is_available = true
GROUP BY t.id, u.full_name, t.is_available, t.price_per_session, t.rating, t.max_daily_sessions
ORDER BY t.rating DESC, available_today DESC;

-- 12. تحليل استخدام قوائم الانتظار
SELECT 
    t.id as trainer_id,
    u.full_name as trainer_name,
    COUNT(wl.id) as total_waiting_requests,
    COUNT(CASE WHEN wl.status = 'waiting' THEN 1 END) as active_waiting,
    COUNT(CASE WHEN wl.status = 'booked' THEN 1 END) as converted_to_booking,
    ROUND(
        COUNT(CASE WHEN wl.status = 'booked' THEN 1 END) * 100.0 / 
        NULLIF(COUNT(wl.id), 0), 
        2
    ) as conversion_rate
FROM trainers t
JOIN users u ON t.user_id = u.id
LEFT JOIN waiting_lists wl ON t.id = wl.trainer_id
GROUP BY t.id, u.full_name
HAVING COUNT(wl.id) > 0
ORDER BY conversion_rate DESC, total_waiting_requests DESC;
