import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

class CustomInputField extends StatefulWidget {
  final String? label;
  final String? hint;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final bool obscureText;
  final TextInputType keyboardType;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final VoidCallback? onSuffixIconTap;
  final int? maxLines;
  final bool enabled;
  final EdgeInsetsGeometry? contentPadding;
  final BorderRadius? borderRadius;
  final Color? fillColor;
  final TextStyle? labelStyle;
  final TextStyle? hintStyle;
  final TextStyle? textStyle;
  final bool showBorder;
  final Color? borderColor;
  final Color? focusedBorderColor;
  final double borderWidth;
  final bool filled;

  const CustomInputField({
    super.key,
    this.label,
    this.hint,
    this.controller,
    this.validator,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconTap,
    this.maxLines = 1,
    this.enabled = true,
    this.contentPadding,
    this.borderRadius,
    this.fillColor,
    this.labelStyle,
    this.hintStyle,
    this.textStyle,
    this.showBorder = true,
    this.borderColor,
    this.focusedBorderColor,
    this.borderWidth = 1.0,
    this.filled = true,
  });

  @override
  State<CustomInputField> createState() => _CustomInputFieldState();
}

class _CustomInputFieldState extends State<CustomInputField>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.scale(
          scale: _animation.value,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: widget.borderRadius ?? BorderRadius.circular(16),
              boxShadow: _isFocused
                  ? [
                      BoxShadow(
                        color: AppTheme.primaryGold.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
            child: TextFormField(
              controller: widget.controller,
              validator: widget.validator,
              obscureText: widget.obscureText,
              keyboardType: widget.keyboardType,
              maxLines: widget.maxLines,
              enabled: widget.enabled,
              style: widget.textStyle ?? AppTheme.bodyLarge,
              onTap: () {
                setState(() {
                  _isFocused = true;
                });
                _animationController.forward();
              },
              onTapOutside: (event) {
                setState(() {
                  _isFocused = false;
                });
                _animationController.reverse();
              },
              decoration: InputDecoration(
                labelText: widget.label,
                hintText: widget.hint,
                prefixIcon: widget.prefixIcon,
                suffixIcon: widget.suffixIcon != null
                    ? GestureDetector(
                        onTap: widget.onSuffixIconTap,
                        child: widget.suffixIcon,
                      )
                    : null,
                filled: widget.filled,
                fillColor: widget.fillColor ?? AppTheme.surfaceColor,
                contentPadding: widget.contentPadding ??
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                border: widget.showBorder
                    ? OutlineInputBorder(
                        borderRadius:
                            widget.borderRadius ?? BorderRadius.circular(16),
                        borderSide: BorderSide(
                          color: widget.borderColor ?? AppTheme.borderColor,
                          width: widget.borderWidth,
                        ),
                      )
                    : InputBorder.none,
                enabledBorder: widget.showBorder
                    ? OutlineInputBorder(
                        borderRadius:
                            widget.borderRadius ?? BorderRadius.circular(16),
                        borderSide: BorderSide(
                          color: widget.borderColor ?? AppTheme.borderColor,
                          width: widget.borderWidth,
                        ),
                      )
                    : InputBorder.none,
                focusedBorder: widget.showBorder
                    ? OutlineInputBorder(
                        borderRadius:
                            widget.borderRadius ?? BorderRadius.circular(16),
                        borderSide: BorderSide(
                          color: widget.focusedBorderColor ?? AppTheme.primaryGold,
                          width: widget.borderWidth + 1,
                        ),
                      )
                    : InputBorder.none,
                errorBorder: widget.showBorder
                    ? OutlineInputBorder(
                        borderRadius:
                            widget.borderRadius ?? BorderRadius.circular(16),
                        borderSide: BorderSide(
                          color: AppTheme.errorRed,
                          width: widget.borderWidth,
                        ),
                      )
                    : InputBorder.none,
                focusedErrorBorder: widget.showBorder
                    ? OutlineInputBorder(
                        borderRadius:
                            widget.borderRadius ?? BorderRadius.circular(16),
                        borderSide: BorderSide(
                          color: AppTheme.errorRed,
                          width: widget.borderWidth + 1,
                        ),
                      )
                    : InputBorder.none,
                labelStyle: widget.labelStyle ??
                    AppTheme.bodyMedium.copyWith(
                      color: _isFocused ? AppTheme.primaryGold : AppTheme.textSecondary,
                    ),
                hintStyle: widget.hintStyle ?? AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textTertiary,
                ),
                errorStyle: AppTheme.bodySmall.copyWith(
                  color: AppTheme.errorRed,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class SearchInputField extends StatelessWidget {
  final String? hint;
  final TextEditingController? controller;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onClear;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;

  const SearchInputField({
    super.key,
    this.hint,
    this.controller,
    this.onChanged,
    this.onClear,
    this.margin,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.all(16),
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(25),
        border: Border.all(color: AppTheme.borderColor),
        boxShadow: AppTheme.cardShadow,
      ),
      child: Row(
        children: [
          const Icon(
            Icons.search,
            color: AppTheme.textSecondary,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: TextField(
              controller: controller,
              onChanged: onChanged,
              style: AppTheme.bodyLarge,
              decoration: InputDecoration(
                hintText: hint ?? 'البحث...',
                hintStyle: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textTertiary,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          if (controller?.text.isNotEmpty == true)
            GestureDetector(
              onTap: onClear,
              child: const Icon(
                Icons.clear,
                color: AppTheme.textSecondary,
                size: 20,
              ),
            ),
        ],
      ),
    );
  }
}
