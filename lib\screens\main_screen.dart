import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_theme.dart';
import '../widgets/modern_navigation_bar.dart';
import '../services/localization_service.dart';
import 'trainer_dashboard.dart';
import 'clients_management_screen.dart';
import 'sessions_management_screen.dart';
import 'subscriptions/subscription_plans_screen.dart';
import 'home/profile_screen.dart';
import 'home/plans_screen.dart' as plans;
import 'home/settings_screen.dart';

class MainScreen extends StatefulWidget {
  final Function(String)? onLanguageChanged;

  const MainScreen({super.key, this.onLanguageChanged});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _selectedIndex = 0;
  late PageController _pageController;

  late final List<Widget> _screens;

  final List<NavigationItem> _navigationItems = [
    NavigationItem(
      icon: Icons.dashboard_outlined,
      selectedIcon: Icons.dashboard_rounded,
      label: LocalizationService.isArabic ? 'الرئيسية' : 'Dashboard',
    ),
    NavigationItem(
      icon: Icons.people_outline_rounded,
      selectedIcon: Icons.people_rounded,
      label: LocalizationService.isArabic ? 'المتدربين' : 'Clients',
    ),
    NavigationItem(
      icon: Icons.fitness_center_outlined,
      selectedIcon: Icons.fitness_center_rounded,
      label: LocalizationService.isArabic ? 'الجلسات' : 'Sessions',
    ),
    NavigationItem(
      icon: Icons.assignment_outlined,
      selectedIcon: Icons.assignment_rounded,
      label: LocalizationService.isArabic ? 'الخطط' : 'Plans',
    ),
    NavigationItem(
      icon: Icons.subscriptions_outlined,
      selectedIcon: Icons.subscriptions_rounded,
      label: LocalizationService.isArabic ? 'الاشتراكات' : 'Subscriptions',
    ),
    NavigationItem(
      icon: Icons.person_outline_rounded,
      selectedIcon: Icons.person_rounded,
      label: LocalizationService.isArabic ? 'الملف الشخصي' : 'Profile',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: _selectedIndex);

    _screens = [
      const TrainerDashboard(),
      const ClientsManagementScreen(),
      const SessionsManagementScreen(),
      const plans.PlansScreen(),
      const SubscriptionPlansScreen(),
      const ProfileScreen(),
    ];
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onItemTapped(int index) {
    if (_selectedIndex != index) {
      setState(() {
        _selectedIndex = index;
      });

      // Add haptic feedback
      HapticFeedback.lightImpact();

      // Animate to the selected page
      _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      body: PageView(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        children: _screens,
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: AppTheme.primaryGold.withValues(alpha: 0.2),
              blurRadius: 20,
              offset: const Offset(0, -8),
              spreadRadius: 2,
            ),
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 15,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: ModernNavigationBar(
          selectedIndex: _selectedIndex,
          onTap: _onItemTapped,
          items: _navigationItems,
          height: 85,
          iconSize: 24,
          selectedIconSize: 28,
          labelFontSize: 11,
          selectedLabelFontSize: 12,
          backgroundType: NavBarBackgroundType.premium,
          showLabels: true,
          showSelectedLabels: true,
          animationDuration: const Duration(milliseconds: 300),
          animationCurve: Curves.easeInOut,
        ),
      ),
    );
  }
}
