import 'package:flutter/material.dart';

abstract class AppLocalizations {
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  // App Info
  String get appName;
  String get appSlogan;
  String get trainerApp;

  // Authentication
  String get welcome;
  String get login;
  String get register;
  String get email;
  String get password;
  String get confirmPassword;
  String get fullName;
  String get forgotPassword;
  String get dontHaveAccount;
  String get alreadyHaveAccount;
  String get createAccount;
  String get signInWithGoogle;
  String get or;
  String get signOut;

  // Navigation
  String get dashboard;
  String get trainees;
  String get sessions;
  String get plans;
  String get profile;
  String get notifications;
  String get chat;
  String get settings;
  String get analytics;
  String get schedule;

  // Trainer Profile Setup
  String get trainerSetup;
  String get professionalInfo;
  String get specialization;
  String get experience;
  String get certifications;
  String get bio;
  String get pricing;
  String get pricePerSession;
  String get pricePerMonth;
  String get availability;
  String get languages;
  String get saveProfile;

  // Dashboard
  String get welcomeTrainer;
  String get todayOverview;
  String get totalTrainees;
  String get activeTrainees;
  String get todaySessions;
  String get monthlyRevenue;
  String get averageRating;
  String get totalReviews;
  String get quickActions;
  String get addPlan;
  String get scheduleSession;
  String get viewAnalytics;
  String get manageTrainees;

  // Trainees Management
  String get myTrainees;
  String get pendingRequests;
  String get allTrainees;
  String get traineeProfile;
  String get assignPlan;
  String get viewProgress;
  String get sendMessage;
  String get approve;
  String get reject;
  String get noTrainees;
  String get noRequests;

  // Sessions Management
  String get mySessions;
  String get upcoming;
  String get completed;
  String get cancelled;
  String get rescheduleRequested;
  String get sessionDetails;
  String get markCompleted;
  String get addNotes;
  String get reschedule;
  String get cancelSession;
  String get sessionNotes;
  String get traineeAttended;
  String get sessionRating;

  // Plans Management
  String get nutritionPlans;
  String get workoutPlans;
  String get createPlan;
  String get editPlan;
  String get assignToTrainee;
  String get planTitle;
  String get planDescription;
  String get startDate;
  String get endDate;
  String get dailyCalories;
  String get macros;
  String get protein;
  String get carbs;
  String get fats;
  String get meals;
  String get addMeal;
  String get exercises;
  String get addExercise;
  String get sets;
  String get reps;
  String get weight;
  String get restTime;
  String get instructions;

  // Analytics
  String get performanceAnalytics;
  String get revenue;
  String get sessionsCompleted;
  String get clientSatisfaction;
  String get monthlyStats;
  String get weeklyStats;
  String get topPerformingPlans;
  String get clientProgress;
  String get revenueGrowth;
  String get sessionTrends;

  // Schedule
  String get mySchedule;
  String get addAvailability;
  String get timeSlot;
  String get available;
  String get booked;
  String get blocked;
  String get setAvailable;
  String get blockTime;
  String get workingHours;
  String get breakTime;

  // Notifications
  String get myNotifications;
  String get markAllAsRead;
  String get noNotifications;
  String get newTraineeRequest;
  String get sessionBooked;
  String get sessionCancelled;
  String get paymentReceived;
  String get reviewReceived;

  // Chat
  String get messages;
  String get typeMessage;
  String get send;
  String get online;
  String get offline;
  String get selectTrainee;

  // Common
  String get save;
  String get cancel;
  String get delete;
  String get edit;
  String get update;
  String get create;
  String get assign;
  String get view;
  String get loading;
  String get error;
  String get success;
  String get retry;
  String get ok;
  String get yes;
  String get no;
  String get required;
  String get optional;
  String get kg;
  String get cm;
  String get years;
  String get minutes;
  String get hours;
  String get days;
  String get weeks;
  String get months;
  String get sar;
  String get search;
  String get filter;
  String get sort;
  String get all;
  String get active;
  String get inactive;
  String get pending;

  // Validation Messages
  String get emailRequired;
  String get emailInvalid;
  String get passwordRequired;
  String get passwordTooShort;
  String get passwordsNotMatch;
  String get nameRequired;
  String get titleRequired;
  String get descriptionRequired;
  String get priceRequired;
  String get priceInvalid;

  // Success Messages
  String get profileUpdated;
  String get planCreated;
  String get planAssigned;
  String get sessionScheduled;
  String get sessionCompleted;
  String get traineeApproved;
  String get notificationsSent;

  // Error Messages
  String get unexpectedError;
  String get networkError;
  String get authError;
  String get permissionDenied;
  String get planNotFound;
  String get traineeNotFound;
  String get sessionNotFound;

  // Settings
  String get language;
  String get arabic;
  String get english;
  String get changeLanguage;
  String get privacySecurity;
  String get helpSupport;
  String get aboutApp;
  String get version;
  String get accountSettings;
  String get businessSettings;
  String get paymentSettings;

  // Specializations
  String get weightLoss;
  String get muscleBuilding;
  String get cardioTraining;
  String get sportsNutrition;
  String get rehabilitation;
  String get generalFitness;
  String get seniorTraining;
  String get youthTraining;

  // Time Ago
  String get now;
  String get minuteAgo;
  String get minutesAgo;
  String get hourAgo;
  String get hoursAgo;
  String get dayAgo;
  String get daysAgo;
  String get weekAgo;
  String get weeksAgo;
  String get monthAgo;
  String get monthsAgo;
  String get yearAgo;
  String get yearsAgo;
}
