import '../main.dart';

class SampleDataService {
  static Future<void> createSampleData() async {
    try {
      // إنشاء مستخدمين تجريبيين (متدربين)
      await _createSampleTrainees();

      // إنشاء جلسات تجريبية
      await _createSampleSessions();

      // إنشاء خطط تمرين تجريبية
      await _createSampleWorkoutPlans();

      // إنشاء خطط تغذية تجريبية
      await _createSampleNutritionPlans();

      print('Sample data created successfully');
    } catch (e) {
      print('Error creating sample data: $e');
    }
  }

  static Future<void> _createSampleTrainees() async {
    // الحصول على معرف المدرب الحالي
    final user = supabase.auth.currentUser;
    if (user == null) return;

    final trainerList = await supabase
        .from('trainers')
        .select('id')
        .eq('user_id', user.id)
        .limit(1);

    final trainer = trainerList.isNotEmpty ? trainerList.first : null;

    if (trainer == null) return;

    // إنشاء مستخدمين تجريبيين
    final sampleUsers = [
      {
        'id': 'trainee-1',
        'email': '<EMAIL>',
        'full_name': 'أحمد علي محمد',
        'phone': '+966501234567',
        'user_type': 'trainee',
        'is_active': true,
        'avatar_url': null,
      },
      {
        'id': 'trainee-2',
        'email': '<EMAIL>',
        'full_name': 'سارة حسن أحمد',
        'phone': '+966507654321',
        'user_type': 'trainee',
        'is_active': true,
        'avatar_url': null,
      },
      {
        'id': 'trainee-3',
        'email': '<EMAIL>',
        'full_name': 'محمد عمر سالم',
        'phone': '+966509876543',
        'user_type': 'trainee',
        'is_active': true,
        'avatar_url': null,
      },
    ];

    // إدراج المستخدمين
    for (int i = 0; i < sampleUsers.length; i++) {
      final userData = sampleUsers[i];
      try {
        // التحقق من وجود المستخدم
        final existingUser = await supabase
            .from('users')
            .select('id')
            .eq('id', userData['id'] as String)
            .maybeSingle();

        if (existingUser == null) {
          await supabase.from('users').insert(userData);
        }

        // إنشاء ملف المتدرب الشخصي
        final existingProfile = await supabase
            .from('trainees_profiles')
            .select('id')
            .eq('user_id', userData['id'] as String)
            .maybeSingle();

        if (existingProfile == null) {
          final profileData = {
            'user_id': userData['id'],
            'age': 25 + (i * 5),
            'gender': i % 2 == 0 ? 'male' : 'female',
            'weight': 70.0 + (i * 5),
            'height': 170.0 + (i * 3),
            'fitness_goal': ['weight_loss', 'muscle_gain', 'endurance'][i % 3],
            'activity_level': 2 + (i % 3),
            'health_conditions': [],
            'dietary_preferences': [],
            'allergies': [],
            'medications': [],
            'target_weight': 65.0 + (i * 3),
            'target_date': DateTime.now()
                .add(Duration(days: 90 + (i * 30)))
                .toIso8601String()
                .split('T')[0],
          };
          await supabase.from('trainees_profiles').insert(profileData);
        }

        // إنشاء تعيين المدرب
        final existingAssignmentList = await supabase
            .from('trainer_assignments')
            .select('id')
            .eq('trainer_id', trainer['id'])
            .eq('trainee_id', userData['id'] as String)
            .limit(1);

        final existingAssignment = existingAssignmentList.isNotEmpty ? existingAssignmentList.first : null;

        if (existingAssignment == null) {
          await supabase.from('trainer_assignments').insert({
            'trainer_id': trainer['id'],
            'trainee_id': userData['id'],
            'status': 'active',
            'assigned_at': DateTime.now().toIso8601String(),
          });
        }
      } catch (e) {
        print('Error creating trainee ${userData['full_name']}: $e');
      }
    }
  }

  static Future<void> _createSampleSessions() async {
    final user = supabase.auth.currentUser;
    if (user == null) return;

    final trainerList = await supabase
        .from('trainers')
        .select('id')
        .eq('user_id', user.id)
        .limit(1);

    final trainer = trainerList.isNotEmpty ? trainerList.first : null;

    if (trainer == null) return;

    final sampleSessions = [
      {
        'trainer_id': trainer['id'],
        'trainee_id': 'trainee-1',
        'type': 'strength_training',
        'status': 'completed',
        'scheduled_at':
            DateTime.now().subtract(const Duration(days: 2)).toIso8601String(),
        'duration': 60,
        'location': 'الصالة الرياضية الرئيسية',
        'notes': 'جلسة ممتازة، تحسن واضح في الأداء',
      },
      {
        'trainer_id': trainer['id'],
        'trainee_id': 'trainee-2',
        'type': 'cardio',
        'status': 'scheduled',
        'scheduled_at':
            DateTime.now().add(const Duration(days: 1)).toIso8601String(),
        'duration': 45,
        'location': 'منطقة الكارديو',
        'notes': 'تركيز على تحسين اللياقة القلبية',
      },
      {
        'trainer_id': trainer['id'],
        'trainee_id': 'trainee-3',
        'type': 'functional_training',
        'status': 'in_progress',
        'scheduled_at': DateTime.now().toIso8601String(),
        'duration': 50,
        'location': 'منطقة التدريب الوظيفي',
        'notes': 'تمارين متنوعة لتحسين الحركة الوظيفية',
      },
      {
        'trainer_id': trainer['id'],
        'trainee_id': 'trainee-1',
        'type': 'strength_training',
        'status': 'scheduled',
        'scheduled_at':
            DateTime.now().add(const Duration(days: 3)).toIso8601String(),
        'duration': 60,
        'location': 'الصالة الرياضية الرئيسية',
        'notes': 'التركيز على تمارين الجزء العلوي',
      },
    ];

    for (final sessionData in sampleSessions) {
      try {
        await supabase.from('sessions').insert(sessionData);
      } catch (e) {
        print('Error creating session: $e');
      }
    }
  }

  static Future<void> _createSampleWorkoutPlans() async {
    final user = supabase.auth.currentUser;
    if (user == null) return;

    final trainerList = await supabase
        .from('trainers')
        .select('id')
        .eq('user_id', user.id)
        .limit(1);

    final trainer = trainerList.isNotEmpty ? trainerList.first : null;

    if (trainer == null) return;

    final sampleWorkoutPlans = [
      {
        'trainer_id': trainer['id'],
        'trainee_id': 'trainee-1',
        'name': 'برنامج بناء العضلات للمبتدئين',
        'description':
            'برنامج شامل لبناء العضلات مخصص للمبتدئين يركز على التمارين الأساسية',
        'duration_weeks': 8,
        'difficulty_level': 'beginner',
        'goals': ['muscle_building', 'strength'],
        'is_active': true,
      },
      {
        'trainer_id': trainer['id'],
        'trainee_id': 'trainee-2',
        'name': 'برنامج حرق الدهون المتقدم',
        'description': 'برنامج مكثف لحرق الدهون وتحسين اللياقة البدنية العامة',
        'duration_weeks': 12,
        'difficulty_level': 'intermediate',
        'goals': ['weight_loss', 'endurance'],
        'is_active': true,
      },
      {
        'trainer_id': trainer['id'],
        'trainee_id': 'trainee-3',
        'name': 'برنامج اللياقة الوظيفية',
        'description': 'تمارين وظيفية لتحسين الحركة والمرونة في الحياة اليومية',
        'duration_weeks': 6,
        'difficulty_level': 'intermediate',
        'goals': ['functional_fitness', 'flexibility'],
        'is_active': true,
      },
    ];

    for (final planData in sampleWorkoutPlans) {
      try {
        await supabase.from('workout_plans').insert(planData);
      } catch (e) {
        print('Error creating workout plan: $e');
      }
    }
  }

  static Future<void> _createSampleNutritionPlans() async {
    final user = supabase.auth.currentUser;
    if (user == null) return;

    final trainerList = await supabase
        .from('trainers')
        .select('id')
        .eq('user_id', user.id)
        .limit(1);

    final trainer = trainerList.isNotEmpty ? trainerList.first : null;

    if (trainer == null) return;

    final sampleNutritionPlans = [
      {
        'trainer_id': trainer['id'],
        'trainee_id': 'trainee-1',
        'name': 'نظام غذائي لبناء العضلات',
        'description': 'نظام غذائي عالي البروتين لدعم نمو العضلات',
        'calories_target': 2800,
        'protein_target': 150,
        'carbs_target': 300,
        'fat_target': 90,
        'is_active': true,
      },
      {
        'trainer_id': trainer['id'],
        'trainee_id': 'trainee-2',
        'name': 'نظام غذائي لحرق الدهون',
        'description': 'نظام غذائي منخفض السعرات لفقدان الوزن الصحي',
        'calories_target': 1800,
        'protein_target': 120,
        'carbs_target': 150,
        'fat_target': 60,
        'is_active': true,
      },
      {
        'trainer_id': trainer['id'],
        'trainee_id': 'trainee-3',
        'name': 'نظام غذائي متوازن',
        'description': 'نظام غذائي متوازن للحفاظ على الوزن وتحسين الصحة العامة',
        'calories_target': 2200,
        'protein_target': 110,
        'carbs_target': 250,
        'fat_target': 75,
        'is_active': true,
      },
    ];

    for (final planData in sampleNutritionPlans) {
      try {
        await supabase.from('nutrition_plans').insert(planData);
      } catch (e) {
        print('Error creating nutrition plan: $e');
      }
    }
  }

  static Future<void> clearSampleData() async {
    try {
      final user = supabase.auth.currentUser;
      if (user == null) return;

      final trainerList = await supabase
          .from('trainers')
          .select('id')
          .eq('user_id', user.id)
          .limit(1);

      final trainer = trainerList.isNotEmpty ? trainerList.first : null;

      if (trainer == null) return;

      // حذف البيانات التجريبية
      await supabase.from('sessions').delete().eq('trainer_id', trainer['id']);
      await supabase
          .from('workout_plans')
          .delete()
          .eq('trainer_id', trainer['id']);
      await supabase
          .from('nutrition_plans')
          .delete()
          .eq('trainer_id', trainer['id']);
      await supabase
          .from('trainer_assignments')
          .delete()
          .eq('trainer_id', trainer['id']);

      // حذف المستخدمين التجريبيين
      await supabase
          .from('users')
          .delete()
          .inFilter('id', ['trainee-1', 'trainee-2', 'trainee-3']);

      print('Sample data cleared successfully');
    } catch (e) {
      print('Error clearing sample data: $e');
    }
  }
}
