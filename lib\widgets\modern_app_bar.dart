import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

class ModernAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double elevation;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final PreferredSizeWidget? bottom;
  final Gradient? gradient;
  final bool showShadow;

  const ModernAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.centerTitle = true,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation = 0,
    this.showBackButton = true,
    this.onBackPressed,
    this.bottom,
    this.gradient,
    this.showShadow = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: gradient == null ? (backgroundColor ?? AppTheme.darkBackground) : null,
        gradient: gradient,
        boxShadow: showShadow
            ? [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: AppBar(
        title: Text(
          title,
          style: AppTheme.headlineSmall.copyWith(
            color: foregroundColor ?? AppTheme.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: centerTitle,
        backgroundColor: Colors.transparent,
        foregroundColor: foregroundColor ?? AppTheme.textPrimary,
        elevation: 0,
        leading: leading ??
            (showBackButton && Navigator.canPop(context)
                ? IconButton(
                    icon: const Icon(Icons.arrow_back_ios),
                    onPressed: onBackPressed ?? () => Navigator.pop(context),
                    color: foregroundColor ?? AppTheme.primaryGold,
                  )
                : null),
        actions: actions,
        bottom: bottom,
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
        kToolbarHeight + (bottom?.preferredSize.height ?? 0),
      );
}

class GradientAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  final Gradient gradient;
  final Color? foregroundColor;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final PreferredSizeWidget? bottom;

  const GradientAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.centerTitle = true,
    this.gradient = AppTheme.goldGradient,
    this.foregroundColor,
    this.showBackButton = true,
    this.onBackPressed,
    this.bottom,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: gradient,
        boxShadow: AppTheme.elevatedShadow,
      ),
      child: AppBar(
        title: Text(
          title,
          style: AppTheme.headlineSmall.copyWith(
            color: foregroundColor ?? Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: centerTitle,
        backgroundColor: Colors.transparent,
        foregroundColor: foregroundColor ?? Colors.black,
        elevation: 0,
        leading: leading ??
            (showBackButton && Navigator.canPop(context)
                ? IconButton(
                    icon: const Icon(Icons.arrow_back_ios),
                    onPressed: onBackPressed ?? () => Navigator.pop(context),
                    color: foregroundColor ?? Colors.black,
                  )
                : null),
        actions: actions,
        bottom: bottom,
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
        kToolbarHeight + (bottom?.preferredSize.height ?? 0),
      );
}

class SliverModernAppBar extends StatelessWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final bool floating;
  final bool pinned;
  final bool snap;
  final double expandedHeight;
  final Widget? flexibleSpace;
  final Gradient? gradient;

  const SliverModernAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.centerTitle = true,
    this.backgroundColor,
    this.foregroundColor,
    this.floating = false,
    this.pinned = true,
    this.snap = false,
    this.expandedHeight = 200.0,
    this.flexibleSpace,
    this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      title: Text(
        title,
        style: AppTheme.headlineSmall.copyWith(
          color: foregroundColor ?? AppTheme.textPrimary,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? AppTheme.darkBackground,
      foregroundColor: foregroundColor ?? AppTheme.textPrimary,
      floating: floating,
      pinned: pinned,
      snap: snap,
      expandedHeight: expandedHeight,
      leading: leading,
      actions: actions,
      flexibleSpace: flexibleSpace ??
          FlexibleSpaceBar(
            background: Container(
              decoration: BoxDecoration(
                color: gradient == null ? (backgroundColor ?? AppTheme.darkBackground) : null,
                gradient: gradient ?? AppTheme.darkGradient,
              ),
            ),
          ),
    );
  }
}

class TabBarCustom extends StatelessWidget implements PreferredSizeWidget {
  final List<Tab> tabs;
  final TabController? controller;
  final Color? indicatorColor;
  final Color? labelColor;
  final Color? unselectedLabelColor;
  final TextStyle? labelStyle;
  final TextStyle? unselectedLabelStyle;
  final EdgeInsetsGeometry? labelPadding;
  final bool isScrollable;

  const TabBarCustom({
    super.key,
    required this.tabs,
    this.controller,
    this.indicatorColor,
    this.labelColor,
    this.unselectedLabelColor,
    this.labelStyle,
    this.unselectedLabelStyle,
    this.labelPadding,
    this.isScrollable = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppTheme.surfaceColor,
        border: Border(
          bottom: BorderSide(
            color: AppTheme.borderColor,
            width: 1,
          ),
        ),
      ),
      child: TabBar(
        controller: controller,
        tabs: tabs,
        indicatorColor: indicatorColor ?? AppTheme.primaryGold,
        labelColor: labelColor ?? AppTheme.primaryGold,
        unselectedLabelColor: unselectedLabelColor ?? AppTheme.textSecondary,
        labelStyle: labelStyle ?? AppTheme.labelLarge,
        unselectedLabelStyle: unselectedLabelStyle ?? AppTheme.labelMedium,
        labelPadding: labelPadding,
        isScrollable: isScrollable,
        indicatorWeight: 3,
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kTextTabBarHeight);
}
