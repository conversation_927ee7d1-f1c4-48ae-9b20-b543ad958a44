import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../main.dart';

class AuthProvider with ChangeNotifier {
  User? _user;
  bool _isLoading = false;
  String? _errorMessage;

  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _user != null;

  AuthProvider() {
    _initializeAuth();
  }

  void _initializeAuth() {
    // Listen to auth state changes
    supabase.auth.onAuthStateChange.listen((data) {
      final AuthChangeEvent event = data.event;
      final Session? session = data.session;

      switch (event) {
        case AuthChangeEvent.signedIn:
          _user = session?.user;
          _errorMessage = null;
          notifyListeners();
          break;
        case AuthChangeEvent.signedOut:
          _user = null;
          _errorMessage = null;
          notifyListeners();
          break;
        case AuthChangeEvent.userUpdated:
          _user = session?.user;
          notifyListeners();
          break;
        default:
          break;
      }
    });

    // Check if user is already signed in
    final session = supabase.auth.currentSession;
    if (session != null) {
      _user = session.user;
    }
  }

  Future<bool> signIn(String email, String password) async {
    try {
      _setLoading(true);
      _errorMessage = null;

      final response = await supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        _user = response.user;
        _setLoading(false);
        return true;
      } else {
        _errorMessage = 'فشل في تسجيل الدخول';
        _setLoading(false);
        return false;
      }
    } on AuthException catch (e) {
      _errorMessage = _getArabicErrorMessage(e.message);
      _setLoading(false);
      return false;
    } catch (e) {
      _errorMessage = 'حدث خطأ غير متوقع';
      _setLoading(false);
      return false;
    }
  }

  Future<bool> signUp(String email, String password, String fullName) async {
    try {
      _setLoading(true);
      _errorMessage = null;

      final response = await supabase.auth.signUp(
        email: email,
        password: password,
        data: {
          'full_name': fullName,
        },
      );

      if (response.user != null) {
        // Create trainer profile
        await _createTrainerProfile(response.user!.id, fullName, email);
        _user = response.user;
        _setLoading(false);
        return true;
      } else {
        _errorMessage = 'فشل في إنشاء الحساب';
        _setLoading(false);
        return false;
      }
    } on AuthException catch (e) {
      _errorMessage = _getArabicErrorMessage(e.message);
      _setLoading(false);
      return false;
    } catch (e) {
      _errorMessage = 'حدث خطأ غير متوقع';
      _setLoading(false);
      return false;
    }
  }

  Future<void> _createTrainerProfile(String userId, String fullName, String email) async {
    try {
      // First create user record
      await supabase.from('users').insert({
        'id': userId,
        'email': email,
        'full_name': fullName,
        'user_type': 'trainer',
        'is_active': true,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      // Then create trainer profile
      await supabase.from('trainers').insert({
        'user_id': userId,
        'specializations': [],
        'experience_years': 0,
        'hourly_rate': 0.0,
        'currency': 'SAR',
        'is_available': true,
        'is_verified': false,
        'rating': 0.0,
        'total_sessions': 0,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      print('Error creating trainer profile: $e');
      // Don't throw error here as the user account was created successfully
    }
  }

  Future<void> signOut() async {
    try {
      _setLoading(true);
      await supabase.auth.signOut();
      _user = null;
      _errorMessage = null;
      _setLoading(false);
    } catch (e) {
      _errorMessage = 'فشل في تسجيل الخروج';
      _setLoading(false);
    }
  }

  Future<bool> resetPassword(String email) async {
    try {
      _setLoading(true);
      _errorMessage = null;

      await supabase.auth.resetPasswordForEmail(email);
      _setLoading(false);
      return true;
    } on AuthException catch (e) {
      _errorMessage = _getArabicErrorMessage(e.message);
      _setLoading(false);
      return false;
    } catch (e) {
      _errorMessage = 'حدث خطأ غير متوقع';
      _setLoading(false);
      return false;
    }
  }

  Future<bool> updateProfile({
    String? fullName,
    String? phone,
    String? avatarUrl,
  }) async {
    try {
      _setLoading(true);
      _errorMessage = null;

      final updates = <String, dynamic>{};
      if (fullName != null) updates['full_name'] = fullName;
      if (phone != null) updates['phone'] = phone;
      if (avatarUrl != null) updates['avatar_url'] = avatarUrl;

      if (updates.isNotEmpty) {
        await supabase.auth.updateUser(UserAttributes(data: updates));
        
        // Also update in users table
        await supabase.from('users').update({
          ...updates,
          'updated_at': DateTime.now().toIso8601String(),
        }).eq('id', _user!.id);
      }

      _setLoading(false);
      return true;
    } catch (e) {
      _errorMessage = 'فشل في تحديث الملف الشخصي';
      _setLoading(false);
      return false;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  String _getArabicErrorMessage(String englishMessage) {
    final errorMap = {
      'Invalid login credentials': 'بيانات تسجيل الدخول غير صحيحة',
      'Email not confirmed': 'البريد الإلكتروني غير مؤكد',
      'User already registered': 'المستخدم مسجل مسبقاً',
      'Password should be at least 6 characters': 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
      'Invalid email': 'البريد الإلكتروني غير صحيح',
      'Signup requires a valid password': 'التسجيل يتطلب كلمة مرور صحيحة',
      'Unable to validate email address: invalid format': 'تنسيق البريد الإلكتروني غير صحيح',
      'Password is too weak': 'كلمة المرور ضعيفة جداً',
      'Email rate limit exceeded': 'تم تجاوز حد إرسال الرسائل',
    };

    return errorMap[englishMessage] ?? 'حدث خطأ غير متوقع';
  }
}
