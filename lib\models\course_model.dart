class CourseModel {
  final String id;
  final String instructorId;
  final String title;
  final String? description;
  final String? thumbnailUrl;
  final String difficultyLevel;
  final int estimatedDuration;
  final double price;
  final String currency;
  final bool isPublished;
  final bool isFeatured;
  final int enrollmentCount;
  final double rating;
  final int ratingCount;
  final List<String> tags;
  final String? categoryId;
  final Map<String, dynamic>? courseData;
  final DateTime createdAt;
  final DateTime updatedAt;

  CourseModel({
    required this.id,
    required this.instructorId,
    required this.title,
    this.description,
    this.thumbnailUrl,
    this.difficultyLevel = 'beginner',
    this.estimatedDuration = 0,
    this.price = 0.0,
    this.currency = 'SAR',
    this.isPublished = false,
    this.isFeatured = false,
    this.enrollmentCount = 0,
    this.rating = 0.0,
    this.ratingCount = 0,
    this.tags = const [],
    this.categoryId,
    this.courseData,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CourseModel.fromJson(Map<String, dynamic> json) {
    return CourseModel(
      id: json['id'] as String,
      instructorId: json['instructor_id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      thumbnailUrl: json['thumbnail_url'] as String?,
      difficultyLevel: json['difficulty_level'] as String? ?? 'beginner',
      estimatedDuration: json['estimated_duration'] as int? ?? 0,
      price: (json['price'] as num?)?.toDouble() ?? 0.0,
      currency: json['currency'] as String? ?? 'SAR',
      isPublished: json['is_published'] as bool? ?? false,
      isFeatured: json['is_featured'] as bool? ?? false,
      enrollmentCount: json['enrollment_count'] as int? ?? 0,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      ratingCount: json['rating_count'] as int? ?? 0,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e.toString()).toList() ?? [],
      categoryId: json['category_id'] as String?,
      courseData: json['course_data'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'instructor_id': instructorId,
      'title': title,
      'description': description,
      'thumbnail_url': thumbnailUrl,
      'difficulty_level': difficultyLevel,
      'estimated_duration': estimatedDuration,
      'price': price,
      'currency': currency,
      'is_published': isPublished,
      'is_featured': isFeatured,
      'enrollment_count': enrollmentCount,
      'rating': rating,
      'rating_count': ratingCount,
      'tags': tags,
      'category_id': categoryId,
      'course_data': courseData,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get difficultyDisplayName {
    switch (difficultyLevel) {
      case 'beginner':
        return 'مبتدئ';
      case 'intermediate':
        return 'متوسط';
      case 'advanced':
        return 'متقدم';
      case 'expert':
        return 'خبير';
      default:
        return difficultyLevel;
    }
  }

  String get formattedDuration {
    if (estimatedDuration < 60) {
      return '$estimatedDuration دقيقة';
    } else {
      final hours = estimatedDuration ~/ 60;
      final minutes = estimatedDuration % 60;
      if (minutes == 0) {
        return '$hours ساعة';
      } else {
        return '$hours ساعة و $minutes دقيقة';
      }
    }
  }

  bool get isFree => price == 0.0;
}

class LessonModel {
  final String id;
  final String courseId;
  final String title;
  final String? description;
  final String contentType;
  final String? contentUrl;
  final String? videoUrl;
  final String? thumbnailUrl;
  final int duration;
  final int orderIndex;
  final bool isPreview;
  final bool isPublished;
  final Map<String, dynamic>? lessonData;
  final DateTime createdAt;
  final DateTime updatedAt;

  LessonModel({
    required this.id,
    required this.courseId,
    required this.title,
    this.description,
    this.contentType = 'video',
    this.contentUrl,
    this.videoUrl,
    this.thumbnailUrl,
    this.duration = 0,
    this.orderIndex = 0,
    this.isPreview = false,
    this.isPublished = false,
    this.lessonData,
    required this.createdAt,
    required this.updatedAt,
  });

  factory LessonModel.fromJson(Map<String, dynamic> json) {
    return LessonModel(
      id: json['id'] as String,
      courseId: json['course_id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      contentType: json['content_type'] as String? ?? 'video',
      contentUrl: json['content_url'] as String?,
      videoUrl: json['video_url'] as String?,
      thumbnailUrl: json['thumbnail_url'] as String?,
      duration: json['duration'] as int? ?? 0,
      orderIndex: json['order_index'] as int? ?? 0,
      isPreview: json['is_preview'] as bool? ?? false,
      isPublished: json['is_published'] as bool? ?? false,
      lessonData: json['lesson_data'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'course_id': courseId,
      'title': title,
      'description': description,
      'content_type': contentType,
      'content_url': contentUrl,
      'video_url': videoUrl,
      'thumbnail_url': thumbnailUrl,
      'duration': duration,
      'order_index': orderIndex,
      'is_preview': isPreview,
      'is_published': isPublished,
      'lesson_data': lessonData,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get contentTypeDisplayName {
    switch (contentType) {
      case 'video':
        return 'فيديو';
      case 'article':
        return 'مقال';
      case 'quiz':
        return 'اختبار';
      case 'exercise':
        return 'تمرين';
      case 'document':
        return 'مستند';
      default:
        return contentType;
    }
  }

  String get formattedDuration {
    if (duration < 60) {
      return '$duration ثانية';
    } else {
      final minutes = duration ~/ 60;
      final seconds = duration % 60;
      if (seconds == 0) {
        return '$minutes دقيقة';
      } else {
        return '$minutes:${seconds.toString().padLeft(2, '0')}';
      }
    }
  }
}

class CourseEnrollmentModel {
  final String id;
  final String courseId;
  final String userId;
  final DateTime enrolledAt;
  final DateTime? completedAt;
  final double progressPercentage;
  final DateTime? lastAccessedAt;
  final Map<String, dynamic>? enrollmentData;
  final DateTime createdAt;
  final DateTime updatedAt;

  CourseEnrollmentModel({
    required this.id,
    required this.courseId,
    required this.userId,
    required this.enrolledAt,
    this.completedAt,
    this.progressPercentage = 0.0,
    this.lastAccessedAt,
    this.enrollmentData,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CourseEnrollmentModel.fromJson(Map<String, dynamic> json) {
    return CourseEnrollmentModel(
      id: json['id'] as String,
      courseId: json['course_id'] as String,
      userId: json['user_id'] as String,
      enrolledAt: DateTime.parse(json['enrolled_at'] as String),
      completedAt: json['completed_at'] != null 
          ? DateTime.parse(json['completed_at'] as String) 
          : null,
      progressPercentage: (json['progress_percentage'] as num?)?.toDouble() ?? 0.0,
      lastAccessedAt: json['last_accessed_at'] != null 
          ? DateTime.parse(json['last_accessed_at'] as String) 
          : null,
      enrollmentData: json['enrollment_data'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'course_id': courseId,
      'user_id': userId,
      'enrolled_at': enrolledAt.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
      'progress_percentage': progressPercentage,
      'last_accessed_at': lastAccessedAt?.toIso8601String(),
      'enrollment_data': enrollmentData,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  bool get isCompleted => completedAt != null;
  bool get isInProgress => progressPercentage > 0 && !isCompleted;
  bool get isNotStarted => progressPercentage == 0;
}
