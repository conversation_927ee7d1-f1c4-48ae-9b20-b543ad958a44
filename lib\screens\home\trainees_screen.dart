import 'package:flutter/material.dart';
import '../../services/localization_service.dart';
import '../../main.dart';
import '../../widgets/custom_app_bar.dart';
import '../trainee/trainee_details_screen.dart';
import '../../services/sample_data_service.dart';

class TraineesScreen extends StatefulWidget {
  const TraineesScreen({super.key});

  @override
  State<TraineesScreen> createState() => _TraineesScreenState();
}

class _TraineesScreenState extends State<TraineesScreen> {
  List<Map<String, dynamic>> _trainees = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadTrainees();
  }

  Future<void> _loadTrainees() async {
    try {
      final user = supabase.auth.currentUser;
      if (user == null) return;
      final trainerList = await supabase
          .from('trainers')
          .select('id')
          .eq('user_id', user.id)
          .limit(1);

      final trainer = trainerList.isNotEmpty ? trainerList.first : null;
      if (trainer == null) return;
      // جلب المتدربين مع معلومات المستخدم والملف الشخصي
      final traineesData = await supabase
          .from('trainer_assignments')
          .select('''
            trainee_id,
            status,
            start_date,
            end_date,
            notes,
            users!trainer_assignments_trainee_id_fkey (
              id,
              full_name,
              avatar_url,
              email,
              phone,
              is_active
            )
          ''')
          .eq('trainer_id', trainer['id'])
          .eq('status', 'active')
          .order('created_at', ascending: false);

      // جلب ملفات المتدربين الشخصية منفصلة (إزالة المكررات)
      final traineeIds = traineesData
          .map((t) => t['trainee_id'] as String)
          .toSet() // إزالة المكررات
          .toList();
      List<Map<String, dynamic>> profilesData = [];
      if (traineeIds.isNotEmpty) {
        profilesData = await supabase
            .from('trainees_profiles')
            .select(
                'user_id, age, gender, weight, height, fitness_goal, activity_level, target_weight, target_date')
            .inFilter('user_id', traineeIds);
      }

      // دمج البيانات وإزالة المكررات
      final Map<String, Map<String, dynamic>> uniqueTrainees = {};

      for (final assignment in traineesData) {
        final traineeId = assignment['trainee_id'] as String;
        if (uniqueTrainees.containsKey(traineeId)) continue; // تخطي المكررات

        final userData = assignment['users'];
        final profileData = profilesData.firstWhere(
          (p) => p['user_id'] == traineeId,
          orElse: () => <String, dynamic>{},
        );

        uniqueTrainees[traineeId] = {
          'trainee_id': traineeId,
          'status': assignment['status'],
          'start_date': assignment['start_date'],
          'end_date': assignment['end_date'],
          'assignment_notes': assignment['notes'],
          // بيانات المستخدم
          'full_name': userData?['full_name'] ?? '',
          'avatar_url': userData?['avatar_url'],
          'email': userData?['email'] ?? '',
          'phone': userData?['phone'],
          'is_active': userData?['is_active'] ?? true,
          // بيانات الملف الشخصي
          'age': profileData['age'],
          'gender': profileData['gender'],
          'weight': profileData['weight'],
          'height': profileData['height'],
          'fitness_goal': profileData['fitness_goal'],
          'activity_level': profileData['activity_level'],
          'target_weight': profileData['target_weight'],
          'target_date': profileData['target_date'],
        };
      }

      final detailedTrainees = uniqueTrainees.values.toList();
      setState(() {
        _trainees = List<Map<String, dynamic>>.from(detailedTrainees);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: LocalizationService.isArabic ? 'المتدربين' : 'Trainees',
        actions: [
          IconButton(
            icon: const Icon(Icons.add_circle_outline),
            onPressed: () async {
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              await SampleDataService.createSampleData();
              _loadTrainees();
              if (mounted) {
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('تم إنشاء البيانات التجريبية'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            tooltip: 'إنشاء بيانات تجريبية',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Color(0xFFFFD700)))
          : _trainees.isEmpty
              ? Center(
                  child: Text(
                    LocalizationService.isArabic
                        ? 'لا يوجد متدربين'
                        : 'No trainees found',
                    style: const TextStyle(fontSize: 20, color: Colors.grey),
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _trainees.length,
                  itemBuilder: (context, index) {
                    final trainee = _trainees[index];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF232526), Color(0xFF414345)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(18),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.12),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: const Color(0xFFFFD700),
                          backgroundImage: trainee['avatar_url'] != null &&
                                  trainee['avatar_url'] != ''
                              ? NetworkImage(trainee['avatar_url'])
                              : null,
                          child: (trainee['avatar_url'] == null ||
                                  trainee['avatar_url'] == '')
                              ? const Icon(Icons.person, color: Colors.black)
                              : null,
                        ),
                        title: Text(
                          trainee['full_name'] != ''
                              ? trainee['full_name']
                              : 'ID: ${trainee['trainee_id']}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              trainee['email'] ?? '',
                              style: const TextStyle(
                                  color: Colors.white70, fontSize: 13),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              LocalizationService.isArabic
                                  ? 'الحالة: ${trainee['status']}'
                                  : 'Status: ${trainee['status']}',
                              style: const TextStyle(color: Colors.white70),
                            ),
                          ],
                        ),
                        trailing: const Icon(Icons.arrow_forward_ios,
                            color: Colors.white54, size: 18),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 12),
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => TraineeDetailsScreen(
                                trainee: trainee,
                              ),
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
    );
  }
}
