import 'package:flutter/material.dart';
import '../../services/localization_service.dart';
import '../../main.dart';
import '../../widgets/custom_app_bar.dart';
import '../trainee/trainee_details_screen.dart';
import '../../services/sample_data_service.dart';

class TraineesScreen extends StatefulWidget {
  const TraineesScreen({super.key});

  @override
  State<TraineesScreen> createState() => _TraineesScreenState();
}

class _TraineesScreenState extends State<TraineesScreen> {
  List<Map<String, dynamic>> _trainees = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadTrainees();
  }

  Future<void> _loadTrainees() async {
    try {
      final user = supabase.auth.currentUser;
      if (user == null) return;
      final trainer = await supabase
          .from('trainers')
          .select('id')
          .eq('user_id', user.id)
          .maybeSingle();
      if (trainer == null) return;
      final trainees = await supabase
          .from('trainer_assignments')
          .select('trainee_id, status')
          .eq('trainer_id', trainer['id']);
      // جلب بيانات المتدربين من جدول users دفعة واحدة
      final traineeIds =
          trainees.map((t) => t['trainee_id'] as String).toList();
      List<Map<String, dynamic>> usersData = [];
      if (traineeIds.isNotEmpty) {
        usersData = await supabase
            .from('users')
            .select('id, full_name, avatar_url, email')
            .inFilter('id', traineeIds);
      }
      // دمج بيانات المتدرب مع بيانات التعيين
      final detailedTrainees = trainees.map((t) {
        final userData = usersData.firstWhere(
          (u) => u['id'] == t['trainee_id'],
          orElse: () => {'full_name': '', 'avatar_url': '', 'email': ''},
        );
        return {
          ...t,
          'full_name': userData['full_name'],
          'avatar_url': userData['avatar_url'],
          'email': userData['email'],
        };
      }).toList();
      setState(() {
        _trainees = List<Map<String, dynamic>>.from(detailedTrainees);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: LocalizationService.isArabic ? 'المتدربين' : 'Trainees',
        actions: [
          IconButton(
            icon: const Icon(Icons.add_circle_outline),
            onPressed: () async {
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              await SampleDataService.createSampleData();
              _loadTrainees();
              if (mounted) {
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('تم إنشاء البيانات التجريبية'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            tooltip: 'إنشاء بيانات تجريبية',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Color(0xFFFFD700)))
          : _trainees.isEmpty
              ? Center(
                  child: Text(
                    LocalizationService.isArabic
                        ? 'لا يوجد متدربين'
                        : 'No trainees found',
                    style: const TextStyle(fontSize: 20, color: Colors.grey),
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _trainees.length,
                  itemBuilder: (context, index) {
                    final trainee = _trainees[index];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF232526), Color(0xFF414345)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(18),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.12),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: const Color(0xFFFFD700),
                          backgroundImage: trainee['avatar_url'] != null &&
                                  trainee['avatar_url'] != ''
                              ? NetworkImage(trainee['avatar_url'])
                              : null,
                          child: (trainee['avatar_url'] == null ||
                                  trainee['avatar_url'] == '')
                              ? const Icon(Icons.person, color: Colors.black)
                              : null,
                        ),
                        title: Text(
                          trainee['full_name'] != ''
                              ? trainee['full_name']
                              : 'ID: ${trainee['trainee_id']}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              trainee['email'] ?? '',
                              style: const TextStyle(
                                  color: Colors.white70, fontSize: 13),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              LocalizationService.isArabic
                                  ? 'الحالة: ${trainee['status']}'
                                  : 'Status: ${trainee['status']}',
                              style: const TextStyle(color: Colors.white70),
                            ),
                          ],
                        ),
                        trailing: const Icon(Icons.arrow_forward_ios,
                            color: Colors.white54, size: 18),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 12),
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => TraineeDetailsScreen(
                                trainee: trainee,
                              ),
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
    );
  }
}
