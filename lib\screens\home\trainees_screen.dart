import 'package:flutter/material.dart';
import '../../services/localization_service.dart';
import '../../main.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/premium_widgets.dart';
import '../../theme/app_theme.dart';
import '../trainee/trainee_details_screen.dart';
import '../../services/sample_data_service.dart';

class TraineesScreen extends StatefulWidget {
  const TraineesScreen({super.key});

  @override
  State<TraineesScreen> createState() => _TraineesScreenState();
}

class _TraineesScreenState extends State<TraineesScreen> {
  List<Map<String, dynamic>> _trainees = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadTrainees();
  }

  Future<void> _loadTrainees() async {
    try {
      final user = supabase.auth.currentUser;
      if (user == null) return;
      final trainerList = await supabase
          .from('trainers')
          .select('id')
          .eq('user_id', user.id)
          .limit(1);

      final trainer = trainerList.isNotEmpty ? trainerList.first : null;
      if (trainer == null) return;
      // جلب المتدربين مع معلومات المستخدم والملف الشخصي
      final traineesData = await supabase
          .from('trainer_assignments')
          .select('''
            trainee_id,
            status,
            start_date,
            end_date,
            notes,
            users!trainer_assignments_trainee_id_fkey (
              id,
              full_name,
              avatar_url,
              email,
              phone,
              is_active
            )
          ''')
          .eq('trainer_id', trainer['id'])
          .eq('status', 'active')
          .order('created_at', ascending: false);

      // جلب ملفات المتدربين الشخصية منفصلة (إزالة المكررات)
      final traineeIds = traineesData
          .map((t) => t['trainee_id'] as String)
          .toSet() // إزالة المكررات
          .toList();
      List<Map<String, dynamic>> profilesData = [];
      if (traineeIds.isNotEmpty) {
        profilesData = await supabase
            .from('trainees_profiles')
            .select(
                'user_id, age, gender, weight, height, fitness_goal, activity_level, target_weight, target_date')
            .inFilter('user_id', traineeIds);
      }

      // دمج البيانات وإزالة المكررات
      final Map<String, Map<String, dynamic>> uniqueTrainees = {};

      for (final assignment in traineesData) {
        final traineeId = assignment['trainee_id'] as String;
        if (uniqueTrainees.containsKey(traineeId)) continue; // تخطي المكررات

        final userData = assignment['users'];
        final profileData = profilesData.firstWhere(
          (p) => p['user_id'] == traineeId,
          orElse: () => <String, dynamic>{},
        );

        uniqueTrainees[traineeId] = {
          'trainee_id': traineeId,
          'status': assignment['status'],
          'start_date': assignment['start_date'],
          'end_date': assignment['end_date'],
          'assignment_notes': assignment['notes'],
          // بيانات المستخدم
          'full_name': userData?['full_name'] ?? '',
          'avatar_url': userData?['avatar_url'],
          'email': userData?['email'] ?? '',
          'phone': userData?['phone'],
          'is_active': userData?['is_active'] ?? true,
          // بيانات الملف الشخصي
          'age': profileData['age'],
          'gender': profileData['gender'],
          'weight': profileData['weight'],
          'height': profileData['height'],
          'fitness_goal': profileData['fitness_goal'],
          'activity_level': profileData['activity_level'],
          'target_weight': profileData['target_weight'],
          'target_date': profileData['target_date'],
        };
      }

      final detailedTrainees = uniqueTrainees.values.toList();
      setState(() {
        _trainees = List<Map<String, dynamic>>.from(detailedTrainees);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      appBar: CustomAppBar(
        title: LocalizationService.isArabic ? 'المتدربين' : 'Trainees',
        actions: [
          Container(
            margin: const EdgeInsets.only(right: AppTheme.spacingM),
            child: PremiumActionButton(
              text: '',
              icon: Icons.add_circle_outline,
              isPrimary: false,
              width: 48,
              height: 48,
              onPressed: () async {
                final scaffoldMessenger = ScaffoldMessenger.of(context);
                await SampleDataService.createSampleData();
                _loadTrainees();
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text(
                        LocalizationService.isArabic
                            ? 'تم إنشاء البيانات التجريبية'
                            : 'Sample data created',
                      ),
                      backgroundColor: AppTheme.successGreen,
                    ),
                  );
                }
              },
            ),
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.darkGradient,
        ),
        child: _isLoading
            ? const Center(
                child: CircularProgressIndicator(color: AppTheme.primaryGold))
            : _trainees.isEmpty
                ? _buildEmptyState()
                : _buildTraineesList(),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: PremiumGlassCard(
        width: double.infinity,
        margin: const EdgeInsets.all(AppTheme.spacingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingL),
              decoration: BoxDecoration(
                color: AppTheme.primaryGold.withOpacity(0.2),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.people_outline,
                size: 64,
                color: AppTheme.primaryGold,
              ),
            ),
            const SizedBox(height: AppTheme.spacingL),
            Text(
              LocalizationService.isArabic
                  ? 'لا يوجد متدربين'
                  : 'No trainees found',
              style: AppTheme.headlineSmall.copyWith(
                color: AppTheme.textPrimary,
              ),
            ),
            const SizedBox(height: AppTheme.spacingS),
            Text(
              LocalizationService.isArabic
                  ? 'ابدأ بإضافة متدربين جدد'
                  : 'Start by adding new trainees',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
            const SizedBox(height: AppTheme.spacingL),
            PremiumActionButton(
              text:
                  LocalizationService.isArabic ? 'إضافة متدرب' : 'Add Trainee',
              icon: Icons.add,
              onPressed: () async {
                final scaffoldMessenger = ScaffoldMessenger.of(context);
                await SampleDataService.createSampleData();
                _loadTrainees();
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text(
                        LocalizationService.isArabic
                            ? 'تم إنشاء البيانات التجريبية'
                            : 'Sample data created',
                      ),
                      backgroundColor: AppTheme.successGreen,
                    ),
                  );
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTraineesList() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      itemCount: _trainees.length,
      itemBuilder: (context, index) {
        final trainee = _trainees[index];
        return _buildTraineeCard(trainee, index);
      },
    );
  }

  Widget _buildTraineeCard(Map<String, dynamic> trainee, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      child: PremiumListTile(
        leading: PremiumAvatar(
          imageUrl: trainee['avatar_url'],
          name: trainee['full_name'],
          radius: 28,
          showStatus: true,
          isOnline: trainee['is_active'] ?? false,
        ),
        title: trainee['full_name'] != ''
            ? trainee['full_name']
            : 'ID: ${trainee['trainee_id']}',
        subtitle: _buildTraineeSubtitle(trainee),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            PremiumStatusBadge(
              text: trainee['status'] ?? 'active',
              color: _getStatusColor(trainee['status']),
              icon: _getStatusIcon(trainee['status']),
            ),
            const SizedBox(width: AppTheme.spacingS),
            const Icon(
              Icons.arrow_forward_ios,
              color: AppTheme.textTertiary,
              size: 16,
            ),
          ],
        ),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => TraineeDetailsScreen(
                trainee: trainee,
              ),
            ),
          );
        },
      ),
    );
  }

  String _buildTraineeSubtitle(Map<String, dynamic> trainee) {
    final List<String> subtitleParts = [];

    if (trainee['email'] != null && trainee['email'] != '') {
      subtitleParts.add(trainee['email']);
    }

    if (trainee['age'] != null) {
      subtitleParts.add(LocalizationService.isArabic
          ? 'العمر: ${trainee['age']}'
          : 'Age: ${trainee['age']}');
    }

    if (trainee['fitness_goal'] != null && trainee['fitness_goal'] != '') {
      subtitleParts.add(LocalizationService.isArabic
          ? 'الهدف: ${trainee['fitness_goal']}'
          : 'Goal: ${trainee['fitness_goal']}');
    }

    return subtitleParts.join(' • ');
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return AppTheme.successGreen;
      case 'pending':
        return AppTheme.warningOrange;
      case 'inactive':
        return AppTheme.errorRed;
      default:
        return AppTheme.textSecondary;
    }
  }

  IconData _getStatusIcon(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return Icons.check_circle;
      case 'pending':
        return Icons.pending;
      case 'inactive':
        return Icons.pause_circle;
      default:
        return Icons.help;
    }
  }
}
