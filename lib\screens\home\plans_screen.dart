import 'package:flutter/material.dart';
import 'dart:io';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import '../../services/localization_service.dart';
import '../../main.dart';
import '../../theme/app_theme.dart';
import '../../services/database_service.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/premium_widgets.dart';

class PlansScreen extends StatefulWidget {
  const PlansScreen({super.key});

  @override
  State<PlansScreen> createState() => _PlansScreenState();
}

class _PlansScreenState extends State<PlansScreen> {
  List<Map<String, dynamic>> _plans = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPlans();
  }

  Future<void> _loadPlans() async {
    try {
      final user = supabase.auth.currentUser;
      if (user == null) return;
      final trainerList = await supabase
          .from('trainers')
          .select('id')
          .eq('user_id', user.id)
          .limit(1);

      final trainer = trainerList.isNotEmpty ? trainerList.first : null;
      if (trainer == null) return;
      // جلب خطط التغذية وخطط التمارين معًا
      final nutritionPlans = await supabase
          .from('nutrition_plans')
          .select('*')
          .eq('trainer_id', trainer['id']);
      final workoutPlans = await supabase
          .from('workout_plans')
          .select('*')
          .eq('trainer_id', trainer['id']);
      final plans = [
        ...nutritionPlans.map((p) => {...p, 'plan_type': 'nutrition'}),
        ...workoutPlans.map((p) => {...p, 'plan_type': 'workout'}),
      ];
      // جلب بيانات المتدربين المرتبطين بكل خطة
      final traineeIds = plans
          .map((p) => p['trainee_id'] as String?)
          .where((id) => id != null)
          .toSet()
          .toList();
      List<Map<String, dynamic>> usersData = [];
      if (traineeIds.isNotEmpty) {
        usersData = await supabase
            .from('users')
            .select('id, full_name, avatar_url')
            .inFilter('id', traineeIds);
      }
      final detailedPlans = plans.map((p) {
        final userData = usersData.firstWhere(
          (u) => u['id'] == p['trainee_id'],
          orElse: () => {'full_name': '', 'avatar_url': ''},
        );
        return {
          ...p,
          'trainee_name': userData['full_name'],
          'trainee_avatar': userData['avatar_url'],
        };
      }).toList();
      setState(() {
        _plans = List<Map<String, dynamic>>.from(detailedPlans);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showAddPlanDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return AddPlanDialog(onPlanAdded: _loadPlans);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      appBar: CustomAppBar(
        title: LocalizationService.isArabic ? 'الخطط' : 'Plans',
        actions: [
          Container(
            margin: const EdgeInsets.only(right: AppTheme.spacingM),
            child: PremiumActionButton(
              text: '',
              icon: Icons.add,
              isPrimary: false,
              width: 48,
              height: 48,
              onPressed: _showAddPlanDialog,
            ),
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.darkGradient,
        ),
        child: _isLoading
            ? const Center(
                child: CircularProgressIndicator(color: AppTheme.primaryGold))
            : _plans.isEmpty
                ? _buildEmptyState()
                : _buildPlansList(),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: PremiumGlassCard(
        width: double.infinity,
        margin: const EdgeInsets.all(AppTheme.spacingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingL),
              decoration: BoxDecoration(
                color: AppTheme.primaryGold.withValues(alpha: 0.2),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.assignment,
                size: 64,
                color: AppTheme.primaryGold,
              ),
            ),
            const SizedBox(height: AppTheme.spacingL),
            Text(
              LocalizationService.isArabic ? 'لا توجد خطط' : 'No plans found',
              style: AppTheme.headlineSmall.copyWith(
                color: AppTheme.textPrimary,
              ),
            ),
            const SizedBox(height: AppTheme.spacingS),
            Text(
              LocalizationService.isArabic
                  ? 'ابدأ بإنشاء خطط تدريب وتغذية'
                  : 'Start by creating workout and nutrition plans',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
            const SizedBox(height: AppTheme.spacingL),
            PremiumActionButton(
              text: LocalizationService.isArabic ? 'إنشاء خطة' : 'Create Plan',
              icon: Icons.add_circle,
              onPressed: _showAddPlanDialog,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlansList() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      itemCount: _plans.length,
      itemBuilder: (context, index) {
        final plan = _plans[index];
        return _buildPlanCard(plan, index);
      },
    );
  }

  Widget _buildPlanCard(Map<String, dynamic> plan, int index) {
    final isNutrition = plan['plan_type'] == 'nutrition';
    final planColor = isNutrition ? AppTheme.infoBlue : AppTheme.warningOrange;
    final planIcon = isNutrition ? Icons.restaurant : Icons.fitness_center;

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      child: PremiumGlassCard(
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with avatar and plan type
              Row(
                children: [
                  PremiumAvatar(
                    imageUrl: plan['trainee_avatar'],
                    name: plan['trainee_name'],
                    radius: 24,
                    showStatus: false,
                  ),
                  const SizedBox(width: AppTheme.spacingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          plan['trainee_name'] != ''
                              ? plan['trainee_name']
                              : (LocalizationService.isArabic
                                  ? 'خطة رقم: ${plan['id']}'
                                  : 'Plan ID: ${plan['id']}'),
                          style: AppTheme.bodyLarge.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textPrimary,
                          ),
                        ),
                        const SizedBox(height: AppTheme.spacingXS),
                        Row(
                          children: [
                            Icon(
                              planIcon,
                              size: 16,
                              color: planColor,
                            ),
                            const SizedBox(width: AppTheme.spacingXS),
                            Text(
                              LocalizationService.isArabic
                                  ? (isNutrition ? 'خطة تغذية' : 'خطة تمارين')
                                  : (isNutrition
                                      ? 'Nutrition Plan'
                                      : 'Workout Plan'),
                              style: AppTheme.bodySmall.copyWith(
                                color: AppTheme.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  PremiumStatusBadge(
                    text: LocalizationService.isArabic ? 'نشط' : 'Active',
                    color: AppTheme.successGreen,
                    icon: Icons.check_circle,
                  ),
                ],
              ),

              const SizedBox(height: AppTheme.spacingM),

              // Plan description
              if (plan['description'] != null && plan['description'] != '')
                Container(
                  padding: const EdgeInsets.all(AppTheme.spacingM),
                  decoration: BoxDecoration(
                    color: AppTheme.glassColor,
                    borderRadius: AppTheme.mediumRadius,
                    border: Border.all(
                      color: planColor.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.description,
                        size: 16,
                        color: planColor,
                      ),
                      const SizedBox(width: AppTheme.spacingS),
                      Expanded(
                        child: Text(
                          plan['description'],
                          style: AppTheme.bodySmall.copyWith(
                            color: AppTheme.textPrimary,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),

              const SizedBox(height: AppTheme.spacingM),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: PremiumActionButton(
                      text: LocalizationService.isArabic ? 'عرض' : 'View',
                      icon: Icons.visibility,
                      isPrimary: false,
                      onPressed: () {
                        // Navigate to plan details
                      },
                    ),
                  ),
                  const SizedBox(width: AppTheme.spacingS),
                  Expanded(
                    child: PremiumActionButton(
                      text: LocalizationService.isArabic ? 'تعديل' : 'Edit',
                      icon: Icons.edit,
                      onPressed: () {
                        // Navigate to edit plan
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class AddPlanDialog extends StatefulWidget {
  final VoidCallback onPlanAdded;
  const AddPlanDialog({super.key, required this.onPlanAdded});

  @override
  State<AddPlanDialog> createState() => _AddPlanDialogState();
}

class _AddPlanDialogState extends State<AddPlanDialog> {
  final _formKey = GlobalKey<FormState>();
  final ImagePicker _imagePicker = ImagePicker();
  String _planType = 'nutrition';
  String? _selectedTraineeId;
  String _description = '';
  String _details = '';
  bool _isLoading = false;
  List<Map<String, dynamic>> _trainees = [];
  List<File> _selectedImages = [];
  List<File> _selectedVideos = [];
  List<String> _uploadedImageUrls = [];
  List<String> _uploadedVideoUrls = [];

  @override
  void initState() {
    super.initState();
    _fetchTrainees();
  }

  Future<void> _fetchTrainees() async {
    final user = supabase.auth.currentUser;
    if (user == null) return;
    final trainerList = await supabase
        .from('trainers')
        .select('id')
        .eq('user_id', user.id)
        .limit(1);

    final trainer = trainerList.isNotEmpty ? trainerList.first : null;
    if (trainer == null) return;
    final assignments = await supabase
        .from('trainer_assignments')
        .select('trainee_id')
        .eq('trainer_id', trainer['id']);
    final traineeIds =
        assignments.map((a) => a['trainee_id'] as String).toList();
    List<Map<String, dynamic>> usersData = [];
    if (traineeIds.isNotEmpty) {
      usersData = await supabase
          .from('users')
          .select('id, full_name, avatar_url')
          .inFilter('id', traineeIds);
    }
    setState(() {
      _trainees = usersData;
    });
  }

  Future<void> _addPlan() async {
    if (!_formKey.currentState!.validate() || _selectedTraineeId == null)
      return;
    setState(() => _isLoading = true);
    try {
      final user = supabase.auth.currentUser;
      if (user == null) throw Exception('User not found');
      final trainer = await supabase
          .from('trainers')
          .select('id')
          .eq('user_id', user.id)
          .maybeSingle();
      if (trainer == null) throw Exception('Trainer not found');

      // Upload files first
      await _uploadFiles();

      final planData = {
        'trainer_id': trainer['id'],
        'trainee_id': _selectedTraineeId,
        'title': _description,
        'description': _details,
        'start_date': DateTime.now().toIso8601String().split('T')[0],
        'created_at': DateTime.now().toIso8601String(),
      };

      // Insert the plan and get the ID
      final planResponse = _planType == 'nutrition'
          ? await supabase
              .from('nutrition_plans')
              .insert(planData)
              .select('id')
              .single()
          : await supabase
              .from('workout_plans')
              .insert(planData)
              .select('id')
              .single();

      final planId = planResponse['id'];

      // Insert media files
      await _insertMediaFiles(planId);

      if (mounted) {
        widget.onPlanAdded();
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(LocalizationService.isArabic
                  ? 'فشل في إضافة الخطة'
                  : 'Failed to add plan'),
              backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _pickImages() async {
    try {
      final List<XFile> images = await _imagePicker.pickMultiImage();
      if (images.isNotEmpty && mounted) {
        setState(() {
          _selectedImages.addAll(images.map((xfile) => File(xfile.path)));
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocalizationService.isArabic
                ? 'فشل في اختيار الصور'
                : 'Failed to pick images'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _pickVideos() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.video,
        allowMultiple: true,
      );

      if (result != null && mounted) {
        setState(() {
          _selectedVideos.addAll(result.paths
              .where((path) => path != null)
              .map((path) => File(path!)));
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocalizationService.isArabic
                ? 'فشل في اختيار الفيديوهات'
                : 'Failed to pick videos'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  void _removeVideo(int index) {
    setState(() {
      _selectedVideos.removeAt(index);
    });
  }

  Future<void> _uploadFiles() async {
    // Upload images
    for (File image in _selectedImages) {
      try {
        final bytes = await image.readAsBytes();
        final fileName =
            'plans/${DateTime.now().millisecondsSinceEpoch}_${image.path.split('/').last}';
        final url = await DatabaseService.uploadFile(
          bucket: 'media',
          path: fileName,
          fileBytes: bytes,
        );
        _uploadedImageUrls.add(url);
      } catch (e) {
        debugPrint('Error uploading image: $e');
      }
    }

    // Upload videos
    for (File video in _selectedVideos) {
      try {
        final bytes = await video.readAsBytes();
        final fileName =
            'plans/${DateTime.now().millisecondsSinceEpoch}_${video.path.split('/').last}';
        final url = await DatabaseService.uploadFile(
          bucket: 'media',
          path: fileName,
          fileBytes: bytes,
        );
        _uploadedVideoUrls.add(url);
      } catch (e) {
        debugPrint('Error uploading video: $e');
      }
    }
  }

  Future<void> _insertMediaFiles(String planId) async {
    final mediaTable =
        _planType == 'nutrition' ? 'nutrition_media' : 'workout_media';
    final planIdField =
        _planType == 'nutrition' ? 'nutrition_plan_id' : 'workout_plan_id';

    // Insert image media records
    for (int i = 0; i < _uploadedImageUrls.length; i++) {
      try {
        await supabase.from(mediaTable).insert({
          planIdField: planId,
          'media_type': 'image',
          'media_url': _uploadedImageUrls[i],
          'title': 'Plan Image ${i + 1}',
          'sort_order': i,
          'created_at': DateTime.now().toIso8601String(),
        });
      } catch (e) {
        debugPrint('Error inserting image media: $e');
      }
    }

    // Insert video media records
    for (int i = 0; i < _uploadedVideoUrls.length; i++) {
      try {
        await supabase.from(mediaTable).insert({
          planIdField: planId,
          'media_type': 'video',
          'media_url': _uploadedVideoUrls[i],
          'title': 'Plan Video ${i + 1}',
          'sort_order': _uploadedImageUrls.length + i,
          'created_at': DateTime.now().toIso8601String(),
        });
      } catch (e) {
        debugPrint('Error inserting video media: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: 24,
        right: 24,
        top: 24,
        bottom: MediaQuery.of(context).viewInsets.bottom + 24,
      ),
      decoration: BoxDecoration(
        color: AppTheme.cardBackground,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                LocalizationService.isArabic
                    ? 'إضافة خطة جديدة'
                    : 'Add New Plan',
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryGold,
                ),
              ),
              const SizedBox(height: 20),
              // نوع الخطة
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ChoiceChip(
                    label: Text(
                        LocalizationService.isArabic ? 'تغذية' : 'Nutrition'),
                    selected: _planType == 'nutrition',
                    onSelected: (selected) {
                      if (selected) setState(() => _planType = 'nutrition');
                    },
                    selectedColor: const Color(0xFF2196F3),
                  ),
                  const SizedBox(width: 12),
                  ChoiceChip(
                    label: Text(
                        LocalizationService.isArabic ? 'تمارين' : 'Workout'),
                    selected: _planType == 'workout',
                    onSelected: (selected) {
                      if (selected) setState(() => _planType = 'workout');
                    },
                    selectedColor: const Color(0xFFFF9800),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              // اختيار المتدرب
              DropdownButtonFormField<String>(
                value: _selectedTraineeId,
                items: _trainees.map((t) {
                  return DropdownMenuItem<String>(
                    value: t['id'],
                    child: Row(
                      children: [
                        CircleAvatar(
                          backgroundImage:
                              t['avatar_url'] != null && t['avatar_url'] != ''
                                  ? NetworkImage(t['avatar_url'])
                                  : null,
                          child:
                              (t['avatar_url'] == null || t['avatar_url'] == '')
                                  ? const Icon(Icons.person)
                                  : null,
                        ),
                        const SizedBox(width: 8),
                        Text(t['full_name'] ?? ''),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (val) => setState(() => _selectedTraineeId = val),
                decoration: InputDecoration(
                  labelText:
                      LocalizationService.isArabic ? 'المتدرب' : 'Trainee',
                  prefixIcon: Icon(Icons.person, color: AppTheme.primaryGold),
                ),
                validator: (val) => val == null
                    ? (LocalizationService.isArabic
                        ? 'يرجى اختيار متدرب'
                        : 'Please select a trainee')
                    : null,
              ),
              const SizedBox(height: 16),
              // وصف الخطة
              TextFormField(
                maxLines: 2,
                decoration: InputDecoration(
                  labelText: LocalizationService.isArabic
                      ? 'وصف الخطة'
                      : 'Plan Description',
                  prefixIcon:
                      Icon(Icons.description, color: AppTheme.primaryGold),
                ),
                onChanged: (val) => _description = val,
                validator: (val) => val == null || val.isEmpty
                    ? (LocalizationService.isArabic
                        ? 'يرجى إدخال وصف'
                        : 'Please enter a description')
                    : null,
              ),
              const SizedBox(height: 16),
              // تفاصيل الخطة
              TextFormField(
                maxLines: 4,
                decoration: InputDecoration(
                  labelText: LocalizationService.isArabic
                      ? 'تفاصيل الخطة'
                      : 'Plan Details',
                  prefixIcon: Icon(Icons.list_alt, color: AppTheme.primaryGold),
                ),
                onChanged: (val) => _details = val,
                validator: (val) => val == null || val.isEmpty
                    ? (LocalizationService.isArabic
                        ? 'يرجى إدخال التفاصيل'
                        : 'Please enter details')
                    : null,
              ),
              const SizedBox(height: 24),

              // Media Upload Section
              Container(
                decoration: BoxDecoration(
                  border: Border.all(
                      color: AppTheme.primaryGold.withValues(alpha: 0.3)),
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      LocalizationService.isArabic
                          ? 'إضافة صور وفيديوهات'
                          : 'Add Images & Videos',
                      style: const TextStyle(
                        color: AppTheme.primaryGold,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 12),

                    // Image Upload Button
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _pickImages,
                            icon: const Icon(Icons.image, color: Colors.white),
                            label: Text(
                              LocalizationService.isArabic
                                  ? 'اختيار صور'
                                  : 'Pick Images',
                              style: const TextStyle(color: Colors.white),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue[600],
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _pickVideos,
                            icon:
                                const Icon(Icons.videocam, color: Colors.white),
                            label: Text(
                              LocalizationService.isArabic
                                  ? 'اختيار فيديوهات'
                                  : 'Pick Videos',
                              style: const TextStyle(color: Colors.white),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.purple[600],
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Selected Images Display
                    if (_selectedImages.isNotEmpty) ...[
                      const SizedBox(height: 12),
                      Text(
                        LocalizationService.isArabic
                            ? 'الصور المختارة:'
                            : 'Selected Images:',
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      SizedBox(
                        height: 80,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: _selectedImages.length,
                          itemBuilder: (context, index) {
                            return Container(
                              margin: const EdgeInsets.only(right: 8),
                              child: Stack(
                                children: [
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: Image.file(
                                      _selectedImages[index],
                                      width: 80,
                                      height: 80,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                  Positioned(
                                    top: 4,
                                    right: 4,
                                    child: GestureDetector(
                                      onTap: () => _removeImage(index),
                                      child: Container(
                                        padding: const EdgeInsets.all(2),
                                        decoration: const BoxDecoration(
                                          color: Colors.red,
                                          shape: BoxShape.circle,
                                        ),
                                        child: const Icon(
                                          Icons.close,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ],

                    // Selected Videos Display
                    if (_selectedVideos.isNotEmpty) ...[
                      const SizedBox(height: 12),
                      Text(
                        LocalizationService.isArabic
                            ? 'الفيديوهات المختارة:'
                            : 'Selected Videos:',
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Column(
                        children: _selectedVideos.asMap().entries.map((entry) {
                          int index = entry.key;
                          File video = entry.value;
                          String fileName = video.path.split('/').last;

                          return Container(
                            margin: const EdgeInsets.only(bottom: 8),
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: AppTheme.cardBackground,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.grey[600]!),
                            ),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.video_file,
                                  color: Colors.purple,
                                  size: 24,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    fileName,
                                    style: const TextStyle(
                                      color: AppTheme.textPrimary,
                                      fontSize: 14,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                GestureDetector(
                                  onTap: () => _removeVideo(index),
                                  child: Container(
                                    padding: const EdgeInsets.all(4),
                                    decoration: const BoxDecoration(
                                      color: Colors.red,
                                      shape: BoxShape.circle,
                                    ),
                                    child: const Icon(
                                      Icons.close,
                                      color: Colors.white,
                                      size: 16,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                      ),
                    ],
                  ],
                ),
              ),
              const SizedBox(height: 24),

              ElevatedButton(
                onPressed: _isLoading ? null : _addPlan,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryGold,
                  foregroundColor: Colors.black,
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isLoading
                    ? const CircularProgressIndicator(color: Colors.black)
                    : Text(
                        LocalizationService.isArabic
                            ? 'حفظ الخطة'
                            : 'Save Plan',
                        style: const TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 16),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
