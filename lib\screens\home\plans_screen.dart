import 'package:flutter/material.dart';
import '../../services/localization_service.dart';
import '../../main.dart';
import '../../theme/app_theme.dart';
import '../../widgets/custom_app_bar.dart';

class PlansScreen extends StatefulWidget {
  const PlansScreen({super.key});

  @override
  State<PlansScreen> createState() => _PlansScreenState();
}

class _PlansScreenState extends State<PlansScreen> {
  List<Map<String, dynamic>> _plans = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPlans();
  }

  Future<void> _loadPlans() async {
    try {
      final user = supabase.auth.currentUser;
      if (user == null) return;
      final trainer = await supabase
          .from('trainers')
          .select('id')
          .eq('user_id', user.id)
          .maybeSingle();
      if (trainer == null) return;
      // جلب خطط التغذية وخطط التمارين معًا
      final nutritionPlans = await supabase
          .from('nutrition_plans')
          .select('*')
          .eq('trainer_id', trainer['id']);
      final workoutPlans = await supabase
          .from('workout_plans')
          .select('*')
          .eq('trainer_id', trainer['id']);
      final plans = [
        ...nutritionPlans.map((p) => {...p, 'plan_type': 'nutrition'}),
        ...workoutPlans.map((p) => {...p, 'plan_type': 'workout'}),
      ];
      // جلب بيانات المتدربين المرتبطين بكل خطة
      final traineeIds = plans
          .map((p) => p['trainee_id'] as String?)
          .where((id) => id != null)
          .toSet()
          .toList();
      List<Map<String, dynamic>> usersData = [];
      if (traineeIds.isNotEmpty) {
        usersData = await supabase
            .from('users')
            .select('id, full_name, avatar_url')
            .inFilter('id', traineeIds);
      }
      final detailedPlans = plans.map((p) {
        final userData = usersData.firstWhere(
          (u) => u['id'] == p['trainee_id'],
          orElse: () => {'full_name': '', 'avatar_url': ''},
        );
        return {
          ...p,
          'trainee_name': userData['full_name'],
          'trainee_avatar': userData['avatar_url'],
        };
      }).toList();
      setState(() {
        _plans = List<Map<String, dynamic>>.from(detailedPlans);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showAddPlanDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return AddPlanDialog(onPlanAdded: _loadPlans);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: LocalizationService.isArabic ? 'الخطط' : 'Plans',
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Color(0xFFFFD700)))
          : _plans.isEmpty
              ? Center(
                  child: Text(
                    LocalizationService.isArabic
                        ? 'لا توجد خطط'
                        : 'No plans found',
                    style: const TextStyle(fontSize: 20, color: Colors.grey),
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _plans.length,
                  itemBuilder: (context, index) {
                    final plan = _plans[index];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF232526), Color(0xFF414345)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(18),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.12),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: plan['plan_type'] == 'nutrition'
                              ? const Color(0xFF2196F3)
                              : const Color(0xFFFF9800),
                          backgroundImage: plan['trainee_avatar'] != null &&
                                  plan['trainee_avatar'] != ''
                              ? NetworkImage(plan['trainee_avatar'])
                              : null,
                          child: (plan['trainee_avatar'] == null ||
                                  plan['trainee_avatar'] == '')
                              ? const Icon(Icons.person, color: Colors.white)
                              : null,
                        ),
                        title: Text(
                          plan['trainee_name'] != ''
                              ? plan['trainee_name']
                              : (LocalizationService.isArabic
                                  ? 'خطة رقم: ${plan['id']}'
                                  : 'Plan ID: ${plan['id']}'),
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              LocalizationService.isArabic
                                  ? 'النوع: ${plan['plan_type'] == 'nutrition' ? 'تغذية' : 'تمارين'}'
                                  : 'Type: ${plan['plan_type'] == 'nutrition' ? 'Nutrition' : 'Workout'}',
                              style: const TextStyle(
                                  color: Colors.white70, fontSize: 13),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              LocalizationService.isArabic
                                  ? 'الوصف: ${plan['description'] ?? ''}'
                                  : 'Description: ${plan['description'] ?? ''}',
                              style: const TextStyle(color: Colors.white70),
                            ),
                          ],
                        ),
                        trailing: const Icon(Icons.arrow_forward_ios,
                            color: Colors.white54, size: 18),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 12),
                      ),
                    );
                  },
                ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showAddPlanDialog,
        backgroundColor: const Color(0xFFFFD700),
        icon: const Icon(Icons.add, color: Colors.black),
        label: Text(
          LocalizationService.isArabic ? 'إضافة خطة' : 'Add Plan',
          style:
              const TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }
}

class AddPlanDialog extends StatefulWidget {
  final VoidCallback onPlanAdded;
  const AddPlanDialog({super.key, required this.onPlanAdded});

  @override
  State<AddPlanDialog> createState() => _AddPlanDialogState();
}

class _AddPlanDialogState extends State<AddPlanDialog> {
  final _formKey = GlobalKey<FormState>();
  String _planType = 'nutrition';
  String? _selectedTraineeId;
  String _description = '';
  String _details = '';
  bool _isLoading = false;
  List<Map<String, dynamic>> _trainees = [];

  @override
  void initState() {
    super.initState();
    _fetchTrainees();
  }

  Future<void> _fetchTrainees() async {
    final user = supabase.auth.currentUser;
    if (user == null) return;
    final trainer = await supabase
        .from('trainers')
        .select('id')
        .eq('user_id', user.id)
        .maybeSingle();
    if (trainer == null) return;
    final assignments = await supabase
        .from('trainer_assignments')
        .select('trainee_id')
        .eq('trainer_id', trainer['id']);
    final traineeIds =
        assignments.map((a) => a['trainee_id'] as String).toList();
    List<Map<String, dynamic>> usersData = [];
    if (traineeIds.isNotEmpty) {
      usersData = await supabase
          .from('users')
          .select('id, full_name, avatar_url')
          .inFilter('id', traineeIds);
    }
    setState(() {
      _trainees = usersData;
    });
  }

  Future<void> _addPlan() async {
    if (!_formKey.currentState!.validate() || _selectedTraineeId == null)
      return;
    setState(() => _isLoading = true);
    try {
      final user = supabase.auth.currentUser;
      if (user == null) throw Exception('User not found');
      final trainer = await supabase
          .from('trainers')
          .select('id')
          .eq('user_id', user.id)
          .maybeSingle();
      if (trainer == null) throw Exception('Trainer not found');
      final planData = {
        'trainer_id': trainer['id'],
        'trainee_id': _selectedTraineeId,
        'description': _description,
        'details': _details,
        'created_at': DateTime.now().toIso8601String(),
      };
      if (_planType == 'nutrition') {
        await supabase.from('nutrition_plans').insert(planData);
      } else {
        await supabase.from('workout_plans').insert(planData);
      }
      widget.onPlanAdded();
      Navigator.of(context).pop();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(LocalizationService.isArabic
                ? 'فشل في إضافة الخطة'
                : 'Failed to add plan'),
            backgroundColor: Colors.red),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: 24,
        right: 24,
        top: 24,
        bottom: MediaQuery.of(context).viewInsets.bottom + 24,
      ),
      decoration: BoxDecoration(
        color: AppTheme.cardBackground,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                LocalizationService.isArabic
                    ? 'إضافة خطة جديدة'
                    : 'Add New Plan',
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryGold,
                ),
              ),
              const SizedBox(height: 20),
              // نوع الخطة
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ChoiceChip(
                    label: Text(
                        LocalizationService.isArabic ? 'تغذية' : 'Nutrition'),
                    selected: _planType == 'nutrition',
                    onSelected: (selected) {
                      if (selected) setState(() => _planType = 'nutrition');
                    },
                    selectedColor: const Color(0xFF2196F3),
                  ),
                  const SizedBox(width: 12),
                  ChoiceChip(
                    label: Text(
                        LocalizationService.isArabic ? 'تمارين' : 'Workout'),
                    selected: _planType == 'workout',
                    onSelected: (selected) {
                      if (selected) setState(() => _planType = 'workout');
                    },
                    selectedColor: const Color(0xFFFF9800),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              // اختيار المتدرب
              DropdownButtonFormField<String>(
                value: _selectedTraineeId,
                items: _trainees.map((t) {
                  return DropdownMenuItem<String>(
                    value: t['id'],
                    child: Row(
                      children: [
                        CircleAvatar(
                          backgroundImage:
                              t['avatar_url'] != null && t['avatar_url'] != ''
                                  ? NetworkImage(t['avatar_url'])
                                  : null,
                          child:
                              (t['avatar_url'] == null || t['avatar_url'] == '')
                                  ? const Icon(Icons.person)
                                  : null,
                        ),
                        const SizedBox(width: 8),
                        Text(t['full_name'] ?? ''),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (val) => setState(() => _selectedTraineeId = val),
                decoration: InputDecoration(
                  labelText:
                      LocalizationService.isArabic ? 'المتدرب' : 'Trainee',
                  prefixIcon: Icon(Icons.person, color: AppTheme.primaryGold),
                ),
                validator: (val) => val == null
                    ? (LocalizationService.isArabic
                        ? 'يرجى اختيار متدرب'
                        : 'Please select a trainee')
                    : null,
              ),
              const SizedBox(height: 16),
              // وصف الخطة
              TextFormField(
                maxLines: 2,
                decoration: InputDecoration(
                  labelText: LocalizationService.isArabic
                      ? 'وصف الخطة'
                      : 'Plan Description',
                  prefixIcon:
                      Icon(Icons.description, color: AppTheme.primaryGold),
                ),
                onChanged: (val) => _description = val,
                validator: (val) => val == null || val.isEmpty
                    ? (LocalizationService.isArabic
                        ? 'يرجى إدخال وصف'
                        : 'Please enter a description')
                    : null,
              ),
              const SizedBox(height: 16),
              // تفاصيل الخطة
              TextFormField(
                maxLines: 4,
                decoration: InputDecoration(
                  labelText: LocalizationService.isArabic
                      ? 'تفاصيل الخطة'
                      : 'Plan Details',
                  prefixIcon: Icon(Icons.list_alt, color: AppTheme.primaryGold),
                ),
                onChanged: (val) => _details = val,
                validator: (val) => val == null || val.isEmpty
                    ? (LocalizationService.isArabic
                        ? 'يرجى إدخال التفاصيل'
                        : 'Please enter details')
                    : null,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _isLoading ? null : _addPlan,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryGold,
                  foregroundColor: Colors.black,
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isLoading
                    ? const CircularProgressIndicator(color: Colors.black)
                    : Text(
                        LocalizationService.isArabic
                            ? 'حفظ الخطة'
                            : 'Save Plan',
                        style: const TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 16),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
