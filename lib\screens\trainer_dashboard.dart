import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import '../services/trainer_service.dart';
import '../models/trainer_model.dart';
import '../models/session_model.dart';
import '../providers/auth_provider.dart';

class TrainerDashboard extends StatefulWidget {
  const TrainerDashboard({Key? key}) : super(key: key);

  @override
  State<TrainerDashboard> createState() => _TrainerDashboardState();
}

class _TrainerDashboardState extends State<TrainerDashboard> {
  Map<String, dynamic>? dashboardStats;
  List<SessionModel> todaySessions = [];
  List<SessionModel> upcomingSessions = [];
  bool isLoading = true;
  String? trainerId;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final userId = authProvider.user?.id;

      if (userId != null) {
        final trainer = await TrainerService.getTrainerProfile(userId);
        if (trainer != null) {
          trainerId = trainer.id;

          final stats = await TrainerService.getDashboardStats(trainer.id);
          final today = await TrainerService.getTodaySchedule(trainer.id);
          final upcoming = await TrainerService.getUpcomingWeek(trainer.id);

          setState(() {
            dashboardStats = stats;
            todaySessions = today;
            upcomingSessions = upcoming;
            isLoading = false;
          });
        }
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في تحميل البيانات: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة التحكم'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDashboardData,
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadDashboardData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildWelcomeCard(),
              const SizedBox(height: 16),
              _buildStatsCards(),
              const SizedBox(height: 16),
              _buildTodaySchedule(),
              const SizedBox(height: 16),
              _buildUpcomingSessions(),
              const SizedBox(height: 16),
              _buildQuickActions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Card(
      elevation: 4,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [Colors.blue[600]!, Colors.blue[400]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'مرحباً بك',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'إليك ملخص أدائك اليوم',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white.withOpacity(0.9),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCards() {
    if (dashboardStats == null) return const SizedBox();

    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.5,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      children: [
        _buildStatCard(
          'العملاء النشطين',
          '${dashboardStats!['total_clients']}',
          Icons.people,
          Colors.green,
        ),
        _buildStatCard(
          'جلسات اليوم',
          '${dashboardStats!['today_sessions']}',
          Icons.today,
          Colors.blue,
        ),
        _buildStatCard(
          'إيرادات الشهر',
          '${dashboardStats!['month_revenue'].toStringAsFixed(0)} ر.س',
          Icons.attach_money,
          Colors.orange,
        ),
        _buildStatCard(
          'معدل الإكمال',
          '${dashboardStats!['completion_rate'].toStringAsFixed(1)}%',
          Icons.check_circle,
          Colors.purple,
        ),
      ],
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTodaySchedule() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'جدول اليوم',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(context, '/sessions');
                  },
                  child: Text('عرض الكل'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (todaySessions.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Text(
                    'لا توجد جلسات مجدولة اليوم',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ),
              )
            else
              ...todaySessions
                  .take(3)
                  .map((session) => _buildSessionTile(session)),
          ],
        ),
      ),
    );
  }

  Widget _buildUpcomingSessions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الجلسات القادمة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(context, '/sessions');
                  },
                  child: Text('عرض الكل'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (upcomingSessions.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Text(
                    'لا توجد جلسات قادمة',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ),
              )
            else
              ...upcomingSessions
                  .take(3)
                  .map((session) => _buildSessionTile(session)),
          ],
        ),
      ),
    );
  }

  Widget _buildSessionTile(SessionModel session) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: _getStatusColor(session.status),
        child: Icon(
          _getStatusIcon(session.status),
          color: Colors.white,
          size: 20,
        ),
      ),
      title: Text(
        session.sessionType ?? 'جلسة تدريب',
        style: const TextStyle(fontWeight: FontWeight.w500),
      ),
      subtitle: Text(
        '${_formatTime(session.scheduledAt)} - ${session.durationMinutes} دقيقة',
      ),
      trailing: Text(
        session.statusDisplayName,
        style: TextStyle(
          color: _getStatusColor(session.status),
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: () {
        // Navigate to session details
      },
    );
  }

  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إجراءات سريعة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildQuickActionButton(
                  'جلسة جديدة',
                  Icons.add_circle,
                  Colors.blue,
                  () {
                    Navigator.pushNamed(context, '/sessions');
                  },
                ),
                _buildQuickActionButton(
                  'عملائي',
                  Icons.people,
                  Colors.green,
                  () {
                    Navigator.pushNamed(context, '/clients');
                  },
                ),
                _buildQuickActionButton(
                  'التقارير',
                  Icons.analytics,
                  Colors.orange,
                  () {
                    Navigator.pushNamed(context, '/reports');
                  },
                ),
                _buildQuickActionButton(
                  'الإعدادات',
                  Icons.settings,
                  Colors.grey,
                  () {
                    Navigator.pushNamed(context, '/settings');
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'scheduled':
        return Colors.blue;
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      case 'no_show':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'scheduled':
        return Icons.schedule;
      case 'completed':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      case 'no_show':
        return Icons.person_off;
      default:
        return Icons.help;
    }
  }

  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
