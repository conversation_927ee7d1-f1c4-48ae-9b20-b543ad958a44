import '../models/trainer_model.dart';
import '../models/session_model.dart';
import '../models/subscription_model.dart';
import '../models/analytics_model.dart';
import 'database_service.dart';

class TrainerService {
  static const String _trainersTable = 'trainers';
  static const String _sessionsTable = 'sessions';
  static const String _subscriptionsTable = 'subscriptions';
  static const String _paymentsTable = 'payments';

  // Trainer Profile Management
  static Future<TrainerModel?> getTrainerProfile(String userId) async {
    final result = await DatabaseService.selectSingle(
      table: _trainersTable,
      filters: {'user_id': userId},
      select: '''
        *,
        users!inner(
          id,
          email,
          full_name,
          phone,
          avatar_url,
          is_active
        )
      ''',
    );

    return result != null ? TrainerModel.fromJson(result) : null;
  }

  static Future<TrainerModel> updateTrainerProfile(TrainerModel trainer) async {
    final data = trainer.toJson();
    data['updated_at'] = DateTime.now().toIso8601String();

    final result = await DatabaseService.update(
      table: _trainersTable,
      data: data,
      filters: {'id': trainer.id},
    );

    return TrainerModel.fromJson(result);
  }

  static Future<TrainerModel> updateAvailability(String trainerId, bool isAvailable) async {
    final result = await DatabaseService.update(
      table: _trainersTable,
      data: {
        'is_available': isAvailable,
        'updated_at': DateTime.now().toIso8601String(),
      },
      filters: {'id': trainerId},
    );

    return TrainerModel.fromJson(result);
  }

  // Dashboard Statistics
  static Future<Map<String, dynamic>> getDashboardStats(String trainerId) async {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final startOfDay = DateTime(now.year, now.month, now.day);

    // Get sessions data
    final allSessions = await DatabaseService.select(
      table: _sessionsTable,
      filters: {'trainer_id': trainerId},
    );

    final thisMonthSessions = allSessions.where((s) => 
      DateTime.parse(s['scheduled_at']).isAfter(startOfMonth)).toList();
    
    final thisWeekSessions = allSessions.where((s) => 
      DateTime.parse(s['scheduled_at']).isAfter(startOfWeek)).toList();
    
    final todaySessions = allSessions.where((s) {
      final sessionDate = DateTime.parse(s['scheduled_at']);
      return sessionDate.year == now.year && 
             sessionDate.month == now.month && 
             sessionDate.day == now.day;
    }).toList();

    // Get active subscriptions
    final activeSubscriptions = await DatabaseService.select(
      table: _subscriptionsTable,
      filters: {'trainer_id': trainerId, 'status': 'active'},
    );

    // Get revenue data
    final payments = await DatabaseService.select(
      table: _paymentsTable,
      select: '''
        *,
        subscriptions!inner(trainer_id)
      ''',
      filters: {'status': 'completed'},
    );

    final trainerPayments = payments.where((p) => 
      p['subscriptions']['trainer_id'] == trainerId).toList();

    final thisMonthRevenue = trainerPayments.where((p) => 
      DateTime.parse(p['paid_at']).isAfter(startOfMonth))
      .fold(0.0, (sum, p) => sum + (p['amount'] as num).toDouble());

    // Calculate completion rate
    final completedSessions = allSessions.where((s) => s['status'] == 'completed').length;
    final totalSessions = allSessions.length;
    final completionRate = totalSessions > 0 ? (completedSessions / totalSessions) * 100 : 0.0;

    // Calculate average rating
    final ratedSessions = allSessions.where((s) => s['rating'] != null).toList();
    final averageRating = ratedSessions.isNotEmpty 
        ? ratedSessions.fold(0.0, (sum, s) => sum + (s['rating'] as num).toDouble()) / ratedSessions.length
        : 0.0;

    return {
      'total_clients': activeSubscriptions.length,
      'total_sessions': totalSessions,
      'completed_sessions': completedSessions,
      'today_sessions': todaySessions.length,
      'week_sessions': thisWeekSessions.length,
      'month_sessions': thisMonthSessions.length,
      'month_revenue': thisMonthRevenue,
      'completion_rate': completionRate,
      'average_rating': averageRating,
      'active_subscriptions': activeSubscriptions.length,
    };
  }

  // Session Management for Trainer
  static Future<List<SessionModel>> getTrainerSessions(
    String trainerId, {
    String? status,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    final filters = {'trainer_id': trainerId};
    if (status != null) filters['status'] = status;

    var sessions = await DatabaseService.select(
      table: _sessionsTable,
      filters: filters,
      orderBy: 'scheduled_at',
      ascending: false,
      limit: limit,
      select: '''
        *,
        users!sessions_trainee_id_fkey(
          id,
          full_name,
          phone,
          avatar_url
        )
      ''',
    );

    // Filter by date range if provided
    if (startDate != null || endDate != null) {
      sessions = sessions.where((session) {
        final scheduledAt = DateTime.parse(session['scheduled_at']);
        if (startDate != null && scheduledAt.isBefore(startDate)) return false;
        if (endDate != null && scheduledAt.isAfter(endDate)) return false;
        return true;
      }).toList();
    }

    return sessions.map((json) => SessionModel.fromJson(json)).toList();
  }

  static Future<List<SessionModel>> getTodaySchedule(String trainerId) async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    return getTrainerSessions(
      trainerId,
      startDate: startOfDay,
      endDate: endOfDay,
    );
  }

  static Future<List<SessionModel>> getUpcomingWeek(String trainerId) async {
    final now = DateTime.now();
    final endOfWeek = now.add(const Duration(days: 7));

    return getTrainerSessions(
      trainerId,
      status: 'scheduled',
      startDate: now,
      endDate: endOfWeek,
    );
  }

  // Client Management for Trainer
  static Future<List<Map<String, dynamic>>> getTrainerClients(String trainerId) async {
    final subscriptions = await DatabaseService.select(
      table: _subscriptionsTable,
      filters: {'trainer_id': trainerId, 'status': 'active'},
      select: '''
        *,
        users!subscriptions_trainee_id_fkey(
          id,
          full_name,
          email,
          phone,
          avatar_url
        )
      ''',
    );

    final clients = <Map<String, dynamic>>[];
    
    for (final sub in subscriptions) {
      final clientSessions = await DatabaseService.select(
        table: _sessionsTable,
        filters: {
          'trainer_id': trainerId,
          'trainee_id': sub['trainee_id'],
        },
      );

      final completedSessions = clientSessions.where((s) => s['status'] == 'completed').length;
      final totalSessions = clientSessions.length;
      final lastSession = clientSessions.isNotEmpty 
          ? clientSessions.reduce((a, b) => 
              DateTime.parse(a['scheduled_at']).isAfter(DateTime.parse(b['scheduled_at'])) ? a : b)
          : null;

      clients.add({
        'user': sub['users'],
        'subscription': sub,
        'total_sessions': totalSessions,
        'completed_sessions': completedSessions,
        'last_session': lastSession,
        'join_date': sub['start_date'],
      });
    }

    return clients;
  }

  // Financial Analytics for Trainer
  static Future<Map<String, dynamic>> getFinancialAnalytics(
    String trainerId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final payments = await DatabaseService.select(
      table: _paymentsTable,
      select: '''
        *,
        subscriptions!inner(trainer_id, plan_type)
      ''',
      filters: {'status': 'completed'},
    );

    var trainerPayments = payments.where((p) => 
      p['subscriptions']['trainer_id'] == trainerId).toList();

    // Filter by date range if provided
    if (startDate != null || endDate != null) {
      trainerPayments = trainerPayments.where((payment) {
        final paidAt = DateTime.parse(payment['paid_at']);
        if (startDate != null && paidAt.isBefore(startDate)) return false;
        if (endDate != null && paidAt.isAfter(endDate)) return false;
        return true;
      }).toList();
    }

    final totalRevenue = trainerPayments.fold(0.0, (sum, p) => sum + (p['amount'] as num).toDouble());
    final totalPayments = trainerPayments.length;
    final averagePayment = totalPayments > 0 ? totalRevenue / totalPayments : 0.0;

    // Group by month for chart data
    final monthlyRevenue = <String, double>{};
    for (final payment in trainerPayments) {
      final date = DateTime.parse(payment['paid_at']);
      final monthKey = '${date.year}-${date.month.toString().padLeft(2, '0')}';
      monthlyRevenue[monthKey] = (monthlyRevenue[monthKey] ?? 0) + (payment['amount'] as num).toDouble();
    }

    return {
      'total_revenue': totalRevenue,
      'total_payments': totalPayments,
      'average_payment': averagePayment,
      'monthly_revenue': monthlyRevenue,
    };
  }

  // Performance Analytics
  static Future<Map<String, dynamic>> getPerformanceAnalytics(String trainerId) async {
    final sessions = await DatabaseService.select(
      table: _sessionsTable,
      filters: {'trainer_id': trainerId},
    );

    final completedSessions = sessions.where((s) => s['status'] == 'completed').toList();
    final cancelledSessions = sessions.where((s) => s['status'] == 'cancelled').toList();
    final noShowSessions = sessions.where((s) => s['status'] == 'no_show').toList();
    final ratedSessions = sessions.where((s) => s['rating'] != null).toList();

    final totalSessions = sessions.length;
    final completionRate = totalSessions > 0 ? (completedSessions.length / totalSessions) * 100 : 0.0;
    final cancellationRate = totalSessions > 0 ? (cancelledSessions.length / totalSessions) * 100 : 0.0;
    final noShowRate = totalSessions > 0 ? (noShowSessions.length / totalSessions) * 100 : 0.0;
    
    final averageRating = ratedSessions.isNotEmpty 
        ? ratedSessions.fold(0.0, (sum, s) => sum + (s['rating'] as num).toDouble()) / ratedSessions.length
        : 0.0;

    // Rating distribution
    final ratingDistribution = <int, int>{};
    for (int i = 1; i <= 5; i++) {
      ratingDistribution[i] = ratedSessions.where((s) => (s['rating'] as num).round() == i).length;
    }

    return {
      'total_sessions': totalSessions,
      'completed_sessions': completedSessions.length,
      'cancelled_sessions': cancelledSessions.length,
      'no_show_sessions': noShowSessions.length,
      'completion_rate': completionRate,
      'cancellation_rate': cancellationRate,
      'no_show_rate': noShowRate,
      'average_rating': averageRating,
      'total_ratings': ratedSessions.length,
      'rating_distribution': ratingDistribution,
    };
  }

  // Schedule Management
  static Future<bool> isTimeSlotAvailable(
    String trainerId,
    DateTime startTime,
    int durationMinutes, {
    String? excludeSessionId,
  }) async {
    final endTime = startTime.add(Duration(minutes: durationMinutes));
    
    final sessions = await DatabaseService.select(
      table: _sessionsTable,
      filters: {
        'trainer_id': trainerId,
        'status': 'scheduled',
      },
    );

    for (final sessionData in sessions) {
      if (excludeSessionId != null && sessionData['id'] == excludeSessionId) continue;
      
      final sessionStart = DateTime.parse(sessionData['scheduled_at']);
      final sessionEnd = sessionStart.add(Duration(minutes: sessionData['duration_minutes']));
      
      // Check for overlap
      if (startTime.isBefore(sessionEnd) && endTime.isAfter(sessionStart)) {
        return false;
      }
    }

    return true;
  }

  static Future<List<DateTime>> getAvailableTimeSlots(
    String trainerId,
    DateTime date,
    int durationMinutes,
  ) async {
    final availableSlots = <DateTime>[];
    final startOfDay = DateTime(date.year, date.month, date.day, 6, 0); // Start at 6 AM
    final endOfDay = DateTime(date.year, date.month, date.day, 22, 0); // End at 10 PM

    var currentTime = startOfDay;
    while (currentTime.isBefore(endOfDay)) {
      final isAvailable = await isTimeSlotAvailable(trainerId, currentTime, durationMinutes);
      if (isAvailable) {
        availableSlots.add(currentTime);
      }
      currentTime = currentTime.add(const Duration(minutes: 30)); // 30-minute intervals
    }

    return availableSlots;
  }
}
