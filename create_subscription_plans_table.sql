-- Create subscription_plans table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.subscription_plans (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL CHECK (name IN ('Basic', 'Standard', 'Premium', 'VIP', 'Custom')),
    description TEXT,
    duration_days INTEGER NOT NULL CHECK (duration_days > 0),
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    features TEXT[] DEFAULT '{}',
    session_count INTEGER DEFAULT 0, -- 0 means unlimited
    nutrition_plan_included BOOLEAN DEFAULT false,
    workout_plan_included BOOLEAN DEFAULT true,
    chat_support BOOLEAN DEFAULT true,
    video_calls BOOLEAN DEFAULT false,
    progress_tracking BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(trainer_id, name)
);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_subscription_plans_updated_at ON public.subscription_plans;
CREATE TRIGGER update_subscription_plans_updated_at
    BEFORE UPDATE ON public.subscription_plans
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data for testing (only if no plans exist for any trainer)
DO $$
DECLARE
    trainer_record RECORD;
BEGIN
    -- Get the first trainer for testing
    SELECT id INTO trainer_record FROM public.trainers LIMIT 1;
    
    IF trainer_record.id IS NOT NULL THEN
        -- Check if this trainer already has plans
        IF NOT EXISTS (SELECT 1 FROM public.subscription_plans WHERE trainer_id = trainer_record.id) THEN
            -- Insert sample plans
            INSERT INTO public.subscription_plans (trainer_id, name, description, duration_days, price, features, session_count, nutrition_plan_included, workout_plan_included, chat_support, video_calls, progress_tracking, sort_order) VALUES
            (trainer_record.id, 'Basic', 'خطة أساسية للمبتدئين', 30, 299.00, ARRAY['خطة تمارين أساسية', 'دعم المحادثة'], 8, false, true, true, false, true, 1),
            (trainer_record.id, 'Standard', 'خطة متوسطة مع مميزات إضافية', 30, 499.00, ARRAY['خطة تمارين متقدمة', 'خطة تغذية', 'دعم المحادثة', 'متابعة أسبوعية'], 12, true, true, true, false, true, 2),
            (trainer_record.id, 'Premium', 'خطة شاملة مع جميع المميزات', 30, 799.00, ARRAY['خطة تمارين مخصصة', 'خطة تغذية شاملة', 'دعم المحادثة', 'مكالمات فيديو', 'متابعة يومية'], 0, true, true, true, true, true, 3);
            
            RAISE NOTICE 'Sample subscription plans created for trainer: %', trainer_record.id;
        ELSE
            RAISE NOTICE 'Trainer already has subscription plans';
        END IF;
    ELSE
        RAISE NOTICE 'No trainers found in database';
    END IF;
END $$;
