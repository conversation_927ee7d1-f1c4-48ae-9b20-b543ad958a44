import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_theme.dart';
import '../services/localization_service.dart';

enum NavBarBackgroundType {
  solid,
  gradient,
  glass,
  premium,
  none,
}

class ModernNavigationBar extends StatefulWidget {
  final int selectedIndex;
  final ValueChanged<int> onTap;
  final List<NavigationItem> items;
  final double height;
  final double iconSize;
  final double selectedIconSize;
  final double paddingV;
  final double labelFontSize;
  final double selectedLabelFontSize;
  final NavBarBackgroundType backgroundType;
  final Color? backgroundColor;
  final Gradient? gradient;
  final bool showLabels;
  final bool showSelectedLabels;
  final Duration animationDuration;
  final Curve animationCurve;

  const ModernNavigationBar({
    super.key,
    required this.selectedIndex,
    required this.onTap,
    required this.items,
    this.height = 70,
    this.iconSize = 24,
    this.selectedIconSize = 28,
    this.paddingV = 8,
    this.labelFontSize = 12,
    this.selectedLabelFontSize = 14,
    this.backgroundType = NavBarBackgroundType.solid,
    this.backgroundColor,
    this.gradient,
    this.showLabels = true,
    this.showSelectedLabels = true,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
  });

  @override
  State<ModernNavigationBar> createState() => _ModernNavigationBarState();
}

class _ModernNavigationBarState extends State<ModernNavigationBar>
    with TickerProviderStateMixin {
  late List<AnimationController> _animationControllers;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<double>> _fadeAnimations;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationControllers = List.generate(
      widget.items.length,
      (index) => AnimationController(
        duration: widget.animationDuration,
        vsync: this,
      ),
    );

    _scaleAnimations = _animationControllers.map((controller) {
      return Tween<double>(begin: 1.0, end: 1.2).animate(
        CurvedAnimation(parent: controller, curve: widget.animationCurve),
      );
    }).toList();

    _fadeAnimations = _animationControllers.map((controller) {
      return Tween<double>(begin: 0.6, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: widget.animationCurve),
      );
    }).toList();

    // Animate the initially selected item
    if (widget.selectedIndex < _animationControllers.length) {
      _animationControllers[widget.selectedIndex].forward();
    }
  }

  @override
  void didUpdateWidget(ModernNavigationBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedIndex != widget.selectedIndex) {
      // Reset previous selection
      if (oldWidget.selectedIndex < _animationControllers.length) {
        _animationControllers[oldWidget.selectedIndex].reverse();
      }
      // Animate new selection
      if (widget.selectedIndex < _animationControllers.length) {
        _animationControllers[widget.selectedIndex].forward();
      }
    }
  }

  @override
  void dispose() {
    for (var controller in _animationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      decoration: _buildBackgroundDecoration(),
      child: SafeArea(
        top: false,
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: widget.paddingV),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: List.generate(
              widget.items.length,
              (index) => _buildNavigationItem(index),
            ),
          ),
        ),
      ),
    );
  }

  BoxDecoration _buildBackgroundDecoration() {
    switch (widget.backgroundType) {
      case NavBarBackgroundType.solid:
        return BoxDecoration(
          color: widget.backgroundColor ?? AppTheme.surfaceColor,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, -2),
            ),
          ],
        );
      case NavBarBackgroundType.gradient:
        return BoxDecoration(
          gradient: widget.gradient ?? AppTheme.darkGradient,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, -2),
            ),
          ],
        );
      case NavBarBackgroundType.glass:
        return BoxDecoration(
          color: Colors.white.withValues(alpha: 0.1),
          border: Border(
            top: BorderSide(
              color: Colors.white.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        );
      case NavBarBackgroundType.premium:
        return BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.darkBackground.withValues(alpha: 0.95),
              AppTheme.darkBackground,
            ],
          ),
          border: Border(
            top: BorderSide(
              color: AppTheme.primaryGold.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          boxShadow: [
            BoxShadow(
              color: AppTheme.primaryGold.withValues(alpha: 0.2),
              blurRadius: 20,
              offset: const Offset(0, -8),
              spreadRadius: 2,
            ),
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 15,
              offset: const Offset(0, -5),
            ),
          ],
        );
      case NavBarBackgroundType.none:
        return const BoxDecoration();
    }
  }

  Widget _buildNavigationItem(int index) {
    final item = widget.items[index];
    final isSelected = index == widget.selectedIndex;

    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            HapticFeedback.lightImpact();
            widget.onTap(index);
          },
          borderRadius: BorderRadius.circular(16),
          splashColor: AppTheme.primaryGold.withValues(alpha: 0.1),
          highlightColor: AppTheme.primaryGold.withValues(alpha: 0.05),
          child: AnimatedBuilder(
            animation: Listenable.merge([
              _scaleAnimations[index],
              _fadeAnimations[index],
            ]),
            builder: (context, child) {
              return Container(
                padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Icon with premium background
                    AnimatedContainer(
                      duration: widget.animationDuration,
                      curve: widget.animationCurve,
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        gradient: isSelected
                            ? LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  AppTheme.primaryGold.withValues(alpha: 0.3),
                                  AppTheme.primaryGold.withValues(alpha: 0.1),
                                ],
                              )
                            : null,
                        color: isSelected ? null : Colors.transparent,
                        borderRadius: BorderRadius.circular(14),
                        border: isSelected
                            ? Border.all(
                                color:
                                    AppTheme.primaryGold.withValues(alpha: 0.4),
                                width: 1,
                              )
                            : null,
                        boxShadow: isSelected
                            ? [
                                BoxShadow(
                                  color: AppTheme.primaryGold
                                      .withValues(alpha: 0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ]
                            : null,
                      ),
                      child: Transform.scale(
                        scale: isSelected ? _scaleAnimations[index].value : 1.0,
                        child: Icon(
                          isSelected
                              ? item.selectedIcon ?? item.icon
                              : item.icon,
                          size: isSelected
                              ? widget.selectedIconSize
                              : widget.iconSize,
                          color: isSelected
                              ? AppTheme.primaryGold
                              : AppTheme.textSecondary.withValues(
                                  alpha: _fadeAnimations[index].value,
                                ),
                        ),
                      ),
                    ),

                    // Label with animation
                    if (widget.showLabels &&
                        (widget.showSelectedLabels || isSelected))
                      AnimatedContainer(
                        duration: widget.animationDuration,
                        curve: widget.animationCurve,
                        margin: const EdgeInsets.only(top: 6),
                        child: AnimatedDefaultTextStyle(
                          duration: widget.animationDuration,
                          curve: widget.animationCurve,
                          style: TextStyle(
                            fontSize: isSelected
                                ? widget.selectedLabelFontSize
                                : widget.labelFontSize,
                            fontWeight:
                                isSelected ? FontWeight.w600 : FontWeight.w400,
                            color: isSelected
                                ? AppTheme.primaryGold
                                : AppTheme.textSecondary.withValues(
                                    alpha: _fadeAnimations[index].value,
                                  ),
                            letterSpacing: isSelected ? 0.5 : 0,
                          ),
                          child: Text(
                            item.label,
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}

class NavigationItem {
  final IconData icon;
  final IconData? selectedIcon;
  final String label;

  const NavigationItem({
    required this.icon,
    this.selectedIcon,
    required this.label,
  });
}

// Pre-defined navigation items for the trainer app
class TrainerNavigationItems {
  static const List<NavigationItem> items = [
    NavigationItem(
      icon: Icons.dashboard_outlined,
      selectedIcon: Icons.dashboard,
      label: 'الرئيسية',
    ),
    NavigationItem(
      icon: Icons.people_outline,
      selectedIcon: Icons.people,
      label: 'المتدربين',
    ),
    NavigationItem(
      icon: Icons.fitness_center_outlined,
      selectedIcon: Icons.fitness_center,
      label: 'الجلسات',
    ),
    NavigationItem(
      icon: Icons.assignment_outlined,
      selectedIcon: Icons.assignment,
      label: 'الخطط',
    ),
    NavigationItem(
      icon: Icons.subscriptions_outlined,
      selectedIcon: Icons.subscriptions,
      label: 'الاشتراكات',
    ),
    NavigationItem(
      icon: Icons.person_outline,
      selectedIcon: Icons.person,
      label: 'الملف الشخصي',
    ),
  ];
}
