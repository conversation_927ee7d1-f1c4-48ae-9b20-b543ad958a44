import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final String? subtitle;
  final String? imageUrl;
  final Widget? leading;
  final List<Widget>? actions;
  final bool showProfile;
  final String? trainerName;
  final List<Color>? gradientColors;
  final double height;
  const CustomAppBar({
    super.key,
    required this.title,
    this.subtitle,
    this.imageUrl,
    this.leading,
    this.actions,
    this.showProfile = false,
    this.trainerName,
    this.gradientColors,
    this.height = 74,
  });

  @override
  Size get preferredSize => Size.fromHeight(height);

  @override
  Widget build(BuildContext context) {
    final List<Color> colors =
        gradientColors ?? [const Color(0xFFFFD700), const Color(0xFFB8860B)];
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: colors,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: const [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 14,
            offset: Offset(0, 5),
          ),
        ],
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(24)),
      ),
      child: SafeArea(
        bottom: false,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              leading ??
                  (Navigator.canPop(context)
                      ? IconButton(
                          icon: const Icon(Icons.arrow_back_ios_new_rounded,
                              color: Color(0xFFB8860B), size: 22),
                          onPressed: () => Navigator.of(context).maybePop(),
                        )
                      : const SizedBox(width: 0)),
              if (showProfile && (imageUrl != null && imageUrl!.isNotEmpty))
                CircleAvatar(
                  radius: 20,
                  backgroundImage: NetworkImage(imageUrl!),
                  backgroundColor: Colors.white,
                )
              else if (showProfile)
                const CircleAvatar(
                  radius: 20,
                  backgroundColor: Colors.white,
                  child: Icon(Icons.person, color: Color(0xFFB8860B), size: 22),
                ),
              if (showProfile) const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                        letterSpacing: 0.1,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (trainerName != null && trainerName!.isNotEmpty)
                      Text(
                        trainerName!,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    if (subtitle != null && subtitle!.isNotEmpty)
                      Text(
                        subtitle!,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.black54,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                  ],
                ),
              ),
              if (actions != null)
                ...actions!.map((a) =>
                    Padding(padding: const EdgeInsets.only(left: 4), child: a)),
            ],
          ),
        ),
      ),
    );
  }
}
