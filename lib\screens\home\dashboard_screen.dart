import 'package:flutter/material.dart';
import '../../main.dart';
import '../../theme/app_theme.dart';
import '../../services/localization_service.dart';
import '../../widgets/custom_card.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/animated_widgets.dart';
import '../../widgets/modern_navigation_bar.dart';
import '../../widgets/professional_widgets.dart';
import '../../widgets/modern_app_bar.dart';
import '../../widgets/premium_widgets.dart';
import 'trainees_screen.dart';
import 'sessions_screen.dart';
import 'plans_screen.dart';
import 'profile_screen.dart';
import '../../models/user_model.dart';
import '../../models/trainer_model.dart';
import '../profile/trainer_profile_setup_screen.dart';
import '../subscriptions/subscription_plans_screen.dart';

class DashboardScreen extends StatefulWidget {
  final Function(String) onLanguageChanged;

  const DashboardScreen({super.key, required this.onLanguageChanged});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  int _selectedIndex = 0;
  UserModel? _userModel;
  TrainerModel? _trainerModel;
  Map<String, int> _stats = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  bool _isTrainerProfileComplete(Map<String, dynamic> trainerData) {
    return trainerData['bio'] != null &&
        trainerData['bio'].toString().trim().isNotEmpty &&
        trainerData['specialization'] != null &&
        (trainerData['specialization'] is List
            ? (trainerData['specialization'] as List).isNotEmpty
            : trainerData['specialization'].toString().isNotEmpty) &&
        trainerData['experience_years'] != null &&
        trainerData['certifications'] != null &&
        trainerData['languages'] != null &&
        trainerData['price_per_session'] != null &&
        trainerData['price_per_month'] != null;
  }

  Future<void> _loadDashboardData() async {
    try {
      final user = supabase.auth.currentUser;
      debugPrint('Supabase Auth User:');
      debugPrint(user != null ? user.id : 'No user');
      if (user == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // جلب بيانات المستخدم (users)
      final userData = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .maybeSingle();
      debugPrint('User row from users table:');
      debugPrint(userData != null ? userData.toString() : 'No user row');

      // جلب بيانات المدرب (trainers)
      final trainerList = await supabase
          .from('trainers')
          .select('*')
          .eq('user_id', user.id)
          .limit(1);

      final trainerResponse = trainerList.isNotEmpty ? trainerList.first : null;
      debugPrint('Trainer row from trainers table:');
      debugPrint(trainerResponse != null
          ? trainerResponse.toString()
          : 'No trainer row');

      // تحقق من وجود صف للمدرب
      if (trainerResponse == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // منطق أول تسجيل دخول فقط
      final isFirstLogin = user.createdAt == user.lastSignInAt;
      if (!_isTrainerProfileComplete(trainerResponse) && isFirstLogin) {
        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => TrainerProfileSetupScreen(
                  onLanguageChanged: widget.onLanguageChanged),
            ),
          );
        }
        return;
      }
      if (userData == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      final userModel = UserModel.fromJson(userData);
      TrainerModel? trainerModel;
      trainerModel = TrainerModel.fromJson(trainerResponse);

      // Load stats
      final trainees = await supabase
          .from('trainer_assignments')
          .select('id')
          .eq('trainer_id', trainerModel.id)
          .eq('status', 'active');
      debugPrint('Trainees count: ${trainees.length}');
      debugPrint('Trainees raw: ${trainees.toString()}');

      final sessions = await supabase
          .from('sessions')
          .select('id')
          .eq('trainer_id', trainerModel.id)
          .eq('status', 'completed');
      debugPrint('Sessions count: ${sessions.length}');
      debugPrint('Sessions raw: ${sessions.toString()}');

      final pending = await supabase
          .from('trainer_assignments')
          .select('id')
          .eq('trainer_id', trainerModel.id)
          .eq('status', 'pending');
      debugPrint('Pending count: ${pending.length}');
      debugPrint('Pending raw: ${pending.toString()}');

      setState(() {
        _userModel = userModel;
        _trainerModel = trainerModel;
        _stats = {
          'trainees': trainees.length,
          'sessions': sessions.length,
          'pending': pending.length,
        };
        _isLoading = false;
      });
    } catch (error, stack) {
      debugPrint('Dashboard error:');
      debugPrint(error.toString());
      debugPrint(stack.toString());
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(color: AppTheme.primaryGold),
        ),
      );
    }

    return Scaffold(
      // لا يوجد appBar في الداشبورد
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.darkBackground,
              Color(0xFF1A1A1A),
            ],
          ),
        ),
        child: _buildDashboardContent(),
      ),
      // Modern Navigation Bar
      bottomNavigationBar: ModernNavigationBar(
        selectedIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        items: TrainerNavigationItems.items,
        height: 70,
        iconSize: 22,
        selectedIconSize: 26,
        paddingV: 8,
        labelFontSize: 11,
        selectedLabelFontSize: 12,
        backgroundType: NavBarBackgroundType.solid,
        backgroundColor: AppTheme.surfaceColor,
        showLabels: true,
        showSelectedLabels: true,
      ),
    );
  }

  Widget _buildDashboardContent() {
    switch (_selectedIndex) {
      case 0:
        return _buildDashboard();
      case 1:
        return const TraineesScreen();
      case 2:
        return const SessionsScreen();
      case 3:
        return const PlansScreen();
      case 4:
        return const SubscriptionPlansScreen();
      case 5:
        return const ProfileScreen();
      default:
        return _buildDashboard();
    }
  }

  Widget _buildDashboard() {
    return Container(
      decoration: const BoxDecoration(
        gradient: AppTheme.darkGradient,
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppTheme.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Safe area for status bar
            SizedBox(
                height: MediaQuery.of(context).padding.top + AppTheme.spacingM),

            // Premium Welcome Header
            FadeInAnimation(
              delay: const Duration(milliseconds: 100),
              child: Container(
                padding: const EdgeInsets.all(AppTheme.spacingL),
                decoration: AppTheme.premiumCardDecoration,
                child: Row(
                  children: [
                    // Premium Avatar
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: AppTheme.premiumGoldGradient,
                        boxShadow: AppTheme.glowShadow,
                      ),
                      padding: const EdgeInsets.all(3),
                      child: PremiumAvatar(
                        name: _userModel?.fullName,
                        radius: 32,
                        showStatus: true,
                        isOnline: true,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingL),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            LocalizationService.isArabic
                                ? 'مرحباً ${_userModel?.fullName ?? 'مدرب'}'
                                : 'Welcome ${_userModel?.fullName ?? 'Trainer'}',
                            style: AppTheme.headlineMedium.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppTheme.textPrimary,
                            ),
                          ),
                          const SizedBox(height: AppTheme.spacingXS),
                          Text(
                            LocalizationService.isArabic
                                ? 'جاهز لبدء يوم تدريبي جديد؟'
                                : 'Ready to start a new training day?',
                            style: AppTheme.bodyMedium.copyWith(
                              color: AppTheme.textSecondary,
                            ),
                          ),
                          const SizedBox(height: AppTheme.spacingS),
                          // Premium Status Badge
                          PremiumStatusBadge(
                            text: LocalizationService.isArabic
                                ? 'مدرب محترف'
                                : 'Pro Trainer',
                            color: AppTheme.primaryGold,
                            icon: Icons.verified,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: AppTheme.spacingL),

            // Premium Stats Grid
            SlideInAnimation(
              delay: const Duration(milliseconds: 200),
              begin: const Offset(0, 0.5),
              child: GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                crossAxisSpacing: AppTheme.spacingM,
                mainAxisSpacing: AppTheme.spacingM,
                childAspectRatio: 1.2,
                children: [
                  AnimatedStatsCard(
                    title:
                        LocalizationService.isArabic ? 'المتدربين' : 'Trainees',
                    value: _stats['trainees']?.toString() ?? '0',
                    subtitle: LocalizationService.isArabic
                        ? 'متدرب نشط'
                        : 'Active trainees',
                    icon: Icons.people,
                    color: AppTheme.primaryGold,
                    trend: '+12%',
                    isPositive: true,
                  ),
                  AnimatedStatsCard(
                    title:
                        LocalizationService.isArabic ? 'الجلسات' : 'Sessions',
                    value: _stats['sessions']?.toString() ?? '0',
                    subtitle: LocalizationService.isArabic
                        ? 'جلسة مكتملة'
                        : 'Completed sessions',
                    icon: Icons.fitness_center,
                    color: AppTheme.successGreen,
                    trend: '+8%',
                    isPositive: true,
                  ),
                  AnimatedStatsCard(
                    title: LocalizationService.isArabic
                        ? 'طلبات معلقة'
                        : 'Pending',
                    value: _stats['pending']?.toString() ?? '0',
                    subtitle: LocalizationService.isArabic
                        ? 'طلب جديد'
                        : 'New requests',
                    icon: Icons.pending,
                    color: AppTheme.warningOrange,
                  ),
                  AnimatedStatsCard(
                    title: LocalizationService.isArabic ? 'التقييم' : 'Rating',
                    value: _trainerModel?.rating != null
                        ? '${_trainerModel!.rating.toStringAsFixed(1)}⭐'
                        : '0.0⭐',
                    subtitle: LocalizationService.isArabic
                        ? 'تقييم ممتاز'
                        : 'Excellent rating',
                    icon: Icons.star,
                    color: AppTheme.primaryGold,
                    trend: '+0.2',
                    isPositive: true,
                  ),
                ],
              ),
            ),
            const SizedBox(height: AppTheme.spacingXL),

            // Premium Quick Actions Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppTheme.spacingS),
                  decoration: BoxDecoration(
                    gradient: AppTheme.premiumGoldGradient,
                    borderRadius: AppTheme.mediumRadius,
                  ),
                  child: const Icon(
                    Icons.flash_on,
                    color: Colors.black,
                    size: 20,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingM),
                Text(
                  LocalizationService.isArabic
                      ? 'إجراءات سريعة'
                      : 'Quick Actions',
                  style: AppTheme.headlineSmall.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppTheme.spacingL),

            // Premium Action Buttons Grid
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: AppTheme.spacingM,
              mainAxisSpacing: AppTheme.spacingM,
              childAspectRatio: 1.1,
              children: [
                _buildPremiumActionCard(
                  LocalizationService.isArabic
                      ? 'إدارة المتدربين'
                      : 'Manage Trainees',
                  Icons.people_outline,
                  () => setState(() => _selectedIndex = 1),
                  AppTheme.successGreen,
                ),
                _buildPremiumActionCard(
                  LocalizationService.isArabic
                      ? 'جدولة جلسة'
                      : 'Schedule Session',
                  Icons.add_circle_outline,
                  () => setState(() => _selectedIndex = 2),
                  AppTheme.warningOrange,
                ),
                _buildPremiumActionCard(
                  LocalizationService.isArabic ? 'إنشاء خطة' : 'Create Plan',
                  Icons.assignment_add,
                  () => setState(() => _selectedIndex = 3),
                  AppTheme.primaryGold,
                ),
                _buildPremiumActionCard(
                  LocalizationService.isArabic
                      ? 'خطط الاشتراك'
                      : 'Subscriptions',
                  Icons.subscriptions,
                  () => setState(() => _selectedIndex = 4),
                  AppTheme.purpleAccent,
                ),
                _buildPremiumActionCard(
                  LocalizationService.isArabic ? 'الملف الشخصي' : 'My Profile',
                  Icons.person_outline,
                  () => setState(() => _selectedIndex = 5),
                  AppTheme.infoBlue,
                ),
                _buildPremiumActionCard(
                  LocalizationService.isArabic ? 'الإعدادات' : 'Settings',
                  Icons.settings,
                  () {
                    // Navigate to settings
                  },
                  AppTheme.tealAccent,
                ),
              ],
            ),

            const SizedBox(height: AppTheme.spacingXL),
          ],
        ),
      ),
    );
  }

  Widget _buildPremiumActionCard(
    String title,
    IconData icon,
    VoidCallback onTap,
    Color color,
  ) {
    return PremiumGlassCard(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingM),
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: color.withOpacity(0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Icon(
              icon,
              size: 32,
              color: color,
            ),
          ),
          const SizedBox(height: AppTheme.spacingM),
          Text(
            title,
            textAlign: TextAlign.center,
            style: AppTheme.bodyLarge.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimary,
            ),
          ),
        ],
      ),
    );
  }
}
