import 'package:flutter/material.dart';
import '../../main.dart';
import '../../theme/app_theme.dart';
import '../../services/localization_service.dart';
import '../../widgets/custom_card.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/animated_widgets.dart';
import '../../widgets/modern_navigation_bar.dart';
import '../../widgets/professional_widgets.dart';
import '../../widgets/modern_app_bar.dart';
import 'trainees_screen.dart';
import 'sessions_screen.dart';
import 'plans_screen.dart';
import 'profile_screen.dart';
import '../../models/user_model.dart';
import '../../models/trainer_model.dart';
import '../profile/trainer_profile_setup_screen.dart';
import '../subscriptions/subscription_plans_screen.dart';

class DashboardScreen extends StatefulWidget {
  final Function(String) onLanguageChanged;

  const DashboardScreen({super.key, required this.onLanguageChanged});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  int _selectedIndex = 0;
  UserModel? _userModel;
  TrainerModel? _trainerModel;
  Map<String, int> _stats = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  bool _isTrainerProfileComplete(Map<String, dynamic> trainerData) {
    return trainerData['bio'] != null &&
        trainerData['bio'].toString().trim().isNotEmpty &&
        trainerData['specialization'] != null &&
        (trainerData['specialization'] is List
            ? (trainerData['specialization'] as List).isNotEmpty
            : trainerData['specialization'].toString().isNotEmpty) &&
        trainerData['experience_years'] != null &&
        trainerData['certifications'] != null &&
        trainerData['languages'] != null &&
        trainerData['price_per_session'] != null &&
        trainerData['price_per_month'] != null;
  }

  Future<void> _loadDashboardData() async {
    try {
      final user = supabase.auth.currentUser;
      debugPrint('Supabase Auth User:');
      debugPrint(user != null ? user.id : 'No user');
      if (user == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // جلب بيانات المستخدم (users)
      final userData = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .maybeSingle();
      debugPrint('User row from users table:');
      debugPrint(userData != null ? userData.toString() : 'No user row');

      // جلب بيانات المدرب (trainers)
      final trainerList = await supabase
          .from('trainers')
          .select('*')
          .eq('user_id', user.id)
          .limit(1);

      final trainerResponse = trainerList.isNotEmpty ? trainerList.first : null;
      debugPrint('Trainer row from trainers table:');
      debugPrint(trainerResponse != null
          ? trainerResponse.toString()
          : 'No trainer row');

      // تحقق من وجود صف للمدرب
      if (trainerResponse == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // منطق أول تسجيل دخول فقط
      final isFirstLogin = user.createdAt == user.lastSignInAt;
      if (!_isTrainerProfileComplete(trainerResponse) && isFirstLogin) {
        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => TrainerProfileSetupScreen(
                  onLanguageChanged: widget.onLanguageChanged),
            ),
          );
        }
        return;
      }
      if (userData == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      final userModel = UserModel.fromJson(userData);
      TrainerModel? trainerModel;
      trainerModel = TrainerModel.fromJson(trainerResponse);

      // Load stats
      final trainees = await supabase
          .from('trainer_assignments')
          .select('id')
          .eq('trainer_id', trainerModel.id)
          .eq('status', 'active');
      debugPrint('Trainees count: ${trainees.length}');
      debugPrint('Trainees raw: ${trainees.toString()}');

      final sessions = await supabase
          .from('sessions')
          .select('id')
          .eq('trainer_id', trainerModel.id)
          .eq('status', 'completed');
      debugPrint('Sessions count: ${sessions.length}');
      debugPrint('Sessions raw: ${sessions.toString()}');

      final pending = await supabase
          .from('trainer_assignments')
          .select('id')
          .eq('trainer_id', trainerModel.id)
          .eq('status', 'pending');
      debugPrint('Pending count: ${pending.length}');
      debugPrint('Pending raw: ${pending.toString()}');

      setState(() {
        _userModel = userModel;
        _trainerModel = trainerModel;
        _stats = {
          'trainees': trainees.length,
          'sessions': sessions.length,
          'pending': pending.length,
        };
        _isLoading = false;
      });
    } catch (error, stack) {
      debugPrint('Dashboard error:');
      debugPrint(error.toString());
      debugPrint(stack.toString());
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(color: AppTheme.primaryGold),
        ),
      );
    }

    return Scaffold(
      // لا يوجد appBar في الداشبورد
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.darkBackground,
              Color(0xFF1A1A1A),
            ],
          ),
        ),
        child: _buildDashboardContent(),
      ),
      // Modern Navigation Bar
      bottomNavigationBar: ModernNavigationBar(
        selectedIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        items: TrainerNavigationItems.items,
        height: 70,
        iconSize: 22,
        selectedIconSize: 26,
        paddingV: 8,
        labelFontSize: 11,
        selectedLabelFontSize: 12,
        backgroundType: NavBarBackgroundType.solid,
        backgroundColor: AppTheme.surfaceColor,
        showLabels: true,
        showSelectedLabels: true,
      ),
    );
  }

  Widget _buildDashboardContent() {
    switch (_selectedIndex) {
      case 0:
        return _buildDashboard();
      case 1:
        return const TraineesScreen();
      case 2:
        return const SessionsScreen();
      case 3:
        return const PlansScreen();
      case 4:
        return const SubscriptionPlansScreen();
      case 5:
        return const ProfileScreen();
      default:
        return _buildDashboard();
    }
  }

  Widget _buildDashboard() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Safe area for status bar
          SizedBox(height: MediaQuery.of(context).padding.top),

          // Welcome Card with Animation
          FadeInAnimation(
            delay: const Duration(milliseconds: 100),
            child: GradientCard(
              gradient: AppTheme.goldGradient,
              margin: const EdgeInsets.only(bottom: 24),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 36,
                    backgroundColor: Colors.white,
                    child:
                        Icon(Icons.person, size: 40, color: Color(0xFFB8860B)),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          LocalizationService.isArabic
                              ? 'مرحباً ${_userModel?.fullName ?? 'مدرب'}'
                              : 'Welcome ${_userModel?.fullName ?? 'Trainer'}',
                          style: const TextStyle(
                            fontSize: 26,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          LocalizationService.isArabic
                              ? 'جاهز لبدء يوم تدريبي جديد؟'
                              : 'Ready to start a new training day?',
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Stats Cards with Animation
          SlideInAnimation(
            delay: const Duration(milliseconds: 200),
            begin: const Offset(0, 0.5),
            child: Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    LocalizationService.isArabic ? 'المتدربين' : 'Trainees',
                    _stats['trainees']?.toString() ?? '0',
                    Icons.people,
                    AppTheme.primaryGold,
                    AppTheme.darkGold,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    LocalizationService.isArabic ? 'الجلسات' : 'Sessions',
                    _stats['sessions']?.toString() ?? '0',
                    Icons.fitness_center,
                    const Color(0xFF43E97B),
                    const Color(0xFF38F9D7),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  LocalizationService.isArabic ? 'طلبات معلقة' : 'Pending',
                  _stats['pending']?.toString() ?? '0',
                  Icons.pending,
                  Color(0xFFFFA726),
                  Color(0xFFFF7043),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  LocalizationService.isArabic ? 'التقييم' : 'Rating',
                  '${_trainerModel?.rating.toStringAsFixed(1) ?? '0.0'}⭐',
                  Icons.star,
                  Color(0xFFFFD700),
                  Color(0xFFFFA726),
                ),
              ),
            ],
          ),
          const SizedBox(height: 32),
          // Quick Actions
          Text(
            LocalizationService.isArabic ? 'إجراءات سريعة' : 'Quick Actions',
            style: const TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Color(0xFFFFD700),
            ),
          ),
          const SizedBox(height: 20),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 20,
            mainAxisSpacing: 20,
            childAspectRatio: 1.1,
            children: [
              _buildActionCard(
                LocalizationService.isArabic
                    ? 'إدارة المتدربين'
                    : 'Manage Trainees',
                Icons.people_outline,
                () => setState(() => _selectedIndex = 1),
                Color(0xFF43E97B),
                Color(0xFF38F9D7),
              ),
              _buildActionCard(
                LocalizationService.isArabic
                    ? 'جدولة جلسة'
                    : 'Schedule Session',
                Icons.add_circle_outline,
                () => setState(() => _selectedIndex = 2),
                Color(0xFFFFA726),
                Color(0xFFFF7043),
              ),
              _buildActionCard(
                LocalizationService.isArabic ? 'إنشاء خطة' : 'Create Plan',
                Icons.assignment_add,
                () => setState(() => _selectedIndex = 3),
                Color(0xFFB8860B),
                Color(0xFFFFD700),
              ),
              _buildActionCard(
                LocalizationService.isArabic ? 'خطط الاشتراك' : 'Subscriptions',
                Icons.subscriptions,
                () => setState(() => _selectedIndex = 4),
                Color(0xFF9C27B0),
                Color(0xFFE91E63),
              ),
              _buildActionCard(
                LocalizationService.isArabic ? 'الملف الشخصي' : 'My Profile',
                Icons.person_outline,
                () => setState(() => _selectedIndex = 5),
                Color(0xFF2196F3),
                Color(0xFF21CBF3),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color1, Color color2) {
    return GradientCard(
      gradient: LinearGradient(
        colors: [color1, color2],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      padding: const EdgeInsets.all(18),
      margin: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: Colors.white, size: 24),
              ),
              Text(
                value,
                style: AppTheme.headlineMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: AppTheme.bodyMedium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionCard(String title, IconData icon, VoidCallback onTap,
      Color color1, Color color2) {
    return GradientCard(
      gradient: LinearGradient(
        colors: [color1, color2],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      padding: const EdgeInsets.all(18),
      margin: EdgeInsets.zero,
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withValues(alpha: 0.2),
            ),
            padding: const EdgeInsets.all(16),
            child: Icon(
              icon,
              size: 32,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            title,
            textAlign: TextAlign.center,
            style: AppTheme.bodyLarge.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
