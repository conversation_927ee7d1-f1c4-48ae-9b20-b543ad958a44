-- Test if subscription_plans table exists and create it if not
DO $$
BEGIN
    -- Check if table exists
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'subscription_plans') THEN
        -- Create the table
        CREATE TABLE public.subscription_plans (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
            name TEXT NOT NULL CHECK (name IN ('Basic', 'Standard', 'Premium', 'VIP', 'Custom')),
            description TEXT,
            duration_days INTEGER NOT NULL CHECK (duration_days > 0),
            price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
            features TEXT[] DEFAULT '{}',
            session_count INTEGER DEFAULT 0, -- 0 means unlimited
            nutrition_plan_included BOOLEAN DEFAULT false,
            workout_plan_included BOOLEAN DEFAULT true,
            chat_support BOOLEAN DEFAULT true,
            video_calls BOOLEAN DEFAULT false,
            progress_tracking BOOLEAN DEFAULT true,
            is_active BOOLEAN DEFAULT true,
            sort_order INTEGER DEFAULT 0,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(trainer_id, name)
        );
        
        RAISE NOTICE 'subscription_plans table created successfully';
    ELSE
        RAISE NOTICE 'subscription_plans table already exists';
    END IF;
END $$;

-- Check table structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'subscription_plans' 
ORDER BY ordinal_position;
