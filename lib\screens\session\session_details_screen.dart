import 'package:flutter/material.dart';
import '../../main.dart';
import '../../theme/app_theme.dart';
import '../../services/localization_service.dart';
import '../../widgets/widgets.dart';

class SessionDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> session;

  const SessionDetailsScreen({
    super.key,
    required this.session,
  });

  @override
  State<SessionDetailsScreen> createState() => _SessionDetailsScreenState();
}

class _SessionDetailsScreenState extends State<SessionDetailsScreen> {
  bool _isLoading = true;
  Map<String, dynamic>? _sessionDetails;
  Map<String, dynamic>? _traineeDetails;
  List<Map<String, dynamic>> _exercises = [];
  Map<String, dynamic>? _progress;

  @override
  void initState() {
    super.initState();
    _loadSessionDetails();
  }

  Future<void> _loadSessionDetails() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final sessionId = widget.session['id'];
      final traineeId = widget.session['trainee_id'];

      // جلب تفاصيل الجلسة
      final sessionData = await supabase
          .from('sessions')
          .select('*')
          .eq('id', sessionId)
          .maybeSingle();

      // جلب تفاصيل المتدرب
      final traineeData = await supabase
          .from('users')
          .select('id, full_name, avatar_url, email, phone')
          .eq('id', traineeId)
          .maybeSingle();

      // جلب التمارين المرتبطة بالجلسة (مؤقتاً فارغة حتى يتم إنشاء الجدول)
      final exercises = <Map<String, dynamic>>[];

      // جلب تقدم المتدرب (بناءً على trainee_id وليس session_id)
      final progressList = await supabase
          .from('progress_tracking')
          .select('*')
          .eq('trainee_id', traineeId)
          .order('created_at', ascending: false)
          .limit(5); // أحدث 5 قياسات

      final progress = progressList.isNotEmpty ? progressList.first : null;

      setState(() {
        _sessionDetails = sessionData;
        _traineeDetails = traineeData;
        _exercises = List<Map<String, dynamic>>.from(exercises);
        _progress = progress;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              LocalizationService.isArabic
                  ? 'خطأ في تحميل البيانات: $e'
                  : 'Error loading data: $e',
            ),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    }
  }

  Future<void> _updateSessionStatus(String newStatus) async {
    try {
      await supabase
          .from('sessions')
          .update({'status': newStatus}).eq('id', widget.session['id']);

      setState(() {
        _sessionDetails?['status'] = newStatus;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              LocalizationService.isArabic
                  ? 'تم تحديث حالة الجلسة بنجاح'
                  : 'Session status updated successfully',
            ),
            backgroundColor: AppTheme.successGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              LocalizationService.isArabic
                  ? 'خطأ في تحديث الحالة: $e'
                  : 'Error updating status: $e',
            ),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: LoadingOverlay(
        isLoading: _isLoading,
        message:
            LocalizationService.isArabic ? 'جاري التحميل...' : 'Loading...',
        child: CustomScrollView(
          slivers: [
            _buildAppBar(),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSessionHeader(),
                    const SizedBox(height: 16),
                    _buildTraineeInfo(),
                    const SizedBox(height: 16),
                    _buildSessionDetails(),
                    const SizedBox(height: 16),
                    if (_exercises.isNotEmpty) _buildExercisesList(),
                    const SizedBox(height: 16),
                    if (_progress != null) _buildProgressSection(),
                    const SizedBox(height: 16),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      backgroundColor: AppTheme.darkBackground,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          '${LocalizationService.isArabic ? 'جلسة' : 'Session'} #${widget.session['id']}',
          style: AppTheme.headlineSmall.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: _getSessionStatusGradient(
                _sessionDetails?['status'] ?? widget.session['status']),
          ),
          child: SafeArea(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 40),
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _getSessionStatusIcon(
                        _sessionDetails?['status'] ?? widget.session['status']),
                    size: 40,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSessionHeader() {
    final status = _sessionDetails?['status'] ?? widget.session['status'];
    final scheduledAt =
        _sessionDetails?['scheduled_at'] ?? widget.session['scheduled_at'];

    return ProfessionalContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _getSessionStatusColor(status),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  _getStatusText(status),
                  style: AppTheme.bodySmall.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const Spacer(),
              Text(
                scheduledAt ?? '',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (_sessionDetails?['notes'] != null &&
              _sessionDetails!['notes'].isNotEmpty) ...[
            Text(
              LocalizationService.isArabic
                  ? 'ملاحظات الجلسة:'
                  : 'Session Notes:',
              style: AppTheme.bodyLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _sessionDetails!['notes'],
              style: AppTheme.bodyMedium,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTraineeInfo() {
    if (_traineeDetails == null) return const SizedBox.shrink();

    return ProfessionalContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocalizationService.isArabic
                ? 'معلومات المتدرب'
                : 'Trainee Information',
            style: AppTheme.headlineSmall.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: AppTheme.primaryGold,
                backgroundImage: _traineeDetails!['avatar_url'] != null &&
                        _traineeDetails!['avatar_url'] != ''
                    ? NetworkImage(_traineeDetails!['avatar_url'])
                    : null,
                child: (_traineeDetails!['avatar_url'] == null ||
                        _traineeDetails!['avatar_url'] == '')
                    ? const Icon(Icons.person, color: Colors.white)
                    : null,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _traineeDetails!['full_name'] ?? '',
                      style: AppTheme.bodyLarge.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _traineeDetails!['email'] ?? '',
                      style: AppTheme.bodyMedium.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                    ),
                    if (_traineeDetails!['phone'] != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        _traineeDetails!['phone'],
                        style: AppTheme.bodyMedium.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSessionDetails() {
    return ProfessionalContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocalizationService.isArabic ? 'تفاصيل الجلسة' : 'Session Details',
            style: AppTheme.headlineSmall.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          InfoCard(
            title: LocalizationService.isArabic ? 'النوع' : 'Type',
            content: _sessionDetails?['type'] ??
                (LocalizationService.isArabic ? 'غير محدد' : 'Not specified'),
            icon: Icons.category,
          ),
          const SizedBox(height: 8),
          InfoCard(
            title: LocalizationService.isArabic ? 'المدة' : 'Duration',
            content: _sessionDetails?['duration'] != null
                ? '${_sessionDetails!['duration']} ${LocalizationService.isArabic ? 'دقيقة' : 'minutes'}'
                : (LocalizationService.isArabic ? 'غير محدد' : 'Not specified'),
            icon: Icons.timer,
          ),
          const SizedBox(height: 8),
          InfoCard(
            title: LocalizationService.isArabic ? 'الموقع' : 'Location',
            content: _sessionDetails?['location'] ??
                (LocalizationService.isArabic ? 'غير محدد' : 'Not specified'),
            icon: Icons.location_on,
          ),
        ],
      ),
    );
  }

  Widget _buildExercisesList() {
    return ProfessionalContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocalizationService.isArabic ? 'التمارين' : 'Exercises',
            style: AppTheme.headlineSmall.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _exercises.length,
            separatorBuilder: (context, index) => const SizedBox(height: 8),
            itemBuilder: (context, index) {
              final exercise = _exercises[index];
              return Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.borderColor.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.fitness_center,
                        color: AppTheme.primaryGold),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            exercise['name'] ?? '',
                            style: AppTheme.bodyMedium.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          if (exercise['sets'] != null ||
                              exercise['reps'] != null) ...[
                            const SizedBox(height: 4),
                            Text(
                              '${exercise['sets'] ?? 0} ${LocalizationService.isArabic ? 'مجموعات' : 'sets'} × ${exercise['reps'] ?? 0} ${LocalizationService.isArabic ? 'تكرار' : 'reps'}',
                              style: AppTheme.bodySmall.copyWith(
                                color: AppTheme.textSecondary,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildProgressSection() {
    return ProfessionalContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocalizationService.isArabic ? 'التقدم' : 'Progress',
            style: AppTheme.headlineSmall.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          if (_progress!['weight'] != null) ...[
            InfoCard(
              title: LocalizationService.isArabic ? 'الوزن' : 'Weight',
              content:
                  '${_progress!['weight']} ${LocalizationService.isArabic ? 'كيلو' : 'kg'}',
              icon: Icons.monitor_weight,
            ),
            const SizedBox(height: 8),
          ],
          if (_progress!['body_fat'] != null) ...[
            InfoCard(
              title: LocalizationService.isArabic ? 'نسبة الدهون' : 'Body Fat',
              content: '${_progress!['body_fat']}%',
              icon: Icons.analytics,
            ),
            const SizedBox(height: 8),
          ],
          if (_progress!['notes'] != null &&
              _progress!['notes'].isNotEmpty) ...[
            InfoCard(
              title: LocalizationService.isArabic
                  ? 'ملاحظات التقدم'
                  : 'Progress Notes',
              content: _progress!['notes'],
              icon: Icons.note,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    final status = _sessionDetails?['status'] ?? widget.session['status'];

    return Column(
      children: [
        if (status == 'scheduled') ...[
          CustomButton(
            text: LocalizationService.isArabic ? 'بدء الجلسة' : 'Start Session',
            onPressed: () => _updateSessionStatus('in_progress'),
            gradient: AppTheme.goldGradient,
            icon: const Icon(Icons.play_arrow),
          ),
          const SizedBox(height: 12),
          CustomButton(
            text: LocalizationService.isArabic
                ? 'إلغاء الجلسة'
                : 'Cancel Session',
            onPressed: () => _updateSessionStatus('cancelled'),
            isOutlined: true,
            icon: const Icon(Icons.cancel),
          ),
        ] else if (status == 'in_progress') ...[
          CustomButton(
            text: LocalizationService.isArabic
                ? 'إنهاء الجلسة'
                : 'Complete Session',
            onPressed: () => _updateSessionStatus('completed'),
            gradient: AppTheme.goldGradient,
            icon: const Icon(Icons.check),
          ),
        ] else if (status == 'completed') ...[
          CustomButton(
            text: LocalizationService.isArabic ? 'إضافة ملاحظات' : 'Add Notes',
            onPressed: () {
              // Navigate to add notes screen
            },
            isOutlined: true,
            icon: const Icon(Icons.note_add),
          ),
        ],
      ],
    );
  }

  Color _getSessionStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return AppTheme.successGreen;
      case 'scheduled':
        return AppTheme.infoBlue;
      case 'in_progress':
        return AppTheme.primaryGold;
      case 'cancelled':
        return AppTheme.errorRed;
      default:
        return AppTheme.textSecondary;
    }
  }

  IconData _getSessionStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Icons.check_circle;
      case 'scheduled':
        return Icons.schedule;
      case 'in_progress':
        return Icons.play_circle;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.help;
    }
  }

  Gradient _getSessionStatusGradient(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return const LinearGradient(
          colors: [AppTheme.successGreen, Color(0xFF4CAF50)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'scheduled':
        return const LinearGradient(
          colors: [AppTheme.infoBlue, Color(0xFF2196F3)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'in_progress':
        return AppTheme.goldGradient;
      case 'cancelled':
        return const LinearGradient(
          colors: [AppTheme.errorRed, Color(0xFFE57373)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      default:
        return AppTheme.darkGradient;
    }
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return LocalizationService.isArabic ? 'مكتملة' : 'Completed';
      case 'scheduled':
        return LocalizationService.isArabic ? 'مجدولة' : 'Scheduled';
      case 'in_progress':
        return LocalizationService.isArabic ? 'جارية' : 'In Progress';
      case 'cancelled':
        return LocalizationService.isArabic ? 'ملغية' : 'Cancelled';
      default:
        return status;
    }
  }
}
