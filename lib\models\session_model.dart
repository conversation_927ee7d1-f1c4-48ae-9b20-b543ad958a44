class SessionModel {
  final String id;
  final String trainerId;
  final String traineeId;
  final DateTime scheduledAt;
  final DateTime? completedAt;
  final int durationMinutes;
  final String status;
  final String? sessionType;
  final String? location;
  final String? notes;
  final double? rating;
  final String? feedback;
  final Map<String, dynamic>? attendanceData;
  final String? cancellationReason;
  final DateTime? cancelledAt;
  final String? rescheduleReason;
  final DateTime? rescheduledFrom;
  final bool isRecurring;
  final String? recurringPattern;
  final String? packageId;
  final Map<String, dynamic>? sessionData;
  final DateTime createdAt;
  final DateTime updatedAt;

  SessionModel({
    required this.id,
    required this.trainerId,
    required this.traineeId,
    required this.scheduledAt,
    this.completedAt,
    required this.durationMinutes,
    required this.status,
    this.sessionType,
    this.location,
    this.notes,
    this.rating,
    this.feedback,
    this.attendanceData,
    this.cancellationReason,
    this.cancelledAt,
    this.rescheduleReason,
    this.rescheduledFrom,
    this.isRecurring = false,
    this.recurringPattern,
    this.packageId,
    this.sessionData,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SessionModel.fromJson(Map<String, dynamic> json) {
    return SessionModel(
      id: json['id'] as String,
      trainerId: json['trainer_id'] as String,
      traineeId: json['trainee_id'] as String,
      scheduledAt: DateTime.parse(json['scheduled_at'] as String),
      completedAt: json['completed_at'] != null
          ? DateTime.parse(json['completed_at'] as String)
          : null,
      durationMinutes: json['duration_minutes'] as int? ?? 60,
      status: json['status'] as String? ?? 'scheduled',
      sessionType: json['session_type'] as String?,
      location: json['location'] as String?,
      notes: json['notes'] as String?,
      rating:
          json['rating'] != null ? (json['rating'] as num).toDouble() : null,
      feedback: json['feedback'] as String?,
      attendanceData: json['attendance_data'] as Map<String, dynamic>?,
      cancellationReason: json['cancellation_reason'] as String?,
      cancelledAt: json['cancelled_at'] != null
          ? DateTime.parse(json['cancelled_at'] as String)
          : null,
      rescheduleReason: json['reschedule_reason'] as String?,
      rescheduledFrom: json['rescheduled_from'] != null
          ? DateTime.parse(json['rescheduled_from'] as String)
          : null,
      isRecurring: json['is_recurring'] as bool? ?? false,
      recurringPattern: json['recurring_pattern'] as String?,
      packageId: json['package_id'] as String?,
      sessionData: json['session_data'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trainer_id': trainerId,
      'trainee_id': traineeId,
      'scheduled_at': scheduledAt.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
      'duration_minutes': durationMinutes,
      'status': status,
      'session_type': sessionType,
      'location': location,
      'notes': notes,
      'rating': rating,
      'feedback': feedback,
      'attendance_data': attendanceData,
      'cancellation_reason': cancellationReason,
      'cancelled_at': cancelledAt?.toIso8601String(),
      'reschedule_reason': rescheduleReason,
      'rescheduled_from': rescheduledFrom?.toIso8601String(),
      'is_recurring': isRecurring,
      'recurring_pattern': recurringPattern,
      'package_id': packageId,
      'session_data': sessionData,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  SessionModel copyWith({
    String? id,
    String? trainerId,
    String? traineeId,
    DateTime? scheduledAt,
    DateTime? completedAt,
    int? durationMinutes,
    String? status,
    String? sessionType,
    String? location,
    String? notes,
    double? rating,
    String? feedback,
    Map<String, dynamic>? attendanceData,
    String? cancellationReason,
    DateTime? cancelledAt,
    String? rescheduleReason,
    DateTime? rescheduledFrom,
    bool? isRecurring,
    String? recurringPattern,
    String? packageId,
    Map<String, dynamic>? sessionData,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SessionModel(
      id: id ?? this.id,
      trainerId: trainerId ?? this.trainerId,
      traineeId: traineeId ?? this.traineeId,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      completedAt: completedAt ?? this.completedAt,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      status: status ?? this.status,
      sessionType: sessionType ?? this.sessionType,
      location: location ?? this.location,
      notes: notes ?? this.notes,
      rating: rating ?? this.rating,
      feedback: feedback ?? this.feedback,
      attendanceData: attendanceData ?? this.attendanceData,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      rescheduleReason: rescheduleReason ?? this.rescheduleReason,
      rescheduledFrom: rescheduledFrom ?? this.rescheduledFrom,
      isRecurring: isRecurring ?? this.isRecurring,
      recurringPattern: recurringPattern ?? this.recurringPattern,
      packageId: packageId ?? this.packageId,
      sessionData: sessionData ?? this.sessionData,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  bool get isCompleted => status == 'completed';
  bool get isCancelled => status == 'cancelled';
  bool get isScheduled => status == 'scheduled';
  bool get isRescheduleRequested => status == 'reschedule_requested';
  bool get isNoShow => status == 'no_show';

  bool get canBeCancelled => isScheduled && scheduledAt.isAfter(DateTime.now());
  bool get canBeRescheduled =>
      isScheduled && scheduledAt.isAfter(DateTime.now());
  bool get canBeRated => isCompleted && rating == null;

  String get statusDisplayName {
    switch (status) {
      case 'scheduled':
        return 'مجدولة';
      case 'reschedule_requested':
        return 'طلب إعادة جدولة';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      case 'no_show':
        return 'غياب';
      default:
        return status;
    }
  }
}

// Session Package Model
class SessionPackageModel {
  final String id;
  final String trainerId;
  final String name;
  final String? description;
  final int sessionsCount;
  final double totalPrice;
  final double pricePerSession;
  final int validityDays;
  final bool isActive;
  final Map<String, dynamic>? packageData;
  final DateTime createdAt;
  final DateTime updatedAt;

  SessionPackageModel({
    required this.id,
    required this.trainerId,
    required this.name,
    this.description,
    required this.sessionsCount,
    required this.totalPrice,
    required this.pricePerSession,
    required this.validityDays,
    this.isActive = true,
    this.packageData,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SessionPackageModel.fromJson(Map<String, dynamic> json) {
    return SessionPackageModel(
      id: json['id'] as String,
      trainerId: json['trainer_id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      sessionsCount: json['sessions_count'] as int,
      totalPrice: (json['total_price'] as num).toDouble(),
      pricePerSession: (json['price_per_session'] as num).toDouble(),
      validityDays: json['validity_days'] as int,
      isActive: json['is_active'] as bool? ?? true,
      packageData: json['package_data'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trainer_id': trainerId,
      'name': name,
      'description': description,
      'sessions_count': sessionsCount,
      'total_price': totalPrice,
      'price_per_session': pricePerSession,
      'validity_days': validityDays,
      'is_active': isActive,
      'package_data': packageData,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  double get discountPercentage {
    final regularPrice = sessionsCount * pricePerSession;
    return ((regularPrice - totalPrice) / regularPrice) * 100;
  }
}
