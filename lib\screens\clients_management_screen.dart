import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/trainer_service.dart';
import '../providers/auth_provider.dart';
import '../models/trainer_model.dart';

class ClientsManagementScreen extends StatefulWidget {
  const ClientsManagementScreen({Key? key}) : super(key: key);

  @override
  State<ClientsManagementScreen> createState() =>
      _ClientsManagementScreenState();
}

class _ClientsManagementScreenState extends State<ClientsManagementScreen> {
  List<Map<String, dynamic>> clients = [];
  bool isLoading = true;
  String searchQuery = '';
  String? trainerId;

  @override
  void initState() {
    super.initState();
    _loadClients();
  }

  Future<void> _loadClients() async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final userId = authProvider.user?.id;

      if (userId != null) {
        final trainer = await TrainerService.getTrainerProfile(userId);
        if (trainer != null) {
          trainerId = trainer.id;
          final clientsList =
              await TrainerService.getTrainerClients(trainer.id);

          setState(() {
            clients = clientsList;
            isLoading = false;
          });
        }
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في تحميل العملاء: $e')),
      );
    }
  }

  List<Map<String, dynamic>> get filteredClients {
    if (searchQuery.isEmpty) return clients;

    return clients.where((client) {
      final name = client['user']['full_name']?.toString().toLowerCase() ?? '';
      final email = client['user']['email']?.toString().toLowerCase() ?? '';
      final phone = client['user']['phone']?.toString() ?? '';
      final query = searchQuery.toLowerCase();

      return name.contains(query) ||
          email.contains(query) ||
          phone.contains(query);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة العملاء'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadClients,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          _buildStatsRow(),
          Expanded(
            child: isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildClientsList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Navigate to add new client
          _showAddClientDialog();
        },
        backgroundColor: Colors.blue[600],
        child: const Icon(Icons.person_add, color: Colors.white),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'البحث عن عميل...',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          filled: true,
          fillColor: Colors.grey[100],
        ),
        onChanged: (value) {
          setState(() {
            searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildStatsRow() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          _buildStatChip(
              'إجمالي العملاء', clients.length.toString(), Colors.blue),
          const SizedBox(width: 12),
          _buildStatChip(
            'العملاء النشطين',
            clients
                .where((c) => c['subscription']['status'] == 'active')
                .length
                .toString(),
            Colors.green,
          ),
        ],
      ),
    );
  }

  Widget _buildStatChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClientsList() {
    final displayClients = filteredClients;

    if (displayClients.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              searchQuery.isNotEmpty ? Icons.search_off : Icons.people_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              searchQuery.isNotEmpty
                  ? 'لا توجد نتائج للبحث'
                  : 'لا يوجد عملاء حتى الآن',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            if (searchQuery.isEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'اضغط على + لإضافة عميل جديد',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadClients,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: displayClients.length,
        itemBuilder: (context, index) {
          final client = displayClients[index];
          return _buildClientCard(client);
        },
      ),
    );
  }

  Widget _buildClientCard(Map<String, dynamic> client) {
    final user = client['user'];
    final subscription = client['subscription'];
    final totalSessions = client['total_sessions'] ?? 0;
    final completedSessions = client['completed_sessions'] ?? 0;
    final lastSession = client['last_session'];

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: InkWell(
        onTap: () => _showClientDetails(client),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 25,
                    backgroundImage: user['avatar_url'] != null
                        ? NetworkImage(user['avatar_url'])
                        : null,
                    child: user['avatar_url'] == null
                        ? Text(
                            user['full_name']
                                    ?.toString()
                                    .substring(0, 1)
                                    .toUpperCase() ??
                                'ع',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          )
                        : null,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user['full_name'] ?? 'غير محدد',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          user['email'] ?? '',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildStatusChip(subscription['status']),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  _buildInfoChip(
                    Icons.fitness_center,
                    '$completedSessions/$totalSessions جلسة',
                    Colors.blue,
                  ),
                  const SizedBox(width: 8),
                  if (lastSession != null)
                    _buildInfoChip(
                      Icons.schedule,
                      'آخر جلسة: ${_formatDate(DateTime.parse(lastSession['scheduled_at']))}',
                      Colors.green,
                    ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'انضم في: ${_formatDate(DateTime.parse(subscription['start_date']))}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.message, size: 20),
                        onPressed: () => _sendMessage(client),
                        color: Colors.blue,
                      ),
                      IconButton(
                        icon: const Icon(Icons.phone, size: 20),
                        onPressed: () => _callClient(user['phone']),
                        color: Colors.green,
                      ),
                      IconButton(
                        icon: const Icon(Icons.more_vert, size: 20),
                        onPressed: () => _showClientOptions(client),
                        color: Colors.grey,
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String text;

    switch (status) {
      case 'active':
        color = Colors.green;
        text = 'نشط';
        break;
      case 'paused':
        color = Colors.orange;
        text = 'متوقف';
        break;
      case 'expired':
        color = Colors.red;
        text = 'منتهي';
        break;
      default:
        color = Colors.grey;
        text = status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: color,
        ),
      ),
    );
  }

  Widget _buildInfoChip(IconData icon, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  void _showClientDetails(Map<String, dynamic> client) {
    // Navigate to client details screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ClientDetailsScreen(client: client),
      ),
    );
  }

  void _showAddClientDialog() {
    // Show dialog to add new client
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة عميل جديد'),
        content: const Text('سيتم إضافة هذه الميزة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _sendMessage(Map<String, dynamic> client) {
    // Implement messaging functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة ميزة الرسائل قريباً')),
    );
  }

  void _callClient(String? phone) {
    // Implement calling functionality
    if (phone != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('الاتصال بـ $phone')),
      );
    }
  }

  void _showClientOptions(Map<String, dynamic> client) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('تعديل بيانات العميل'),
              onTap: () {
                Navigator.pop(context);
                // Navigate to edit client
              },
            ),
            ListTile(
              leading: const Icon(Icons.schedule),
              title: const Text('جدولة جلسة'),
              onTap: () {
                Navigator.pop(context);
                // Navigate to schedule session
              },
            ),
            ListTile(
              leading: const Icon(Icons.history),
              title: const Text('تاريخ الجلسات'),
              onTap: () {
                Navigator.pop(context);
                // Navigate to session history
              },
            ),
            ListTile(
              leading: const Icon(Icons.pause_circle, color: Colors.orange),
              title: const Text('إيقاف الاشتراك'),
              onTap: () {
                Navigator.pop(context);
                // Pause subscription
              },
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

class ClientDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> client;

  const ClientDetailsScreen({Key? key, required this.client}) : super(key: key);

  @override
  State<ClientDetailsScreen> createState() => _ClientDetailsScreenState();
}

class _ClientDetailsScreenState extends State<ClientDetailsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  List<Map<String, dynamic>> _sessions = [];
  List<Map<String, dynamic>> _workoutPlans = [];
  List<Map<String, dynamic>> _nutritionPlans = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadClientData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadClientData() async {
    try {
      // Load client sessions, plans, etc.
      // This would be implemented with actual API calls
      await Future.delayed(const Duration(seconds: 1)); // Simulate loading

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = widget.client['user'];
    final subscription = widget.client['subscription'];

    return Scaffold(
      appBar: AppBar(
        title: Text(user['full_name'] ?? 'تفاصيل العميل'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          isScrollable: true,
          tabs: const [
            Tab(text: 'المعلومات'),
            Tab(text: 'الجلسات'),
            Tab(text: 'التمارين'),
            Tab(text: 'التغذية'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              // Edit client
            },
          ),
          PopupMenuButton(
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'message',
                child: ListTile(
                  leading: Icon(Icons.message),
                  title: Text('إرسال رسالة'),
                ),
              ),
              const PopupMenuItem(
                value: 'call',
                child: ListTile(
                  leading: Icon(Icons.phone),
                  title: Text('اتصال'),
                ),
              ),
              const PopupMenuItem(
                value: 'schedule',
                child: ListTile(
                  leading: Icon(Icons.schedule),
                  title: Text('جدولة جلسة'),
                ),
              ),
            ],
            onSelected: (value) {
              switch (value) {
                case 'message':
                  _sendMessage();
                  break;
                case 'call':
                  _callClient();
                  break;
                case 'schedule':
                  _scheduleSession();
                  break;
              }
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildInfoTab(),
                _buildSessionsTab(),
                _buildWorkoutTab(),
                _buildNutritionTab(),
              ],
            ),
    );
  }

  Widget _buildInfoTab() {
    final user = widget.client['user'];
    final subscription = widget.client['subscription'];
    final totalSessions = widget.client['total_sessions'] ?? 0;
    final completedSessions = widget.client['completed_sessions'] ?? 0;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 40,
                    backgroundImage: user['avatar_url'] != null
                        ? NetworkImage(user['avatar_url'])
                        : null,
                    child: user['avatar_url'] == null
                        ? Text(
                            user['full_name']
                                    ?.toString()
                                    .substring(0, 1)
                                    .toUpperCase() ??
                                'ع',
                            style: const TextStyle(fontSize: 24),
                          )
                        : null,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user['full_name'] ?? 'غير محدد',
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          user['email'] ?? '',
                          style: TextStyle(
                            color: Colors.grey[600],
                          ),
                        ),
                        if (user['phone'] != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            user['phone'],
                            style: TextStyle(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  _buildStatusChip(subscription['status']),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Stats Cards
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            childAspectRatio: 1.5,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            children: [
              _buildStatCard('إجمالي الجلسات', totalSessions.toString(),
                  Icons.fitness_center, Colors.blue),
              _buildStatCard('جلسات مكتملة', completedSessions.toString(),
                  Icons.check_circle, Colors.green),
              _buildStatCard(
                  'معدل الحضور',
                  '${totalSessions > 0 ? ((completedSessions / totalSessions) * 100).toStringAsFixed(1) : 0}%',
                  Icons.trending_up,
                  Colors.orange),
              _buildStatCard(
                  'أيام متبقية',
                  _calculateRemainingDays(subscription['end_date']).toString(),
                  Icons.calendar_today,
                  Colors.purple),
            ],
          ),

          const SizedBox(height: 16),

          // Subscription Details
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'تفاصيل الاشتراك',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildInfoRow('نوع الاشتراك',
                      subscription['subscription_type'] ?? 'غير محدد'),
                  _buildInfoRow('تاريخ البداية',
                      _formatDate(DateTime.parse(subscription['start_date']))),
                  _buildInfoRow('تاريخ النهاية',
                      _formatDate(DateTime.parse(subscription['end_date']))),
                  _buildInfoRow('السعر',
                      '${subscription['price']} ${subscription['currency'] ?? 'ريال'}'),
                  _buildInfoRow(
                      'الحالة', _getStatusText(subscription['status'])),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Personal Info
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'المعلومات الشخصية',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildInfoRow('العمر', user['age']?.toString() ?? 'غير محدد'),
                  _buildInfoRow('الجنس', user['gender'] ?? 'غير محدد'),
                  _buildInfoRow(
                      'الطول',
                      user['height'] != null
                          ? '${user['height']} سم'
                          : 'غير محدد'),
                  _buildInfoRow(
                      'الوزن',
                      user['weight'] != null
                          ? '${user['weight']} كجم'
                          : 'غير محدد'),
                  _buildInfoRow('تاريخ التسجيل',
                      _formatDate(DateTime.parse(user['created_at']))),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSessionsTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.fitness_center,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'سجل الجلسات',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'سيتم إضافة هذه الميزة قريباً',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkoutTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.sports_gymnastics,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'خطط التمارين',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'سيتم إضافة هذه الميزة قريباً',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNutritionTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.restaurant,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'خطط التغذية',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'سيتم إضافة هذه الميزة قريباً',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 24, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String text;

    switch (status) {
      case 'active':
        color = Colors.green;
        text = 'نشط';
        break;
      case 'paused':
        color = Colors.orange;
        text = 'متوقف';
        break;
      case 'expired':
        color = Colors.red;
        text = 'منتهي';
        break;
      default:
        color = Colors.grey;
        text = status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: color,
        ),
      ),
    );
  }

  int _calculateRemainingDays(String endDate) {
    try {
      final end = DateTime.parse(endDate);
      final now = DateTime.now();
      final difference = end.difference(now).inDays;
      return difference > 0 ? difference : 0;
    } catch (e) {
      return 0;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'active':
        return 'نشط';
      case 'paused':
        return 'متوقف';
      case 'expired':
        return 'منتهي';
      default:
        return status;
    }
  }

  void _sendMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة ميزة الرسائل قريباً')),
    );
  }

  void _callClient() {
    final phone = widget.client['user']['phone'];
    if (phone != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('الاتصال بـ $phone')),
      );
    }
  }

  void _scheduleSession() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة ميزة جدولة الجلسات قريباً')),
    );
  }
}
