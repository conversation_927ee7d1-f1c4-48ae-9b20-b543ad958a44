import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'screens/splash_screen.dart';
import 'theme/app_theme.dart';
import 'services/localization_service.dart';
import 'l10n/app_localizations_delegate.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Supabase
  await Supabase.initialize(
    url: 'https://ibpusprnnztueluoynjw.supabase.co',
    anonKey:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlicHVzcHJubnp0dWVsdW95bmp3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5MzgxNTgsImV4cCI6MjA2NjUxNDE1OH0.eVy9NeKU7Q1ExFVN0COruseK7RX-xRkve17wYpOTM4c',
  );

  // Initialize services
  await LocalizationService.initialize();

  runApp(const FitGoldTrainerApp());
}

final supabase = Supabase.instance.client;

class FitGoldTrainerApp extends StatefulWidget {
  const FitGoldTrainerApp({super.key});

  @override
  State<FitGoldTrainerApp> createState() => _FitGoldTrainerAppState();
}

class _FitGoldTrainerAppState extends State<FitGoldTrainerApp> {
  Locale _locale = Locale(LocalizationService.currentLanguage);

  void _changeLanguage(String languageCode) {
    setState(() {
      _locale = Locale(languageCode);
    });
    LocalizationService.setLanguage(languageCode);
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'FitGold Trainer - مدرب فت جولد',
      theme: AppTheme.darkTheme,
      locale: _locale,
      localizationsDelegates: const [
        AppLocalizationsDelegate(),
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: LocalizationService.supportedLocales,
      home: SplashScreen(onLanguageChanged: _changeLanguage),
      debugShowCheckedModeBanner: false,
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
          child: Directionality(
            textDirection: LocalizationService.isArabic
                ? TextDirection.rtl
                : TextDirection.ltr,
            child: child!,
          ),
        );
      },
    );
  }
}
