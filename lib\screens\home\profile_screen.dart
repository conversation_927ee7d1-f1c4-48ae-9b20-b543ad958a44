import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';
import '../../services/localization_service.dart';
import '../../main.dart';
import '../../models/user_model.dart';
import '../../models/trainer_model.dart';
import '../../widgets/custom_app_bar.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  UserModel? _userModel;
  TrainerModel? _trainerModel;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadProfile();
  }

  Future<void> _loadProfile() async {
    try {
      final user = supabase.auth.currentUser;
      if (user == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }
      // جلب بيانات المستخدم (users)
      final userData = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .maybeSingle();
      // جلب بيانات المدرب (trainers)
      final trainerList = await supabase
          .from('trainers')
          .select('*')
          .eq('user_id', user.id)
          .limit(1);

      final trainer = trainerList.isNotEmpty ? trainerList.first : null;
      if (trainer == null || userData == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }
      setState(() {
        _userModel = UserModel.fromJson(userData);
        _trainerModel = TrainerModel.fromJson(trainer);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: LocalizationService.isArabic ? 'الملف الشخصي' : 'Profile',
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Color(0xFFFFD700)))
          : (_userModel == null || _trainerModel == null)
              ? Center(
                  child: Text(
                    LocalizationService.isArabic
                        ? 'لا توجد بيانات'
                        : 'No profile data',
                    style: const TextStyle(fontSize: 20, color: Colors.grey),
                  ),
                )
              : ListView(
                  padding: const EdgeInsets.all(24),
                  children: [
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFFFFD700), Color(0xFFB8860B)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(24),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.15),
                            blurRadius: 16,
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          CircleAvatar(
                            radius: 36,
                            backgroundColor: Colors.white,
                            child: Icon(Icons.person,
                                size: 40, color: Color(0xFFB8860B)),
                          ),
                          const SizedBox(width: 20),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  _userModel!.fullName,
                                  style: const TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  _userModel!.email,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    color: Colors.black87,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 32),
                    _buildProfileTile(
                        Icons.phone,
                        LocalizationService.isArabic ? 'رقم الجوال' : 'Phone',
                        _userModel!.phone ?? ''),
                    _buildProfileTile(
                        Icons.info_outline,
                        LocalizationService.isArabic ? 'نبذة' : 'Bio',
                        _trainerModel!.bio ?? ''),
                    _buildProfileTile(
                        Icons.star,
                        LocalizationService.isArabic ? 'التقييم' : 'Rating',
                        _trainerModel!.rating.toStringAsFixed(1)),
                    _buildProfileTile(
                        Icons.school,
                        LocalizationService.isArabic
                            ? 'الشهادات'
                            : 'Certifications',
                        _trainerModel!.certifications.join(', ')),
                    _buildProfileTile(
                        Icons.language,
                        LocalizationService.isArabic ? 'اللغات' : 'Languages',
                        _trainerModel!.languages.join(', ')),
                    _buildProfileTile(
                        Icons.work,
                        LocalizationService.isArabic
                            ? 'سنوات الخبرة'
                            : 'Experience',
                        _trainerModel!.experienceYears.toString()),
                  ],
                ),
    );
  }

  Widget _buildProfileTile(IconData icon, String title, String value) {
    return Container(
      margin: const EdgeInsets.only(bottom: 18),
      padding: const EdgeInsets.all(18),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF232526), Color(0xFF414345)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(18),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.12),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(icon, color: Color(0xFFFFD700), size: 28),
          const SizedBox(width: 18),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  value,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 15,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
