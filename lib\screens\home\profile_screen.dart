import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';
import '../../services/localization_service.dart';
import '../../main.dart';
import '../../models/user_model.dart';
import '../../models/trainer_model.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/premium_widgets.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with SingleTickerProviderStateMixin {
  UserModel? _userModel;
  TrainerModel? _trainerModel;
  bool _isLoading = true;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _loadProfile();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadProfile() async {
    try {
      final user = supabase.auth.currentUser;
      if (user == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }
      // جلب بيانات المستخدم (users)
      final userData = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .maybeSingle();
      // جلب بيانات المدرب (trainers)
      final trainerList = await supabase
          .from('trainers')
          .select('*')
          .eq('user_id', user.id)
          .limit(1);

      final trainer = trainerList.isNotEmpty ? trainerList.first : null;
      if (trainer == null || userData == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }
      setState(() {
        _userModel = UserModel.fromJson(userData);
        _trainerModel = TrainerModel.fromJson(trainer);
        _isLoading = false;
      });
      _animationController.forward();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      appBar: CustomAppBar(
        title: LocalizationService.isArabic ? 'الملف الشخصي' : 'Profile',
        actions: [
          Container(
            margin: const EdgeInsets.only(right: AppTheme.spacingM),
            child: PremiumActionButton(
              text: '',
              icon: Icons.edit,
              isPrimary: false,
              width: 48,
              height: 48,
              onPressed: () {
                // Navigate to edit profile
              },
            ),
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.darkGradient,
        ),
        child: _isLoading
            ? const Center(
                child: CircularProgressIndicator(color: AppTheme.primaryGold))
            : (_userModel == null || _trainerModel == null)
                ? _buildEmptyState()
                : _buildProfileContent(),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: PremiumGlassCard(
        width: double.infinity,
        margin: const EdgeInsets.all(AppTheme.spacingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingL),
              decoration: BoxDecoration(
                color: AppTheme.errorRed.withValues(alpha: 0.2),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.person_off,
                size: 64,
                color: AppTheme.errorRed,
              ),
            ),
            const SizedBox(height: AppTheme.spacingL),
            Text(
              LocalizationService.isArabic
                  ? 'لا توجد بيانات'
                  : 'No profile data',
              style: AppTheme.headlineSmall.copyWith(
                color: AppTheme.textPrimary,
              ),
            ),
            const SizedBox(height: AppTheme.spacingS),
            Text(
              LocalizationService.isArabic
                  ? 'تعذر تحميل بيانات الملف الشخصي'
                  : 'Failed to load profile data',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
            const SizedBox(height: AppTheme.spacingL),
            PremiumActionButton(
              text: LocalizationService.isArabic ? 'إعادة المحاولة' : 'Retry',
              icon: Icons.refresh,
              onPressed: _loadProfile,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileContent() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppTheme.spacingM),
          child: Column(
            children: [
              _buildProfileHeader(),
              const SizedBox(height: AppTheme.spacingL),
              _buildStatsSection(),
              const SizedBox(height: AppTheme.spacingL),
              _buildInfoSection(),
              const SizedBox(height: AppTheme.spacingL),
              _buildActionButtons(),
              const SizedBox(height: AppTheme.spacingXL),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return PremiumGoldCard(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Column(
          children: [
            // Profile Avatar
            Stack(
              children: [
                PremiumAvatar(
                  imageUrl: null, // Add avatar URL when available
                  name: _userModel!.fullName,
                  radius: 60,
                  showStatus: false,
                ),
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    padding: const EdgeInsets.all(AppTheme.spacingS),
                    decoration: BoxDecoration(
                      color: AppTheme.successGreen,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppTheme.darkBackground,
                        width: 3,
                      ),
                    ),
                    child: const Icon(
                      Icons.verified,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppTheme.spacingM),

            // Name and Title
            Text(
              _userModel!.fullName,
              style: AppTheme.headlineMedium.copyWith(
                color: AppTheme.darkBackground,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: AppTheme.spacingS),

            Text(
              LocalizationService.isArabic
                  ? 'مدرب شخصي معتمد'
                  : 'Certified Personal Trainer',
              style: AppTheme.bodyLarge.copyWith(
                color: AppTheme.darkBackground.withValues(alpha: 0.8),
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: AppTheme.spacingS),

            Text(
              _userModel!.email,
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.darkBackground.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsSection() {
    return Row(
      children: [
        Expanded(
          child: AnimatedStatsCard(
            title: LocalizationService.isArabic ? 'التقييم' : 'Rating',
            subtitle: LocalizationService.isArabic ? 'من 5' : 'out of 5',
            value: _trainerModel!.rating.toStringAsFixed(1),
            icon: Icons.star,
            color: AppTheme.primaryGold,
          ),
        ),
        const SizedBox(width: AppTheme.spacingM),
        Expanded(
          child: AnimatedStatsCard(
            title: LocalizationService.isArabic ? 'سنوات الخبرة' : 'Experience',
            subtitle: LocalizationService.isArabic ? 'سنة' : 'years',
            value: _trainerModel!.experienceYears.toString(),
            icon: Icons.work,
            color: AppTheme.tealAccent,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoSection() {
    return Column(
      children: [
        _buildPremiumInfoTile(
          icon: Icons.phone,
          title: LocalizationService.isArabic ? 'رقم الجوال' : 'Phone',
          value: _userModel!.phone ?? '',
        ),
        _buildPremiumInfoTile(
          icon: Icons.info_outline,
          title: LocalizationService.isArabic ? 'نبذة' : 'Bio',
          value: _trainerModel!.bio ?? '',
        ),
        _buildPremiumInfoTile(
          icon: Icons.school,
          title: LocalizationService.isArabic ? 'الشهادات' : 'Certifications',
          value: _trainerModel!.certifications.join(', '),
        ),
        _buildPremiumInfoTile(
          icon: Icons.language,
          title: LocalizationService.isArabic ? 'اللغات' : 'Languages',
          value: _trainerModel!.languages.join(', '),
        ),
      ],
    );
  }

  Widget _buildPremiumInfoTile({
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      child: PremiumGlassCard(
        child: PremiumListTile(
          leading: Container(
            padding: const EdgeInsets.all(AppTheme.spacingS),
            decoration: BoxDecoration(
              color: AppTheme.primaryGold.withValues(alpha: 0.2),
              borderRadius: AppTheme.smallRadius,
            ),
            child: Icon(
              icon,
              color: AppTheme.primaryGold,
              size: 24,
            ),
          ),
          title: title,
          subtitle: value.isEmpty
              ? (LocalizationService.isArabic ? 'غير محدد' : 'Not specified')
              : value,
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: PremiumActionButton(
            text: LocalizationService.isArabic
                ? 'تعديل الملف الشخصي'
                : 'Edit Profile',
            icon: Icons.edit,
            onPressed: () {
              // Navigate to edit profile
            },
          ),
        ),
        const SizedBox(width: AppTheme.spacingM),
        Expanded(
          child: PremiumActionButton(
            text:
                LocalizationService.isArabic ? 'مشاركة الملف' : 'Share Profile',
            icon: Icons.share,
            isPrimary: false,
            onPressed: () {
              // Share profile functionality
            },
          ),
        ),
      ],
    );
  }
}
