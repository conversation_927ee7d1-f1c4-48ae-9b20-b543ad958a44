import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../main.dart';
import '../../theme/app_theme.dart';
import '../profile/trainer_profile_setup_screen.dart';
import '../../l10n/app_localizations.dart';
import '../../services/localization_service.dart';
import 'login_screen.dart';
import '../../widgets/custom_app_bar.dart';

class RegisterScreen extends StatefulWidget {
  final Function(String) onLanguageChanged;

  const RegisterScreen({super.key, required this.onLanguageChanged});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _phoneController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _signUp() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await supabase.auth.signUp(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        data: {
          'full_name': _nameController.text.trim(),
        },
      );

      if (response.user != null) {
        // Insert into users table with exact schema fields
        await supabase.from('users').insert({
          'id': response.user!.id,
          'email': _emailController.text.trim(),
          'full_name': _nameController.text.trim(),
          'phone': _phoneController.text.trim(),
          'user_type': 'trainer',
          'is_active': true,
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(LocalizationService.isArabic
                  ? 'تم إرسال رسالة تأكيد إلى بريدك الإلكتروني. يرجى التحقق من بريدك وتأكيد الحساب.'
                  : 'A confirmation email has been sent to your email address. Please check your inbox and confirm your account.'),
              backgroundColor: AppTheme.primaryGold,
            ),
          );
          await Future.delayed(const Duration(seconds: 2));
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => LoginScreen(
                onLanguageChanged: widget.onLanguageChanged,
              ),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(LocalizationService.isArabic
                  ? 'يرجى التحقق من بريدك الإلكتروني لتأكيد الحساب'
                  : 'Please check your email to confirm your account'),
              backgroundColor: AppTheme.primaryGold,
            ),
          );
        }
      }
    } on AuthException catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(error.message),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (error) {
      if (mounted) {
        final l10n = AppLocalizations.of(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.unexpectedError),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: LocalizationService.isArabic
            ? 'إنشاء حساب مدرب جديد'
            : 'Create New Trainer Account',
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppTheme.primaryGold),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.darkBackground,
              Color(0xFF1A1A1A),
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: 20),
                  // Header
                  Center(
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: const LinearGradient(
                          colors: [
                            AppTheme.primaryGold,
                            AppTheme.accentGold,
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: AppTheme.primaryGold.withOpacity(0.3),
                            blurRadius: 15,
                            spreadRadius: 3,
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.person_add,
                        size: 40,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    LocalizationService.isArabic
                        ? 'انضم كمدرب إلى FitGold'
                        : 'Join FitGold as a Trainer',
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryGold,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    LocalizationService.isArabic
                        ? 'ابدأ رحلتك المهنية في التدريب الرياضي'
                        : 'Start your professional fitness training journey',
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppTheme.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 40),

                  // Full Name Field
                  TextFormField(
                    controller: _nameController,
                    decoration: InputDecoration(
                      labelText: LocalizationService.isArabic
                          ? 'الاسم الكامل'
                          : 'Full Name',
                      prefixIcon:
                          const Icon(Icons.person, color: AppTheme.primaryGold),
                      hintText: LocalizationService.isArabic
                          ? 'أدخل اسمك الكامل'
                          : 'Enter your full name',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return LocalizationService.isArabic
                            ? 'يرجى إدخال الاسم الكامل'
                            : 'Please enter your full name';
                      }
                      if (value.length < 3) {
                        return LocalizationService.isArabic
                            ? 'الاسم يجب أن يكون 3 أحرف على الأقل'
                            : 'Name must be at least 3 characters';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 20),

                  // Email Field
                  TextFormField(
                    controller: _emailController,
                    keyboardType: TextInputType.emailAddress,
                    decoration: InputDecoration(
                      labelText: l10n.email,
                      prefixIcon:
                          const Icon(Icons.email, color: AppTheme.primaryGold),
                      hintText: LocalizationService.isArabic
                          ? 'أدخل بريدك الإلكتروني'
                          : 'Enter your email address',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return l10n.emailRequired;
                      }
                      if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                          .hasMatch(value)) {
                        return l10n.emailInvalid;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 20),

                  // Phone Field
                  TextFormField(
                    controller: _phoneController,
                    keyboardType: TextInputType.phone,
                    decoration: InputDecoration(
                      labelText: LocalizationService.isArabic
                          ? 'رقم الهاتف'
                          : 'Phone Number',
                      prefixIcon:
                          const Icon(Icons.phone, color: AppTheme.primaryGold),
                      hintText: LocalizationService.isArabic
                          ? 'أدخل رقم هاتفك'
                          : 'Enter your phone number',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return LocalizationService.isArabic
                            ? 'يرجى إدخال رقم الهاتف'
                            : 'Please enter your phone number';
                      }
                      if (value.length < 10) {
                        return LocalizationService.isArabic
                            ? 'رقم الهاتف غير صحيح'
                            : 'Invalid phone number';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 20),

                  // Password Field
                  TextFormField(
                    controller: _passwordController,
                    obscureText: _obscurePassword,
                    decoration: InputDecoration(
                      labelText: l10n.password,
                      prefixIcon:
                          const Icon(Icons.lock, color: AppTheme.primaryGold),
                      suffixIcon: IconButton(
                        icon: Icon(
                          _obscurePassword
                              ? Icons.visibility
                              : Icons.visibility_off,
                          color: AppTheme.primaryGold,
                        ),
                        onPressed: () {
                          setState(() {
                            _obscurePassword = !_obscurePassword;
                          });
                        },
                      ),
                      hintText: LocalizationService.isArabic
                          ? 'أدخل كلمة مرور قوية'
                          : 'Enter a strong password',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return l10n.passwordRequired;
                      }
                      if (value.length < 8) {
                        return LocalizationService.isArabic
                            ? 'كلمة المرور يجب أن تكون 8 أحرف على الأقل'
                            : 'Password must be at least 8 characters';
                      }
                      if (!RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)')
                          .hasMatch(value)) {
                        return LocalizationService.isArabic
                            ? 'كلمة المرور يجب أن تحتوي على أحرف كبيرة وصغيرة وأرقام'
                            : 'Password must contain uppercase, lowercase and numbers';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 20),

                  // Confirm Password Field
                  TextFormField(
                    controller: _confirmPasswordController,
                    obscureText: _obscureConfirmPassword,
                    decoration: InputDecoration(
                      labelText: LocalizationService.isArabic
                          ? 'تأكيد كلمة المرور'
                          : 'Confirm Password',
                      prefixIcon: const Icon(Icons.lock_outline,
                          color: AppTheme.primaryGold),
                      suffixIcon: IconButton(
                        icon: Icon(
                          _obscureConfirmPassword
                              ? Icons.visibility
                              : Icons.visibility_off,
                          color: AppTheme.primaryGold,
                        ),
                        onPressed: () {
                          setState(() {
                            _obscureConfirmPassword = !_obscureConfirmPassword;
                          });
                        },
                      ),
                      hintText: LocalizationService.isArabic
                          ? 'أعد إدخال كلمة المرور'
                          : 'Re-enter your password',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return LocalizationService.isArabic
                            ? 'يرجى تأكيد كلمة المرور'
                            : 'Please confirm your password';
                      }
                      if (value != _passwordController.text) {
                        return LocalizationService.isArabic
                            ? 'كلمة المرور غير متطابقة'
                            : 'Passwords do not match';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 30),

                  // Terms and Conditions
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppTheme.cardBackground.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppTheme.primaryGold.withOpacity(0.3),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: AppTheme.primaryGold,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              LocalizationService.isArabic
                                  ? 'متطلبات المدرب'
                                  : 'Trainer Requirements',
                              style: const TextStyle(
                                color: AppTheme.primaryGold,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          LocalizationService.isArabic
                              ? '• شهادة معتمدة في التدريب الرياضي\n• خبرة لا تقل عن سنة واحدة\n• الالتزام بمعايير الجودة والمهنية'
                              : '• Certified fitness training qualification\n• Minimum 1 year of experience\n• Commitment to quality and professionalism',
                          style: const TextStyle(
                            color: AppTheme.textSecondary,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 30),

                  // Register Button
                  ElevatedButton(
                    onPressed: _isLoading ? null : _signUp,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator(color: Colors.black)
                        : Text(
                            LocalizationService.isArabic
                                ? 'إنشاء حساب المدرب'
                                : 'Create Trainer Account',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                  const SizedBox(height: 30),

                  // Login Link
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        LocalizationService.isArabic
                            ? 'لديك حساب بالفعل؟ '
                            : 'Already have an account? ',
                        style: const TextStyle(color: AppTheme.textSecondary),
                      ),
                      GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: Text(
                          l10n.login,
                          style: const TextStyle(
                            color: AppTheme.primaryGold,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
