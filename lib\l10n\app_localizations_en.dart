import 'app_localizations.dart';

class AppLocalizationsEn extends AppLocalizations {
  @override
  String get appName => 'FitGold';
  
  @override
  String get appSlogan => 'Golden Fitness';
  
  @override
  String get trainerApp => 'Trainer App';
  
  // Authentication
  @override
  String get welcome => 'Welcome to FitGold for Trainers';
  
  @override
  String get login => 'Sign In';
  
  @override
  String get register => 'Sign Up';
  
  @override
  String get email => 'Email';
  
  @override
  String get password => 'Password';
  
  @override
  String get confirmPassword => 'Confirm Password';
  
  @override
  String get fullName => 'Full Name';
  
  @override
  String get forgotPassword => 'Forgot Password?';
  
  @override
  String get dontHaveAccount => "Don't have an account? ";
  
  @override
  String get alreadyHaveAccount => 'Already have an account? ';
  
  @override
  String get createAccount => 'Create New Account';
  
  @override
  String get signInWithGoogle => 'Sign in with Google';
  
  @override
  String get or => 'OR';
  
  @override
  String get signOut => 'Sign Out';
  
  // Navigation
  @override
  String get dashboard => 'Dashboard';
  
  @override
  String get trainees => 'Trainees';
  
  @override
  String get sessions => 'Sessions';
  
  @override
  String get plans => 'Plans';
  
  @override
  String get profile => 'Profile';
  
  @override
  String get notifications => 'Notifications';
  
  @override
  String get chat => 'Chat';
  
  @override
  String get settings => 'Settings';
  
  @override
  String get analytics => 'Analytics';
  
  @override
  String get schedule => 'Schedule';
  
  // Trainer Profile Setup
  @override
  String get trainerSetup => 'Trainer Profile Setup';
  
  @override
  String get professionalInfo => 'Professional Information';
  
  @override
  String get specialization => 'Specialization';
  
  @override
  String get experience => 'Years of Experience';
  
  @override
  String get certifications => 'Certifications';
  
  @override
  String get bio => 'About You';
  
  @override
  String get pricing => 'Pricing';
  
  @override
  String get pricePerSession => 'Price per Session';
  
  @override
  String get pricePerMonth => 'Monthly Price';
  
  @override
  String get availability => 'Availability';
  
  @override
  String get languages => 'Languages';
  
  @override
  String get saveProfile => 'Save Profile';
  
  // Dashboard
  @override
  String get welcomeTrainer => 'Welcome, Trainer';
  
  @override
  String get todayOverview => "Today's Overview";
  
  @override
  String get totalTrainees => 'Total Trainees';
  
  @override
  String get activeTrainees => 'Active Trainees';
  
  @override
  String get todaySessions => "Today's Sessions";
  
  @override
  String get monthlyRevenue => 'Monthly Revenue';
  
  @override
  String get averageRating => 'Average Rating';
  
  @override
  String get totalReviews => 'Total Reviews';
  
  @override
  String get quickActions => 'Quick Actions';
  
  @override
  String get addPlan => 'Add Plan';
  
  @override
  String get scheduleSession => 'Schedule Session';
  
  @override
  String get viewAnalytics => 'View Analytics';
  
  @override
  String get manageTrainees => 'Manage Trainees';
  
  // Trainees Management
  @override
  String get myTrainees => 'My Trainees';
  
  @override
  String get pendingRequests => 'Pending Requests';
  
  @override
  String get allTrainees => 'All Trainees';
  
  @override
  String get traineeProfile => 'Trainee Profile';
  
  @override
  String get assignPlan => 'Assign Plan';
  
  @override
  String get viewProgress => 'View Progress';
  
  @override
  String get sendMessage => 'Send Message';
  
  @override
  String get approve => 'Approve';
  
  @override
  String get reject => 'Reject';
  
  @override
  String get noTrainees => 'No trainees yet';
  
  @override
  String get noRequests => 'No pending requests';
  
  // Sessions Management
  @override
  String get mySessions => 'My Sessions';
  
  @override
  String get upcoming => 'Upcoming';
  
  @override
  String get completed => 'Completed';
  
  @override
  String get cancelled => 'Cancelled';
  
  @override
  String get rescheduleRequested => 'Reschedule Requested';
  
  @override
  String get sessionDetails => 'Session Details';
  
  @override
  String get markCompleted => 'Mark as Completed';
  
  @override
  String get addNotes => 'Add Notes';
  
  @override
  String get reschedule => 'Reschedule';
  
  @override
  String get cancelSession => 'Cancel Session';
  
  @override
  String get sessionNotes => 'Session Notes';
  
  @override
  String get traineeAttended => 'Trainee Attended';
  
  @override
  String get sessionRating => 'Session Rating';
  
  // Plans Management
  @override
  String get nutritionPlans => 'Nutrition Plans';
  
  @override
  String get workoutPlans => 'Workout Plans';
  
  @override
  String get createPlan => 'Create Plan';
  
  @override
  String get editPlan => 'Edit Plan';
  
  @override
  String get assignToTrainee => 'Assign to Trainee';
  
  @override
  String get planTitle => 'Plan Title';
  
  @override
  String get planDescription => 'Plan Description';
  
  @override
  String get startDate => 'Start Date';
  
  @override
  String get endDate => 'End Date';
  
  @override
  String get dailyCalories => 'Daily Calories';
  
  @override
  String get macros => 'Macros';
  
  @override
  String get protein => 'Protein';
  
  @override
  String get carbs => 'Carbs';
  
  @override
  String get fats => 'Fats';
  
  @override
  String get meals => 'Meals';
  
  @override
  String get addMeal => 'Add Meal';
  
  @override
  String get exercises => 'Exercises';
  
  @override
  String get addExercise => 'Add Exercise';
  
  @override
  String get sets => 'Sets';
  
  @override
  String get reps => 'Reps';
  
  @override
  String get weight => 'Weight';
  
  @override
  String get restTime => 'Rest Time';
  
  @override
  String get instructions => 'Instructions';
  
  // Analytics
  @override
  String get performanceAnalytics => 'Performance Analytics';
  
  @override
  String get revenue => 'Revenue';
  
  @override
  String get sessionsCompleted => 'Sessions Completed';
  
  @override
  String get clientSatisfaction => 'Client Satisfaction';
  
  @override
  String get monthlyStats => 'Monthly Stats';
  
  @override
  String get weeklyStats => 'Weekly Stats';
  
  @override
  String get topPerformingPlans => 'Top Performing Plans';
  
  @override
  String get clientProgress => 'Client Progress';
  
  @override
  String get revenueGrowth => 'Revenue Growth';
  
  @override
  String get sessionTrends => 'Session Trends';
  
  // Schedule
  @override
  String get mySchedule => 'My Schedule';
  
  @override
  String get addAvailability => 'Add Availability';
  
  @override
  String get timeSlot => 'Time Slot';
  
  @override
  String get available => 'Available';
  
  @override
  String get booked => 'Booked';
  
  @override
  String get blocked => 'Blocked';
  
  @override
  String get setAvailable => 'Set Available';
  
  @override
  String get blockTime => 'Block Time';
  
  @override
  String get workingHours => 'Working Hours';
  
  @override
  String get breakTime => 'Break Time';
  
  // Notifications
  @override
  String get myNotifications => 'My Notifications';
  
  @override
  String get markAllAsRead => 'Mark All as Read';
  
  @override
  String get noNotifications => 'No notifications';
  
  @override
  String get newTraineeRequest => 'New Trainee Request';
  
  @override
  String get sessionBooked => 'Session Booked';
  
  @override
  String get sessionCancelled => 'Session Cancelled';
  
  @override
  String get paymentReceived => 'Payment Received';
  
  @override
  String get reviewReceived => 'Review Received';
  
  // Chat
  @override
  String get messages => 'Messages';
  
  @override
  String get typeMessage => 'Type a message...';
  
  @override
  String get send => 'Send';
  
  @override
  String get online => 'Online';
  
  @override
  String get offline => 'Offline';
  
  @override
  String get selectTrainee => 'Select Trainee';
  
  // Common
  @override
  String get save => 'Save';
  
  @override
  String get cancel => 'Cancel';
  
  @override
  String get delete => 'Delete';
  
  @override
  String get edit => 'Edit';
  
  @override
  String get update => 'Update';
  
  @override
  String get create => 'Create';
  
  @override
  String get assign => 'Assign';
  
  @override
  String get view => 'View';
  
  @override
  String get loading => 'Loading...';
  
  @override
  String get error => 'Error';
  
  @override
  String get success => 'Success';
  
  @override
  String get retry => 'Retry';
  
  @override
  String get ok => 'OK';
  
  @override
  String get yes => 'Yes';
  
  @override
  String get no => 'No';
  
  @override
  String get required => 'Required';
  
  @override
  String get optional => 'Optional';
  
  @override
  String get kg => 'kg';
  
  @override
  String get cm => 'cm';
  
  @override
  String get years => 'years';
  
  @override
  String get minutes => 'minutes';
  
  @override
  String get hours => 'hours';
  
  @override
  String get days => 'days';
  
  @override
  String get weeks => 'weeks';
  
  @override
  String get months => 'months';
  
  @override
  String get sar => 'SAR';
  
  @override
  String get search => 'Search';
  
  @override
  String get filter => 'Filter';
  
  @override
  String get sort => 'Sort';
  
  @override
  String get all => 'All';
  
  @override
  String get active => 'Active';
  
  @override
  String get inactive => 'Inactive';
  
  @override
  String get pending => 'Pending';
  
  // Validation Messages
  @override
  String get emailRequired => 'Please enter your email';
  
  @override
  String get emailInvalid => 'Please enter a valid email';
  
  @override
  String get passwordRequired => 'Please enter your password';
  
  @override
  String get passwordTooShort => 'Password must be at least 6 characters';
  
  @override
  String get passwordsNotMatch => 'Passwords do not match';
  
  @override
  String get nameRequired => 'Please enter your full name';
  
  @override
  String get titleRequired => 'Please enter a title';
  
  @override
  String get descriptionRequired => 'Please enter a description';
  
  @override
  String get priceRequired => 'Please enter a price';
  
  @override
  String get priceInvalid => 'Invalid price';
  
  // Success Messages
  @override
  String get profileUpdated => 'Profile updated successfully';
  
  @override
  String get planCreated => 'Plan created successfully';
  
  @override
  String get planAssigned => 'Plan assigned successfully';
  
  @override
  String get sessionScheduled => 'Session scheduled successfully';
  
  @override
  String get sessionCompleted => 'Session marked as completed';
  
  @override
  String get traineeApproved => 'Trainee approved successfully';
  
  @override
  String get notificationsSent => 'Notifications sent';
  
  // Error Messages
  @override
  String get unexpectedError => 'An unexpected error occurred';
  
  @override
  String get networkError => 'Network connection error';
  
  @override
  String get authError => 'Authentication error';
  
  @override
  String get permissionDenied => 'Permission denied';
  
  @override
  String get planNotFound => 'Plan not found';
  
  @override
  String get traineeNotFound => 'Trainee not found';
  
  @override
  String get sessionNotFound => 'Session not found';
  
  // Settings
  @override
  String get language => 'Language';
  
  @override
  String get arabic => 'العربية';
  
  @override
  String get english => 'English';
  
  @override
  String get changeLanguage => 'Change Language';
  
  @override
  String get privacySecurity => 'Privacy & Security';
  
  @override
  String get helpSupport => 'Help & Support';
  
  @override
  String get aboutApp => 'About App';
  
  @override
  String get version => 'Version';
  
  @override
  String get accountSettings => 'Account Settings';
  
  @override
  String get businessSettings => 'Business Settings';
  
  @override
  String get paymentSettings => 'Payment Settings';
  
  // Specializations
  @override
  String get weightLoss => 'Weight Loss';
  
  @override
  String get muscleBuilding => 'Muscle Building';
  
  @override
  String get cardioTraining => 'Cardio Training';
  
  @override
  String get sportsNutrition => 'Sports Nutrition';
  
  @override
  String get rehabilitation => 'Rehabilitation';
  
  @override
  String get generalFitness => 'General Fitness';
  
  @override
  String get seniorTraining => 'Senior Training';
  
  @override
  String get youthTraining => 'Youth Training';
  
  // Time Ago
  @override
  String get now => 'Now';
  
  @override
  String get minuteAgo => '1 minute ago';
  
  @override
  String get minutesAgo => 'minutes ago';
  
  @override
  String get hourAgo => '1 hour ago';
  
  @override
  String get hoursAgo => 'hours ago';
  
  @override
  String get dayAgo => '1 day ago';
  
  @override
  String get daysAgo => 'days ago';
  
  @override
  String get weekAgo => '1 week ago';
  
  @override
  String get weeksAgo => 'weeks ago';
  
  @override
  String get monthAgo => '1 month ago';
  
  @override
  String get monthsAgo => 'months ago';
  
  @override
  String get yearAgo => '1 year ago';
  
  @override
  String get yearsAgo => 'years ago';
}
