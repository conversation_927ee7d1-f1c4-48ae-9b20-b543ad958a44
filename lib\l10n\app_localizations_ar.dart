import 'app_localizations.dart';

class AppLocalizationsAr extends AppLocalizations {
  @override
  String get appName => 'FitGold';
  
  @override
  String get appSlogan => 'رشاقة ذهبية';
  
  @override
  String get trainerApp => 'تطبيق المدرب';
  
  // Authentication
  @override
  String get welcome => 'مرحباً بك في FitGold للمدربين';
  
  @override
  String get login => 'تسجيل الدخول';
  
  @override
  String get register => 'إنشاء حساب';
  
  @override
  String get email => 'البريد الإلكتروني';
  
  @override
  String get password => 'كلمة المرور';
  
  @override
  String get confirmPassword => 'تأكيد كلمة المرور';
  
  @override
  String get fullName => 'الاسم الكامل';
  
  @override
  String get forgotPassword => 'نسيت كلمة المرور؟';
  
  @override
  String get dontHaveAccount => 'ليس لديك حساب؟ ';
  
  @override
  String get alreadyHaveAccount => 'لديك حساب بالفعل؟ ';
  
  @override
  String get createAccount => 'إنشاء حساب جديد';
  
  @override
  String get signInWithGoogle => 'تسجيل الدخول بـ Google';
  
  @override
  String get or => 'أو';
  
  @override
  String get signOut => 'تسجيل الخروج';
  
  // Navigation
  @override
  String get dashboard => 'لوحة التحكم';
  
  @override
  String get trainees => 'المتدربين';
  
  @override
  String get sessions => 'الجلسات';
  
  @override
  String get plans => 'الخطط';
  
  @override
  String get profile => 'الملف الشخصي';
  
  @override
  String get notifications => 'الإشعارات';
  
  @override
  String get chat => 'المحادثات';
  
  @override
  String get settings => 'الإعدادات';
  
  @override
  String get analytics => 'التحليلات';
  
  @override
  String get schedule => 'الجدولة';
  
  // Trainer Profile Setup
  @override
  String get trainerSetup => 'إعداد ملف المدرب';
  
  @override
  String get professionalInfo => 'المعلومات المهنية';
  
  @override
  String get specialization => 'التخصص';
  
  @override
  String get experience => 'سنوات الخبرة';
  
  @override
  String get certifications => 'الشهادات';
  
  @override
  String get bio => 'نبذة عنك';
  
  @override
  String get pricing => 'التسعير';
  
  @override
  String get pricePerSession => 'سعر الجلسة';
  
  @override
  String get pricePerMonth => 'السعر الشهري';
  
  @override
  String get availability => 'أوقات العمل';
  
  @override
  String get languages => 'اللغات';
  
  @override
  String get saveProfile => 'حفظ الملف الشخصي';
  
  // Dashboard
  @override
  String get welcomeTrainer => 'مرحباً أيها المدرب';
  
  @override
  String get todayOverview => 'نظرة عامة على اليوم';
  
  @override
  String get totalTrainees => 'إجمالي المتدربين';
  
  @override
  String get activeTrainees => 'المتدربين النشطين';
  
  @override
  String get todaySessions => 'جلسات اليوم';
  
  @override
  String get monthlyRevenue => 'الإيرادات الشهرية';
  
  @override
  String get averageRating => 'متوسط التقييم';
  
  @override
  String get totalReviews => 'إجمالي المراجعات';
  
  @override
  String get quickActions => 'الإجراءات السريعة';
  
  @override
  String get addPlan => 'إضافة خطة';
  
  @override
  String get scheduleSession => 'جدولة جلسة';
  
  @override
  String get viewAnalytics => 'عرض التحليلات';
  
  @override
  String get manageTrainees => 'إدارة المتدربين';
  
  // Trainees Management
  @override
  String get myTrainees => 'متدربيني';
  
  @override
  String get pendingRequests => 'الطلبات المعلقة';
  
  @override
  String get allTrainees => 'جميع المتدربين';
  
  @override
  String get traineeProfile => 'ملف المتدرب';
  
  @override
  String get assignPlan => 'تعيين خطة';
  
  @override
  String get viewProgress => 'عرض التقدم';
  
  @override
  String get sendMessage => 'إرسال رسالة';
  
  @override
  String get approve => 'موافقة';
  
  @override
  String get reject => 'رفض';
  
  @override
  String get noTrainees => 'لا يوجد متدربين';
  
  @override
  String get noRequests => 'لا توجد طلبات';
  
  // Sessions Management
  @override
  String get mySessions => 'جلساتي';
  
  @override
  String get upcoming => 'القادمة';
  
  @override
  String get completed => 'المكتملة';
  
  @override
  String get cancelled => 'الملغية';
  
  @override
  String get rescheduleRequested => 'طلب إعادة جدولة';
  
  @override
  String get sessionDetails => 'تفاصيل الجلسة';
  
  @override
  String get markCompleted => 'تحديد كمكتملة';
  
  @override
  String get addNotes => 'إضافة ملاحظات';
  
  @override
  String get reschedule => 'إعادة جدولة';
  
  @override
  String get cancelSession => 'إلغاء الجلسة';
  
  @override
  String get sessionNotes => 'ملاحظات الجلسة';
  
  @override
  String get traineeAttended => 'حضر المتدرب';
  
  @override
  String get sessionRating => 'تقييم الجلسة';
  
  // Plans Management
  @override
  String get nutritionPlans => 'الخطط الغذائية';
  
  @override
  String get workoutPlans => 'الخطط الرياضية';
  
  @override
  String get createPlan => 'إنشاء خطة';
  
  @override
  String get editPlan => 'تعديل الخطة';
  
  @override
  String get assignToTrainee => 'تعيين للمتدرب';
  
  @override
  String get planTitle => 'عنوان الخطة';
  
  @override
  String get planDescription => 'وصف الخطة';
  
  @override
  String get startDate => 'تاريخ البداية';
  
  @override
  String get endDate => 'تاريخ النهاية';
  
  @override
  String get dailyCalories => 'السعرات اليومية';
  
  @override
  String get macros => 'المغذيات الكبرى';
  
  @override
  String get protein => 'البروتين';
  
  @override
  String get carbs => 'الكربوهيدرات';
  
  @override
  String get fats => 'الدهون';
  
  @override
  String get meals => 'الوجبات';
  
  @override
  String get addMeal => 'إضافة وجبة';
  
  @override
  String get exercises => 'التمارين';
  
  @override
  String get addExercise => 'إضافة تمرين';
  
  @override
  String get sets => 'مجموعات';
  
  @override
  String get reps => 'تكرارات';
  
  @override
  String get weight => 'الوزن';
  
  @override
  String get restTime => 'وقت الراحة';
  
  @override
  String get instructions => 'التعليمات';
  
  // Analytics
  @override
  String get performanceAnalytics => 'تحليلات الأداء';
  
  @override
  String get revenue => 'الإيرادات';
  
  @override
  String get sessionsCompleted => 'الجلسات المكتملة';
  
  @override
  String get clientSatisfaction => 'رضا العملاء';
  
  @override
  String get monthlyStats => 'إحصائيات شهرية';
  
  @override
  String get weeklyStats => 'إحصائيات أسبوعية';
  
  @override
  String get topPerformingPlans => 'أفضل الخطط أداءً';
  
  @override
  String get clientProgress => 'تقدم العملاء';
  
  @override
  String get revenueGrowth => 'نمو الإيرادات';
  
  @override
  String get sessionTrends => 'اتجاهات الجلسات';
  
  // Schedule
  @override
  String get mySchedule => 'جدولي';
  
  @override
  String get addAvailability => 'إضافة توفر';
  
  @override
  String get timeSlot => 'الفترة الزمنية';
  
  @override
  String get available => 'متاح';
  
  @override
  String get booked => 'محجوز';
  
  @override
  String get blocked => 'محظور';
  
  @override
  String get setAvailable => 'تحديد كمتاح';
  
  @override
  String get blockTime => 'حظر الوقت';
  
  @override
  String get workingHours => 'ساعات العمل';
  
  @override
  String get breakTime => 'وقت الاستراحة';
  
  // Notifications
  @override
  String get myNotifications => 'إشعاراتي';
  
  @override
  String get markAllAsRead => 'تحديد الكل كمقروء';
  
  @override
  String get noNotifications => 'لا توجد إشعارات';
  
  @override
  String get newTraineeRequest => 'طلب متدرب جديد';
  
  @override
  String get sessionBooked => 'جلسة محجوزة';
  
  @override
  String get sessionCancelled => 'جلسة ملغية';
  
  @override
  String get paymentReceived => 'دفعة مستلمة';
  
  @override
  String get reviewReceived => 'مراجعة مستلمة';
  
  // Chat
  @override
  String get messages => 'الرسائل';
  
  @override
  String get typeMessage => 'اكتب رسالة...';
  
  @override
  String get send => 'إرسال';
  
  @override
  String get online => 'متصل';
  
  @override
  String get offline => 'غير متصل';
  
  @override
  String get selectTrainee => 'اختر متدرب';
  
  // Common
  @override
  String get save => 'حفظ';
  
  @override
  String get cancel => 'إلغاء';
  
  @override
  String get delete => 'حذف';
  
  @override
  String get edit => 'تعديل';
  
  @override
  String get update => 'تحديث';
  
  @override
  String get create => 'إنشاء';
  
  @override
  String get assign => 'تعيين';
  
  @override
  String get view => 'عرض';
  
  @override
  String get loading => 'جاري التحميل...';
  
  @override
  String get error => 'خطأ';
  
  @override
  String get success => 'نجح';
  
  @override
  String get retry => 'إعادة المحاولة';
  
  @override
  String get ok => 'حسناً';
  
  @override
  String get yes => 'نعم';
  
  @override
  String get no => 'لا';
  
  @override
  String get required => 'مطلوب';
  
  @override
  String get optional => 'اختياري';
  
  @override
  String get kg => 'كجم';
  
  @override
  String get cm => 'سم';
  
  @override
  String get years => 'سنة';
  
  @override
  String get minutes => 'دقيقة';
  
  @override
  String get hours => 'ساعة';
  
  @override
  String get days => 'يوم';
  
  @override
  String get weeks => 'أسبوع';
  
  @override
  String get months => 'شهر';
  
  @override
  String get sar => 'ريال';
  
  @override
  String get search => 'بحث';
  
  @override
  String get filter => 'فلترة';
  
  @override
  String get sort => 'ترتيب';
  
  @override
  String get all => 'الكل';
  
  @override
  String get active => 'نشط';
  
  @override
  String get inactive => 'غير نشط';
  
  @override
  String get pending => 'معلق';
  
  // Validation Messages
  @override
  String get emailRequired => 'يرجى إدخال البريد الإلكتروني';
  
  @override
  String get emailInvalid => 'يرجى إدخال بريد إلكتروني صحيح';
  
  @override
  String get passwordRequired => 'يرجى إدخال كلمة المرور';
  
  @override
  String get passwordTooShort => 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
  
  @override
  String get passwordsNotMatch => 'كلمة المرور غير متطابقة';
  
  @override
  String get nameRequired => 'يرجى إدخال الاسم الكامل';
  
  @override
  String get titleRequired => 'يرجى إدخال العنوان';
  
  @override
  String get descriptionRequired => 'يرجى إدخال الوصف';
  
  @override
  String get priceRequired => 'يرجى إدخال السعر';
  
  @override
  String get priceInvalid => 'سعر غير صحيح';
  
  // Success Messages
  @override
  String get profileUpdated => 'تم تحديث الملف الشخصي بنجاح';
  
  @override
  String get planCreated => 'تم إنشاء الخطة بنجاح';
  
  @override
  String get planAssigned => 'تم تعيين الخطة بنجاح';
  
  @override
  String get sessionScheduled => 'تم جدولة الجلسة بنجاح';
  
  @override
  String get sessionCompleted => 'تم تحديد الجلسة كمكتملة';
  
  @override
  String get traineeApproved => 'تم قبول المتدرب بنجاح';
  
  @override
  String get notificationsSent => 'تم إرسال الإشعارات';
  
  // Error Messages
  @override
  String get unexpectedError => 'حدث خطأ غير متوقع';
  
  @override
  String get networkError => 'خطأ في الاتصال بالشبكة';
  
  @override
  String get authError => 'خطأ في المصادقة';
  
  @override
  String get permissionDenied => 'تم رفض الإذن';
  
  @override
  String get planNotFound => 'الخطة غير موجودة';
  
  @override
  String get traineeNotFound => 'المتدرب غير موجود';
  
  @override
  String get sessionNotFound => 'الجلسة غير موجودة';
  
  // Settings
  @override
  String get language => 'اللغة';
  
  @override
  String get arabic => 'العربية';
  
  @override
  String get english => 'English';
  
  @override
  String get changeLanguage => 'تغيير اللغة';
  
  @override
  String get privacySecurity => 'الخصوصية والأمان';
  
  @override
  String get helpSupport => 'المساعدة والدعم';
  
  @override
  String get aboutApp => 'حول التطبيق';
  
  @override
  String get version => 'الإصدار';
  
  @override
  String get accountSettings => 'إعدادات الحساب';
  
  @override
  String get businessSettings => 'إعدادات العمل';
  
  @override
  String get paymentSettings => 'إعدادات الدفع';
  
  // Specializations
  @override
  String get weightLoss => 'خسارة الوزن';
  
  @override
  String get muscleBuilding => 'بناء العضل';
  
  @override
  String get cardioTraining => 'تمارين القلب';
  
  @override
  String get sportsNutrition => 'التغذية الرياضية';
  
  @override
  String get rehabilitation => 'إعادة التأهيل';
  
  @override
  String get generalFitness => 'اللياقة العامة';
  
  @override
  String get seniorTraining => 'تدريب كبار السن';
  
  @override
  String get youthTraining => 'تدريب الأطفال';
  
  // Time Ago
  @override
  String get now => 'الآن';
  
  @override
  String get minuteAgo => 'منذ دقيقة';
  
  @override
  String get minutesAgo => 'منذ دقائق';
  
  @override
  String get hourAgo => 'منذ ساعة';
  
  @override
  String get hoursAgo => 'منذ ساعات';
  
  @override
  String get dayAgo => 'منذ يوم';
  
  @override
  String get daysAgo => 'منذ أيام';
  
  @override
  String get weekAgo => 'منذ أسبوع';
  
  @override
  String get weeksAgo => 'منذ أسابيع';
  
  @override
  String get monthAgo => 'منذ شهر';
  
  @override
  String get monthsAgo => 'منذ أشهر';
  
  @override
  String get yearAgo => 'منذ سنة';
  
  @override
  String get yearsAgo => 'منذ سنوات';
}
