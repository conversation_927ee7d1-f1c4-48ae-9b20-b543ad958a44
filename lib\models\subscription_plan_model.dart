class SubscriptionPlanModel {
  final String id;
  final String trainerId;
  final String name;
  final String? description;
  final int durationDays;
  final double price;
  final List<String> features;
  final int sessionCount; // 0 means unlimited
  final bool nutritionPlanIncluded;
  final bool workoutPlanIncluded;
  final bool chatSupport;
  final bool videoCalls;
  final bool progressTracking;
  final bool isActive;
  final int sortOrder;
  final DateTime createdAt;
  final DateTime updatedAt;

  SubscriptionPlanModel({
    required this.id,
    required this.trainerId,
    required this.name,
    this.description,
    required this.durationDays,
    required this.price,
    this.features = const [],
    this.sessionCount = 0,
    this.nutritionPlanIncluded = false,
    this.workoutPlanIncluded = true,
    this.chatSupport = true,
    this.videoCalls = false,
    this.progressTracking = true,
    this.isActive = true,
    this.sortOrder = 0,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SubscriptionPlanModel.fromJson(Map<String, dynamic> json) {
    return SubscriptionPlanModel(
      id: json['id'] as String,
      trainerId: json['trainer_id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      durationDays: json['duration_days'] as int,
      price: (json['price'] as num).toDouble(),
      features: json['features'] != null 
          ? List<String>.from(json['features'] as List)
          : [],
      sessionCount: json['session_count'] as int? ?? 0,
      nutritionPlanIncluded: json['nutrition_plan_included'] as bool? ?? false,
      workoutPlanIncluded: json['workout_plan_included'] as bool? ?? true,
      chatSupport: json['chat_support'] as bool? ?? true,
      videoCalls: json['video_calls'] as bool? ?? false,
      progressTracking: json['progress_tracking'] as bool? ?? true,
      isActive: json['is_active'] as bool? ?? true,
      sortOrder: json['sort_order'] as int? ?? 0,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trainer_id': trainerId,
      'name': name,
      'description': description,
      'duration_days': durationDays,
      'price': price,
      'features': features,
      'session_count': sessionCount,
      'nutrition_plan_included': nutritionPlanIncluded,
      'workout_plan_included': workoutPlanIncluded,
      'chat_support': chatSupport,
      'video_calls': videoCalls,
      'progress_tracking': progressTracking,
      'is_active': isActive,
      'sort_order': sortOrder,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Map<String, dynamic> toInsertJson() {
    return {
      'trainer_id': trainerId,
      'name': name,
      'description': description,
      'duration_days': durationDays,
      'price': price,
      'features': features,
      'session_count': sessionCount,
      'nutrition_plan_included': nutritionPlanIncluded,
      'workout_plan_included': workoutPlanIncluded,
      'chat_support': chatSupport,
      'video_calls': videoCalls,
      'progress_tracking': progressTracking,
      'is_active': isActive,
      'sort_order': sortOrder,
    };
  }

  SubscriptionPlanModel copyWith({
    String? id,
    String? trainerId,
    String? name,
    String? description,
    int? durationDays,
    double? price,
    List<String>? features,
    int? sessionCount,
    bool? nutritionPlanIncluded,
    bool? workoutPlanIncluded,
    bool? chatSupport,
    bool? videoCalls,
    bool? progressTracking,
    bool? isActive,
    int? sortOrder,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SubscriptionPlanModel(
      id: id ?? this.id,
      trainerId: trainerId ?? this.trainerId,
      name: name ?? this.name,
      description: description ?? this.description,
      durationDays: durationDays ?? this.durationDays,
      price: price ?? this.price,
      features: features ?? this.features,
      sessionCount: sessionCount ?? this.sessionCount,
      nutritionPlanIncluded: nutritionPlanIncluded ?? this.nutritionPlanIncluded,
      workoutPlanIncluded: workoutPlanIncluded ?? this.workoutPlanIncluded,
      chatSupport: chatSupport ?? this.chatSupport,
      videoCalls: videoCalls ?? this.videoCalls,
      progressTracking: progressTracking ?? this.progressTracking,
      isActive: isActive ?? this.isActive,
      sortOrder: sortOrder ?? this.sortOrder,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get nameInArabic {
    switch (name) {
      case 'Basic':
        return 'بازيك';
      case 'Standard':
        return 'ستاندرد';
      case 'Premium':
        return 'بريميوم';
      case 'VIP':
        return 'في آي بي';
      case 'Custom':
        return 'مخصص';
      default:
        return name;
    }
  }

  String get durationDisplayName {
    if (durationDays == 7) return 'أسبوع واحد';
    if (durationDays == 30) return 'شهر واحد';
    if (durationDays == 90) return '3 أشهر';
    if (durationDays == 180) return '6 أشهر';
    if (durationDays == 365) return 'سنة واحدة';
    return '$durationDays يوم';
  }

  String get sessionCountDisplayName {
    if (sessionCount == 0) return 'جلسات غير محدودة';
    return '$sessionCount جلسة';
  }

  List<String> get featuresInArabic {
    return features.map((feature) {
      switch (feature.toLowerCase()) {
        case 'unlimited sessions':
          return 'جلسات غير محدودة';
        case 'nutrition plan':
          return 'خطة تغذية';
        case 'workout plan':
          return 'خطة تمارين';
        case 'chat support':
          return 'دعم المحادثة';
        case 'video calls':
          return 'مكالمات فيديو';
        case 'progress tracking':
          return 'تتبع التقدم';
        case 'meal planning':
          return 'تخطيط الوجبات';
        case 'supplement guidance':
          return 'إرشادات المكملات';
        case 'weekly check-ins':
          return 'متابعة أسبوعية';
        case 'custom workouts':
          return 'تمارين مخصصة';
        case 'priority support':
          return 'دعم أولوية';
        case '24/7 support':
          return 'دعم 24/7';
        default:
          return feature;
      }
    }).toList();
  }
}
