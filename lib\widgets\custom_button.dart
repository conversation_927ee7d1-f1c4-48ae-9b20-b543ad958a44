import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonStyle? style;
  final Widget? icon;
  final bool isLoading;
  final bool isOutlined;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final EdgeInsetsGeometry? padding;
  final Size? minimumSize;
  final BorderRadius? borderRadius;
  final double? elevation;
  final Gradient? gradient;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.style,
    this.icon,
    this.isLoading = false,
    this.isOutlined = false,
    this.backgroundColor,
    this.foregroundColor,
    this.padding,
    this.minimumSize,
    this.borderRadius,
    this.elevation,
    this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    if (gradient != null) {
      return _buildGradientButton();
    }

    if (isOutlined) {
      return _buildOutlinedButton();
    }

    return _buildElevatedButton();
  }

  Widget _buildElevatedButton() {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: style ??
          ElevatedButton.styleFrom(
            backgroundColor: backgroundColor ?? AppTheme.primaryGold,
            foregroundColor: foregroundColor ?? Colors.black,
            padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            minimumSize: minimumSize ?? const Size(120, 48),
            elevation: elevation ?? 8,
            shadowColor: AppTheme.primaryGold.withValues(alpha: 0.3),
            shape: RoundedRectangleBorder(
              borderRadius: borderRadius ?? BorderRadius.circular(16),
            ),
            textStyle: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildOutlinedButton() {
    return OutlinedButton(
      onPressed: isLoading ? null : onPressed,
      style: style ??
          OutlinedButton.styleFrom(
            foregroundColor: foregroundColor ?? AppTheme.primaryGold,
            padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            minimumSize: minimumSize ?? const Size(120, 48),
            side: BorderSide(
              color: backgroundColor ?? AppTheme.primaryGold,
              width: 2,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: borderRadius ?? BorderRadius.circular(16),
            ),
            textStyle: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildGradientButton() {
    return Container(
      decoration: BoxDecoration(
        gradient: gradient,
        borderRadius: borderRadius ?? BorderRadius.circular(16),
        boxShadow: AppTheme.goldShadow,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isLoading ? null : onPressed,
          borderRadius: borderRadius ?? BorderRadius.circular(16),
          child: Container(
            padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            constraints: BoxConstraints(
              minWidth: minimumSize?.width ?? 120,
              minHeight: minimumSize?.height ?? 48,
            ),
            child: _buildButtonContent(),
          ),
        ),
      ),
    );
  }

  Widget _buildButtonContent() {
    if (isLoading) {
      return const SizedBox(
        height: 20,
        width: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
        ),
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          icon!,
          const SizedBox(width: 8),
          Text(text),
        ],
      );
    }

    return Text(text);
  }
}

class IconButtonCustom extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? iconColor;
  final double? size;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final bool showShadow;
  final Gradient? gradient;

  const IconButtonCustom({
    super.key,
    required this.icon,
    this.onPressed,
    this.backgroundColor,
    this.iconColor,
    this.size,
    this.padding,
    this.borderRadius,
    this.showShadow = true,
    this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    final borderRadiusValue = borderRadius ?? BorderRadius.circular(12);
    
    return Container(
      decoration: BoxDecoration(
        color: gradient == null ? (backgroundColor ?? AppTheme.surfaceColor) : null,
        gradient: gradient,
        borderRadius: borderRadiusValue,
        boxShadow: showShadow ? AppTheme.cardShadow : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: borderRadiusValue,
          child: Padding(
            padding: padding ?? const EdgeInsets.all(12),
            child: Icon(
              icon,
              color: iconColor ?? AppTheme.primaryGold,
              size: size ?? 24,
            ),
          ),
        ),
      ),
    );
  }
}

class FloatingActionButtonCustom extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final Color? backgroundColor;
  final double? elevation;
  final BorderRadius? borderRadius;
  final Gradient? gradient;

  const FloatingActionButtonCustom({
    super.key,
    this.onPressed,
    required this.child,
    this.backgroundColor,
    this.elevation,
    this.borderRadius,
    this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    final borderRadiusValue = borderRadius ?? BorderRadius.circular(16);
    
    return Container(
      decoration: BoxDecoration(
        color: gradient == null ? (backgroundColor ?? AppTheme.primaryGold) : null,
        gradient: gradient ?? AppTheme.goldGradient,
        borderRadius: borderRadiusValue,
        boxShadow: AppTheme.goldShadow,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: borderRadiusValue,
          child: Container(
            padding: const EdgeInsets.all(16),
            child: child,
          ),
        ),
      ),
    );
  }
}
