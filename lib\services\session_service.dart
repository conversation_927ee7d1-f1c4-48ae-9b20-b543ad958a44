import '../models/session_model.dart';
import 'database_service.dart';

class SessionService {
  static const String _tableName = 'sessions';
  static const String _packagesTableName = 'session_packages';

  // Session CRUD operations
  static Future<List<SessionModel>> getSessions({
    String? trainerId,
    String? traineeId,
    String? status,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  }) async {
    final filters = <String, dynamic>{};
    
    if (trainerId != null) filters['trainer_id'] = trainerId;
    if (traineeId != null) filters['trainee_id'] = traineeId;
    if (status != null) filters['status'] = status;

    var query = await DatabaseService.select(
      table: _tableName,
      filters: filters,
      orderBy: 'scheduled_at',
      ascending: false,
      limit: limit,
      offset: offset,
    );

    // Filter by date range if provided
    if (startDate != null || endDate != null) {
      query = query.where((session) {
        final scheduledAt = DateTime.parse(session['scheduled_at']);
        if (startDate != null && scheduledAt.isBefore(startDate)) return false;
        if (endDate != null && scheduledAt.isAfter(endDate)) return false;
        return true;
      }).toList();
    }

    return query.map((json) => SessionModel.fromJson(json)).toList();
  }

  static Future<SessionModel?> getSessionById(String sessionId) async {
    final result = await DatabaseService.selectSingle(
      table: _tableName,
      filters: {'id': sessionId},
    );

    return result != null ? SessionModel.fromJson(result) : null;
  }

  static Future<SessionModel> createSession(SessionModel session) async {
    final data = session.toJson();
    data.remove('id'); // Let database generate ID
    data['created_at'] = DateTime.now().toIso8601String();
    data['updated_at'] = DateTime.now().toIso8601String();

    final result = await DatabaseService.insert(
      table: _tableName,
      data: data,
    );

    return SessionModel.fromJson(result);
  }

  static Future<SessionModel> updateSession(SessionModel session) async {
    final data = session.toJson();
    data['updated_at'] = DateTime.now().toIso8601String();

    final result = await DatabaseService.update(
      table: _tableName,
      data: data,
      filters: {'id': session.id},
    );

    return SessionModel.fromJson(result);
  }

  static Future<void> deleteSession(String sessionId) async {
    await DatabaseService.delete(
      table: _tableName,
      filters: {'id': sessionId},
    );
  }

  // Session status operations
  static Future<SessionModel> updateSessionStatus(String sessionId, String status) async {
    final data = {
      'status': status,
      'updated_at': DateTime.now().toIso8601String(),
    };

    if (status == 'completed') {
      data['completed_at'] = DateTime.now().toIso8601String();
    }

    final result = await DatabaseService.update(
      table: _tableName,
      data: data,
      filters: {'id': sessionId},
    );

    return SessionModel.fromJson(result);
  }

  static Future<SessionModel> cancelSession(String sessionId, String reason) async {
    final data = {
      'status': 'cancelled',
      'cancellation_reason': reason,
      'cancelled_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };

    final result = await DatabaseService.update(
      table: _tableName,
      data: data,
      filters: {'id': sessionId},
    );

    return SessionModel.fromJson(result);
  }

  static Future<SessionModel> rescheduleSession(
    String sessionId,
    DateTime newDateTime,
    String reason,
  ) async {
    final session = await getSessionById(sessionId);
    if (session == null) throw Exception('Session not found');

    final data = {
      'scheduled_at': newDateTime.toIso8601String(),
      'rescheduled_from': session.scheduledAt.toIso8601String(),
      'reschedule_reason': reason,
      'status': 'scheduled',
      'updated_at': DateTime.now().toIso8601String(),
    };

    final result = await DatabaseService.update(
      table: _tableName,
      data: data,
      filters: {'id': sessionId},
    );

    return SessionModel.fromJson(result);
  }

  static Future<SessionModel> rateSession(String sessionId, double rating, String? feedback) async {
    final data = {
      'rating': rating,
      'feedback': feedback,
      'updated_at': DateTime.now().toIso8601String(),
    };

    final result = await DatabaseService.update(
      table: _tableName,
      data: data,
      filters: {'id': sessionId},
    );

    return SessionModel.fromJson(result);
  }

  // Session analytics
  static Future<Map<String, dynamic>> getSessionStats(String trainerId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final filters = {'trainer_id': trainerId};
    
    final sessions = await getSessions(
      trainerId: trainerId,
      startDate: startDate,
      endDate: endDate,
    );

    final totalSessions = sessions.length;
    final completedSessions = sessions.where((s) => s.isCompleted).length;
    final cancelledSessions = sessions.where((s) => s.isCancelled).length;
    final noShowSessions = sessions.where((s) => s.isNoShow).length;
    
    final totalRevenue = sessions
        .where((s) => s.isCompleted)
        .fold(0.0, (sum, session) => sum + (session.sessionData?['price'] ?? 0.0));

    final averageRating = sessions
        .where((s) => s.rating != null)
        .fold(0.0, (sum, session) => sum + session.rating!) / 
        sessions.where((s) => s.rating != null).length;

    return {
      'total_sessions': totalSessions,
      'completed_sessions': completedSessions,
      'cancelled_sessions': cancelledSessions,
      'no_show_sessions': noShowSessions,
      'completion_rate': totalSessions > 0 ? (completedSessions / totalSessions) * 100 : 0,
      'cancellation_rate': totalSessions > 0 ? (cancelledSessions / totalSessions) * 100 : 0,
      'total_revenue': totalRevenue,
      'average_rating': averageRating.isNaN ? 0.0 : averageRating,
    };
  }

  static Future<List<SessionModel>> getUpcomingSessions(String trainerId, {int days = 7}) async {
    final startDate = DateTime.now();
    final endDate = startDate.add(Duration(days: days));

    return getSessions(
      trainerId: trainerId,
      status: 'scheduled',
      startDate: startDate,
      endDate: endDate,
    );
  }

  static Future<List<SessionModel>> getTodaySessions(String trainerId) async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    return getSessions(
      trainerId: trainerId,
      startDate: startOfDay,
      endDate: endOfDay,
    );
  }

  // Session packages
  static Future<List<SessionPackageModel>> getSessionPackages(String trainerId) async {
    final result = await DatabaseService.select(
      table: _packagesTableName,
      filters: {'trainer_id': trainerId, 'is_active': true},
      orderBy: 'created_at',
      ascending: false,
    );

    return result.map((json) => SessionPackageModel.fromJson(json)).toList();
  }

  static Future<SessionPackageModel> createSessionPackage(SessionPackageModel package) async {
    final data = package.toJson();
    data.remove('id');
    data['created_at'] = DateTime.now().toIso8601String();
    data['updated_at'] = DateTime.now().toIso8601String();

    final result = await DatabaseService.insert(
      table: _packagesTableName,
      data: data,
    );

    return SessionPackageModel.fromJson(result);
  }

  static Future<SessionPackageModel> updateSessionPackage(SessionPackageModel package) async {
    final data = package.toJson();
    data['updated_at'] = DateTime.now().toIso8601String();

    final result = await DatabaseService.update(
      table: _packagesTableName,
      data: data,
      filters: {'id': package.id},
    );

    return SessionPackageModel.fromJson(result);
  }

  static Future<void> deleteSessionPackage(String packageId) async {
    await DatabaseService.update(
      table: _packagesTableName,
      data: {'is_active': false, 'updated_at': DateTime.now().toIso8601String()},
      filters: {'id': packageId},
    );
  }

  // Recurring sessions
  static Future<List<SessionModel>> createRecurringSessions({
    required String trainerId,
    required String traineeId,
    required DateTime startDate,
    required String recurringPattern,
    required int occurrences,
    required int durationMinutes,
    String? sessionType,
    String? location,
    String? notes,
  }) async {
    final sessions = <SessionModel>[];
    var currentDate = startDate;

    for (int i = 0; i < occurrences; i++) {
      final session = SessionModel(
        id: '', // Will be generated by database
        trainerId: trainerId,
        traineeId: traineeId,
        scheduledAt: currentDate,
        durationMinutes: durationMinutes,
        status: 'scheduled',
        sessionType: sessionType,
        location: location,
        notes: notes,
        isRecurring: true,
        recurringPattern: recurringPattern,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final createdSession = await createSession(session);
      sessions.add(createdSession);

      // Calculate next occurrence based on pattern
      switch (recurringPattern) {
        case 'daily':
          currentDate = currentDate.add(const Duration(days: 1));
          break;
        case 'weekly':
          currentDate = currentDate.add(const Duration(days: 7));
          break;
        case 'biweekly':
          currentDate = currentDate.add(const Duration(days: 14));
          break;
        case 'monthly':
          currentDate = DateTime(
            currentDate.year,
            currentDate.month + 1,
            currentDate.day,
            currentDate.hour,
            currentDate.minute,
          );
          break;
      }
    }

    return sessions;
  }

  // Session availability
  static Future<bool> isTimeSlotAvailable(
    String trainerId,
    DateTime startTime,
    int durationMinutes, {
    String? excludeSessionId,
  }) async {
    final endTime = startTime.add(Duration(minutes: durationMinutes));
    
    final sessions = await getSessions(
      trainerId: trainerId,
      status: 'scheduled',
      startDate: startTime.subtract(const Duration(hours: 1)),
      endDate: endTime.add(const Duration(hours: 1)),
    );

    for (final session in sessions) {
      if (excludeSessionId != null && session.id == excludeSessionId) continue;
      
      final sessionEnd = session.scheduledAt.add(Duration(minutes: session.durationMinutes));
      
      // Check for overlap
      if (startTime.isBefore(sessionEnd) && endTime.isAfter(session.scheduledAt)) {
        return false;
      }
    }

    return true;
  }
}
