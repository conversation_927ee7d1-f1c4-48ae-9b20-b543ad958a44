import 'package:supabase_flutter/supabase_flutter.dart';
import '../main.dart';
import 'dart:typed_data';

class DatabaseService {
  static final SupabaseClient _client = supabase;

  // Generic CRUD operations
  static Future<List<Map<String, dynamic>>> select({
    required String table,
    String? select,
    Map<String, dynamic>? filters,
    String? orderBy,
    bool ascending = true,
    int? limit,
    int? offset,
  }) async {
    try {
      var query = _client.from(table).select(select ?? '*');

      // Apply filters
      if (filters != null) {
        filters.forEach((key, value) {
          if (value is List) {
            query = query.inFilter(key, value);
          } else if (value is String && value.contains('%')) {
            query = query.like(key, value);
          } else {
            query = query.eq(key, value);
          }
        });
      }

      // Apply ordering
      if (orderBy != null) {
        query = query.order(orderBy, ascending: ascending);
      }

      // Apply pagination (chain directly, don't reassign)
      if (limit != null && offset != null) {
        query = query.range(offset, offset + limit - 1);
      } else if (limit != null) {
        query = query.limit(limit);
      }

      final response = await query;
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw DatabaseException('Error selecting from $table: $e');
    }
  }

  static Future<Map<String, dynamic>?> selectSingle({
    required String table,
    String? select,
    required Map<String, dynamic> filters,
  }) async {
    try {
      var query = _client.from(table).select(select ?? '*');

      filters.forEach((key, value) {
        query = query.eq(key, value);
      });

      final response = await query.maybeSingle();
      return response;
    } catch (e) {
      throw DatabaseException('Error selecting single from $table: $e');
    }
  }

  static Future<Map<String, dynamic>> insert({
    required String table,
    required Map<String, dynamic> data,
    String? select,
  }) async {
    try {
      final response = await _client
          .from(table)
          .insert(data)
          .select(select ?? '*')
          .single();
      return response;
    } catch (e) {
      throw DatabaseException('Error inserting into $table: $e');
    }
  }

  static Future<List<Map<String, dynamic>>> insertMultiple({
    required String table,
    required List<Map<String, dynamic>> data,
    String? select,
  }) async {
    try {
      final response = await _client
          .from(table)
          .insert(data)
          .select(select ?? '*');
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw DatabaseException('Error inserting multiple into $table: $e');
    }
  }

  static Future<Map<String, dynamic>> update({
    required String table,
    required Map<String, dynamic> data,
    required Map<String, dynamic> filters,
    String? select,
  }) async {
    try {
      var query = _client.from(table).update(data);

      filters.forEach((key, value) {
        query = query.eq(key, value);
      });

      final response = await query.select(select ?? '*').single();
      return response;
    } catch (e) {
      throw DatabaseException('Error updating $table: $e');
    }
  }

  static Future<List<Map<String, dynamic>>> updateMultiple({
    required String table,
    required Map<String, dynamic> data,
    required Map<String, dynamic> filters,
    String? select,
  }) async {
    try {
      var query = _client.from(table).update(data);

      filters.forEach((key, value) {
        query = query.eq(key, value);
      });

      final response = await query.select(select ?? '*');
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw DatabaseException('Error updating multiple in $table: $e');
    }
  }

  static Future<void> delete({
    required String table,
    required Map<String, dynamic> filters,
  }) async {
    try {
      var query = _client.from(table).delete();

      filters.forEach((key, value) {
        query = query.eq(key, value);
      });

      await query;
    } catch (e) {
      throw DatabaseException('Error deleting from $table: $e');
    }
  }

  // Advanced query methods
  static Future<int> count({
    required String table,
    Map<String, dynamic>? filters,
  }) async {
    try {
      var query = _client.from(table).select('*', head: true);
      if (filters != null) {
        filters.forEach((key, value) {
          query = query.eq(key, value);
        });
      }
      final response = await query.execute();
      return response.count ?? 0;
    } catch (e) {
      throw DatabaseException('Error counting $table: $e');
    }
  }

  static Future<List<Map<String, dynamic>>> search({
    required String table,
    required String column,
    required String searchTerm,
    String? select,
    int? limit,
  }) async {
    try {
      var query = _client.from(table).select(select ?? '*');
      query = query.textSearch(column, searchTerm);

      if (limit != null) {
        query = query.limit(limit);
      }

      final response = await query;
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw DatabaseException('Error searching $table: $e');
    }
  }

  // Aggregation methods
  static Future<double> sum({
    required String table,
    required String column,
    Map<String, dynamic>? filters,
  }) async {
    try {
      var query = _client.from(table).select('$column.sum()');

      if (filters != null) {
        filters.forEach((key, value) {
          query = query.eq(key, value);
        });
      }

      final response = await query.single();
      return (response['sum'] as num?)?.toDouble() ?? 0.0;
    } catch (e) {
      throw DatabaseException('Error summing $column in $table: $e');
    }
  }

  static Future<double> average({
    required String table,
    required String column,
    Map<String, dynamic>? filters,
  }) async {
    try {
      var query = _client.from(table).select('$column.avg()');

      if (filters != null) {
        filters.forEach((key, value) {
          query = query.eq(key, value);
        });
      }

      final response = await query.single();
      return (response['avg'] as num?)?.toDouble() ?? 0.0;
    } catch (e) {
      throw DatabaseException('Error averaging $column in $table: $e');
    }
  }

  // Real-time subscriptions
  static RealtimeChannel subscribeToTable({
    required String table,
    required Function(PostgresChangePayload) onInsert,
    required Function(PostgresChangePayload) onUpdate,
    required Function(PostgresChangePayload) onDelete,
    Map<String, dynamic>? filters,
  }) {
    var channel = _client.channel('public:$table');

    if (filters != null) {
      final filterString = filters.entries
          .map((e) => '${e.key}=eq.${e.value}')
          .join(',');
      channel = _client.channel('public:$table:$filterString');
    }

    return channel
        .onPostgresChanges(
          event: PostgresChangeEvent.insert,
          schema: 'public',
          table: table,
          callback: onInsert,
        )
        .onPostgresChanges(
          event: PostgresChangeEvent.update,
          schema: 'public',
          table: table,
          callback: onUpdate,
        )
        .onPostgresChanges(
          event: PostgresChangeEvent.delete,
          schema: 'public',
          table: table,
          callback: onDelete,
        )
        .subscribe();
  }

  // Transaction support
  static Future<T> transaction<T>(Future<T> Function() operation) async {
    try {
      return await operation();
    } catch (e) {
      throw DatabaseException('Transaction failed: $e');
    }
  }

  // Batch operations
  static Future<void> batchInsert({
    required String table,
    required List<Map<String, dynamic>> data,
    int batchSize = 100,
  }) async {
    try {
      for (int i = 0; i < data.length; i += batchSize) {
        final batch = data.skip(i).take(batchSize).toList();
        await _client.from(table).insert(batch);
      }
    } catch (e) {
      throw DatabaseException('Error batch inserting into $table: $e');
    }
  }

  // File storage operations
  static Future<String> uploadFile({
    required String bucket,
    required String path,
    required List<int> fileBytes,
    Map<String, String>? metadata,
  }) async {
    try {
      // Convert List<int> to Uint8List for uploadBinary
      final bytes = Uint8List.fromList(fileBytes);
      await _client.storage.from(bucket).uploadBinary(
        path,
        bytes,
        fileOptions: FileOptions(
          metadata: metadata,
        ),
      );

      return _client.storage.from(bucket).getPublicUrl(path);
    } catch (e) {
      throw DatabaseException('Error uploading file: $e');
    }
  }

  static Future<void> deleteFile({
    required String bucket,
    required String path,
  }) async {
    try {
      await _client.storage.from(bucket).remove([path]);
    } catch (e) {
      throw DatabaseException('Error deleting file: $e');
    }
  }

  static String getFileUrl({
    required String bucket,
    required String path,
  }) {
    return _client.storage.from(bucket).getPublicUrl(path);
  }
}

class DatabaseException implements Exception {
  final String message;
  DatabaseException(this.message);

  @override
  String toString() => 'DatabaseException: $message';
}
