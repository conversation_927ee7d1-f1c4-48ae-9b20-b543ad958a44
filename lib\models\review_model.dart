class ReviewModel {
  final String id;
  final String traineeId;
  final String trainerId;
  final String? sessionId;
  final String? courseId;
  final int rating;
  final String? title;
  final String? comment;
  final List<String> pros;
  final List<String> cons;
  final bool isAnonymous;
  final bool isVerified;
  final bool isFeatured;
  final int helpfulCount;
  final int reportedCount;
  final String? trainerResponse;
  final DateTime? trainerRespondedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  ReviewModel({
    required this.id,
    required this.traineeId,
    required this.trainerId,
    this.sessionId,
    this.courseId,
    required this.rating,
    this.title,
    this.comment,
    this.pros = const [],
    this.cons = const [],
    this.isAnonymous = false,
    this.isVerified = false,
    this.isFeatured = false,
    this.helpfulCount = 0,
    this.reportedCount = 0,
    this.trainerResponse,
    this.trainerRespondedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ReviewModel.fromJson(Map<String, dynamic> json) {
    return ReviewModel(
      id: json['id'] as String,
      traineeId: json['trainee_id'] as String,
      trainerId: json['trainer_id'] as String,
      sessionId: json['session_id'] as String?,
      courseId: json['course_id'] as String?,
      rating: json['rating'] as int,
      title: json['title'] as String?,
      comment: json['comment'] as String?,
      pros: (json['pros'] as List<dynamic>?)?.map((e) => e.toString()).toList() ?? [],
      cons: (json['cons'] as List<dynamic>?)?.map((e) => e.toString()).toList() ?? [],
      isAnonymous: json['is_anonymous'] as bool? ?? false,
      isVerified: json['is_verified'] as bool? ?? false,
      isFeatured: json['is_featured'] as bool? ?? false,
      helpfulCount: json['helpful_count'] as int? ?? 0,
      reportedCount: json['reported_count'] as int? ?? 0,
      trainerResponse: json['trainer_response'] as String?,
      trainerRespondedAt: json['trainer_responded_at'] != null 
          ? DateTime.parse(json['trainer_responded_at'] as String) 
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trainee_id': traineeId,
      'trainer_id': trainerId,
      'session_id': sessionId,
      'course_id': courseId,
      'rating': rating,
      'title': title,
      'comment': comment,
      'pros': pros,
      'cons': cons,
      'is_anonymous': isAnonymous,
      'is_verified': isVerified,
      'is_featured': isFeatured,
      'helpful_count': helpfulCount,
      'reported_count': reportedCount,
      'trainer_response': trainerResponse,
      'trainer_responded_at': trainerRespondedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  bool get hasTrainerResponse => trainerResponse != null && trainerResponse!.isNotEmpty;
  bool get isPositive => rating >= 4;
  bool get isNegative => rating <= 2;
  bool get isNeutral => rating == 3;

  String get ratingDisplayText {
    switch (rating) {
      case 1:
        return 'سيء جداً';
      case 2:
        return 'سيء';
      case 3:
        return 'متوسط';
      case 4:
        return 'جيد';
      case 5:
        return 'ممتاز';
      default:
        return '$rating نجوم';
    }
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}

class NotificationModel {
  final String id;
  final String userId;
  final String title;
  final String message;
  final String type;
  final String priority;
  final Map<String, dynamic> data;
  final String? actionUrl;
  final String? actionText;
  final bool isRead;
  final DateTime? readAt;
  final DateTime? expiresAt;
  final List<String> sentVia;
  final Map<String, dynamic> deliveryStatus;
  final DateTime createdAt;

  NotificationModel({
    required this.id,
    required this.userId,
    required this.title,
    required this.message,
    this.type = 'general',
    this.priority = 'normal',
    this.data = const {},
    this.actionUrl,
    this.actionText,
    this.isRead = false,
    this.readAt,
    this.expiresAt,
    this.sentVia = const ['app'],
    this.deliveryStatus = const {},
    required this.createdAt,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      type: json['type'] as String? ?? 'general',
      priority: json['priority'] as String? ?? 'normal',
      data: json['data'] as Map<String, dynamic>? ?? {},
      actionUrl: json['action_url'] as String?,
      actionText: json['action_text'] as String?,
      isRead: json['is_read'] as bool? ?? false,
      readAt: json['read_at'] != null 
          ? DateTime.parse(json['read_at'] as String) 
          : null,
      expiresAt: json['expires_at'] != null 
          ? DateTime.parse(json['expires_at'] as String) 
          : null,
      sentVia: (json['sent_via'] as List<dynamic>?)?.map((e) => e.toString()).toList() ?? ['app'],
      deliveryStatus: json['delivery_status'] as Map<String, dynamic>? ?? {},
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'title': title,
      'message': message,
      'type': type,
      'priority': priority,
      'data': data,
      'action_url': actionUrl,
      'action_text': actionText,
      'is_read': isRead,
      'read_at': readAt?.toIso8601String(),
      'expires_at': expiresAt?.toIso8601String(),
      'sent_via': sentVia,
      'delivery_status': deliveryStatus,
      'created_at': createdAt.toIso8601String(),
    };
  }

  bool get isExpired => expiresAt != null && expiresAt!.isBefore(DateTime.now());
  bool get hasAction => actionUrl != null && actionUrl!.isNotEmpty;

  String get typeDisplayName {
    switch (type) {
      case 'session':
        return 'جلسة';
      case 'payment':
        return 'دفع';
      case 'subscription':
        return 'اشتراك';
      case 'course':
        return 'دورة';
      case 'review':
        return 'مراجعة';
      case 'system':
        return 'نظام';
      case 'marketing':
        return 'تسويق';
      case 'general':
        return 'عام';
      default:
        return type;
    }
  }

  String get priorityDisplayName {
    switch (priority) {
      case 'low':
        return 'منخفض';
      case 'normal':
        return 'عادي';
      case 'high':
        return 'عالي';
      case 'urgent':
        return 'عاجل';
      default:
        return priority;
    }
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}

class ChatMessageModel {
  final String id;
  final String conversationId;
  final String senderId;
  final String receiverId;
  final String message;
  final String messageType;
  final String? fileUrl;
  final String? fileName;
  final int? fileSizeBytes;
  final String? thumbnailUrl;
  final bool isRead;
  final DateTime? readAt;
  final bool isEdited;
  final DateTime? editedAt;
  final String? replyToId;
  final bool isDeleted;
  final DateTime? deletedAt;
  final DateTime createdAt;

  ChatMessageModel({
    required this.id,
    required this.conversationId,
    required this.senderId,
    required this.receiverId,
    required this.message,
    this.messageType = 'text',
    this.fileUrl,
    this.fileName,
    this.fileSizeBytes,
    this.thumbnailUrl,
    this.isRead = false,
    this.readAt,
    this.isEdited = false,
    this.editedAt,
    this.replyToId,
    this.isDeleted = false,
    this.deletedAt,
    required this.createdAt,
  });

  factory ChatMessageModel.fromJson(Map<String, dynamic> json) {
    return ChatMessageModel(
      id: json['id'] as String,
      conversationId: json['conversation_id'] as String,
      senderId: json['sender_id'] as String,
      receiverId: json['receiver_id'] as String,
      message: json['message'] as String,
      messageType: json['message_type'] as String? ?? 'text',
      fileUrl: json['file_url'] as String?,
      fileName: json['file_name'] as String?,
      fileSizeBytes: json['file_size_bytes'] as int?,
      thumbnailUrl: json['thumbnail_url'] as String?,
      isRead: json['is_read'] as bool? ?? false,
      readAt: json['read_at'] != null 
          ? DateTime.parse(json['read_at'] as String) 
          : null,
      isEdited: json['is_edited'] as bool? ?? false,
      editedAt: json['edited_at'] != null 
          ? DateTime.parse(json['edited_at'] as String) 
          : null,
      replyToId: json['reply_to_id'] as String?,
      isDeleted: json['is_deleted'] as bool? ?? false,
      deletedAt: json['deleted_at'] != null 
          ? DateTime.parse(json['deleted_at'] as String) 
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'conversation_id': conversationId,
      'sender_id': senderId,
      'receiver_id': receiverId,
      'message': message,
      'message_type': messageType,
      'file_url': fileUrl,
      'file_name': fileName,
      'file_size_bytes': fileSizeBytes,
      'thumbnail_url': thumbnailUrl,
      'is_read': isRead,
      'read_at': readAt?.toIso8601String(),
      'is_edited': isEdited,
      'edited_at': editedAt?.toIso8601String(),
      'reply_to_id': replyToId,
      'is_deleted': isDeleted,
      'deleted_at': deletedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  bool get isTextMessage => messageType == 'text';
  bool get isImageMessage => messageType == 'image';
  bool get isVideoMessage => messageType == 'video';
  bool get isAudioMessage => messageType == 'audio';
  bool get isFileMessage => messageType == 'file';
  bool get isLocationMessage => messageType == 'location';
  bool get hasFile => fileUrl != null && fileUrl!.isNotEmpty;
  bool get isReply => replyToId != null;

  String get messageTypeDisplayName {
    switch (messageType) {
      case 'text':
        return 'نص';
      case 'image':
        return 'صورة';
      case 'video':
        return 'فيديو';
      case 'audio':
        return 'صوت';
      case 'file':
        return 'ملف';
      case 'location':
        return 'موقع';
      default:
        return messageType;
    }
  }

  String get formattedFileSize {
    if (fileSizeBytes == null) return '';
    
    final bytes = fileSizeBytes!;
    if (bytes < 1024) {
      return '$bytes بايت';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} كيلوبايت';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
    }
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}
