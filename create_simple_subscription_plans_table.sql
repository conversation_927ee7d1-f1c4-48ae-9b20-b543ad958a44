-- Create subscription_plans table without sort_order column
CREATE TABLE IF NOT EXISTS public.subscription_plans (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL CHECK (name IN ('Basic', 'Standard', 'Premium', 'VIP', 'Custom')),
    description TEXT,
    duration_days INTEGER NOT NULL CHECK (duration_days > 0),
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    features TEXT[] DEFAULT '{}',
    session_count INTEGER DEFAULT 0, -- 0 means unlimited
    nutrition_plan_included BOOLEAN DEFAULT false,
    workout_plan_included BOOLEAN DEFAULT true,
    chat_support BOOLEAN DEFAULT true,
    video_calls BOOLEAN DEFAULT false,
    progress_tracking BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(trainer_id, name)
);

-- <PERSON>reate trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_subscription_plans_updated_at ON public.subscription_plans;
CREATE TRIGGER update_subscription_plans_updated_at
    BEFORE UPDATE ON public.subscription_plans
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS (Row Level Security)
ALTER TABLE public.subscription_plans ENABLE ROW LEVEL SECURITY;

-- Create policy for trainers to access their own plans
CREATE POLICY "Trainers can view their own subscription plans" ON public.subscription_plans
    FOR SELECT USING (
        trainer_id IN (
            SELECT id FROM public.trainers WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Trainers can insert their own subscription plans" ON public.subscription_plans
    FOR INSERT WITH CHECK (
        trainer_id IN (
            SELECT id FROM public.trainers WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Trainers can update their own subscription plans" ON public.subscription_plans
    FOR UPDATE USING (
        trainer_id IN (
            SELECT id FROM public.trainers WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Trainers can delete their own subscription plans" ON public.subscription_plans
    FOR DELETE USING (
        trainer_id IN (
            SELECT id FROM public.trainers WHERE user_id = auth.uid()
        )
    );
