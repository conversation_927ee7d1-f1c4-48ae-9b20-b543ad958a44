import 'package:flutter/material.dart';
import '../../main.dart';
import '../../theme/app_theme.dart';
import '../../services/localization_service.dart';
import '../../widgets/widgets.dart';

class TraineeDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> trainee;

  const TraineeDetailsScreen({
    super.key,
    required this.trainee,
  });

  @override
  State<TraineeDetailsScreen> createState() => _TraineeDetailsScreenState();
}

class _TraineeDetailsScreenState extends State<TraineeDetailsScreen>
    with TickerProviderStateMixin {
  bool _isLoading = true;
  Map<String, dynamic>? _traineeDetails;
  List<Map<String, dynamic>> _sessions = [];
  List<Map<String, dynamic>> _workoutPlans = [];
  List<Map<String, dynamic>> _nutritionPlans = [];
  Map<String, dynamic>? _subscription;

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadTraineeDetails();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadTraineeDetails() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final traineeId = widget.trainee['trainee_id'];

      // جلب تفاصيل المتدرب
      final traineeData = await supabase
          .from('users')
          .select('*')
          .eq('id', traineeId)
          .maybeSingle();

      // جلب الجلسات
      final sessions = await supabase
          .from('sessions')
          .select('*')
          .eq('trainee_id', traineeId)
          .order('scheduled_at', ascending: false);

      // جلب خطط التمرين
      final workoutPlans = await supabase
          .from('workout_plans')
          .select('*')
          .eq('trainee_id', traineeId)
          .order('created_at', ascending: false);

      // جلب خطط التغذية
      final nutritionPlans = await supabase
          .from('nutrition_plans')
          .select('*')
          .eq('trainee_id', traineeId)
          .order('created_at', ascending: false);

      // جلب الاشتراك الحالي (أحدث اشتراك نشط)
      final subscriptionList = await supabase
          .from('subscriptions')
          .select('*, subscription_plans(*)')
          .eq('trainee_id', traineeId)
          .eq('status', 'active')
          .order('created_at', ascending: false)
          .limit(1);

      final subscription =
          subscriptionList.isNotEmpty ? subscriptionList.first : null;

      setState(() {
        _traineeDetails = traineeData;
        _sessions = List<Map<String, dynamic>>.from(sessions);
        _workoutPlans = List<Map<String, dynamic>>.from(workoutPlans);
        _nutritionPlans = List<Map<String, dynamic>>.from(nutritionPlans);
        _subscription = subscription;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              LocalizationService.isArabic
                  ? 'خطأ في تحميل البيانات: $e'
                  : 'Error loading data: $e',
            ),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: LoadingOverlay(
        isLoading: _isLoading,
        message:
            LocalizationService.isArabic ? 'جاري التحميل...' : 'Loading...',
        child: CustomScrollView(
          slivers: [
            _buildAppBar(),
            SliverToBoxAdapter(
              child: Column(
                children: [
                  _buildTraineeHeader(),
                  _buildTabBar(),
                ],
              ),
            ),
            SliverFillRemaining(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildOverviewTab(),
                  _buildSessionsTab(),
                  _buildWorkoutPlansTab(),
                  _buildNutritionPlansTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      backgroundColor: AppTheme.darkBackground,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: const BoxDecoration(
            gradient: AppTheme.goldGradient,
          ),
          child: SafeArea(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 40),
                CircleAvatar(
                  radius: 50,
                  backgroundColor: Colors.white,
                  backgroundImage: _traineeDetails?['avatar_url'] != null &&
                          _traineeDetails!['avatar_url'] != ''
                      ? NetworkImage(_traineeDetails!['avatar_url'])
                      : null,
                  child: (_traineeDetails?['avatar_url'] == null ||
                          _traineeDetails!['avatar_url'] == '')
                      ? const Icon(Icons.person,
                          size: 50, color: AppTheme.primaryGold)
                      : null,
                ),
                const SizedBox(height: 16),
                Text(
                  _traineeDetails?['full_name'] ??
                      (LocalizationService.isArabic ? 'متدرب' : 'Trainee'),
                  style: AppTheme.headlineMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTraineeHeader() {
    if (_traineeDetails == null) return const SizedBox.shrink();

    return ProfessionalContainer(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          InfoCard(
            title: LocalizationService.isArabic ? 'البريد الإلكتروني' : 'Email',
            content: _traineeDetails!['email'] ?? '',
            icon: Icons.email,
          ),
          const SizedBox(height: 8),
          InfoCard(
            title: LocalizationService.isArabic ? 'رقم الهاتف' : 'Phone',
            content: _traineeDetails!['phone'] ??
                (LocalizationService.isArabic ? 'غير محدد' : 'Not specified'),
            icon: Icons.phone,
          ),
          const SizedBox(height: 8),
          InfoCard(
            title:
                LocalizationService.isArabic ? 'حالة الحساب' : 'Account Status',
            content: _traineeDetails!['is_active'] == true
                ? (LocalizationService.isArabic ? 'نشط' : 'Active')
                : (LocalizationService.isArabic ? 'غير نشط' : 'Inactive'),
            icon: Icons.account_circle,
            iconColor: _traineeDetails!['is_active'] == true
                ? AppTheme.successGreen
                : AppTheme.errorRed,
          ),
          if (_traineeDetails!['age'] != null) ...[
            const SizedBox(height: 8),
            InfoCard(
              title: LocalizationService.isArabic ? 'العمر' : 'Age',
              content:
                  '${_traineeDetails!['age']} ${LocalizationService.isArabic ? 'سنة' : 'years'}',
              icon: Icons.cake,
            ),
          ],
          if (_traineeDetails!['gender'] != null) ...[
            const SizedBox(height: 8),
            InfoCard(
              title: LocalizationService.isArabic ? 'الجنس' : 'Gender',
              content: _traineeDetails!['gender'] == 'male'
                  ? (LocalizationService.isArabic ? 'ذكر' : 'Male')
                  : (LocalizationService.isArabic ? 'أنثى' : 'Female'),
              icon: Icons.person,
            ),
          ],
          if (_traineeDetails!['fitness_goal'] != null) ...[
            const SizedBox(height: 8),
            InfoCard(
              title: LocalizationService.isArabic ? 'الهدف' : 'Fitness Goal',
              content: _getFitnessGoalText(_traineeDetails!['fitness_goal']),
              icon: Icons.flag,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppTheme.cardShadow,
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          gradient: AppTheme.goldGradient,
          borderRadius: BorderRadius.circular(8),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: AppTheme.textSecondary,
        labelStyle: AppTheme.bodySmall.copyWith(fontWeight: FontWeight.bold),
        unselectedLabelStyle: AppTheme.bodySmall,
        tabs: [
          Tab(text: LocalizationService.isArabic ? 'نظرة عامة' : 'Overview'),
          Tab(text: LocalizationService.isArabic ? 'الجلسات' : 'Sessions'),
          Tab(text: LocalizationService.isArabic ? 'التمارين' : 'Workouts'),
          Tab(text: LocalizationService.isArabic ? 'التغذية' : 'Nutrition'),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          if (_subscription != null) _buildSubscriptionCard(),
          const SizedBox(height: 16),
          _buildPhysicalInfoCard(),
          const SizedBox(height: 16),
          _buildStatsCards(),
        ],
      ),
    );
  }

  Widget _buildSubscriptionCard() {
    final plan = _subscription!['subscription_plans'];
    return ProfessionalContainer(
      gradient: AppTheme.goldGradient,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.card_membership, color: Colors.white),
              const SizedBox(width: 8),
              Text(
                LocalizationService.isArabic
                    ? 'الاشتراك الحالي'
                    : 'Current Subscription',
                style: AppTheme.headlineSmall.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            plan['name'] ?? '',
            style: AppTheme.bodyLarge.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${plan['price']} ${LocalizationService.isArabic ? 'ريال/شهر' : 'SAR/month'}',
            style: AppTheme.bodyMedium.copyWith(color: Colors.white),
          ),
          const SizedBox(height: 8),
          Text(
            '${LocalizationService.isArabic ? 'من' : 'From'}: ${_subscription!['start_date']} ${LocalizationService.isArabic ? 'إلى' : 'to'}: ${_subscription!['end_date']}',
            style: AppTheme.bodySmall.copyWith(color: Colors.white70),
          ),
        ],
      ),
    );
  }

  Widget _buildPhysicalInfoCard() {
    if (_traineeDetails == null) return const SizedBox.shrink();

    return ProfessionalContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.fitness_center, color: AppTheme.primaryGold),
              const SizedBox(width: 8),
              Text(
                LocalizationService.isArabic
                    ? 'المعلومات الجسدية'
                    : 'Physical Information',
                style: AppTheme.headlineSmall.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              if (_traineeDetails!['weight'] != null) ...[
                Expanded(
                  child: StatCard(
                    title: LocalizationService.isArabic
                        ? 'الوزن الحالي'
                        : 'Current Weight',
                    value:
                        '${_traineeDetails!['weight']} ${LocalizationService.isArabic ? 'كجم' : 'kg'}',
                    icon: Icons.monitor_weight,
                    color: AppTheme.infoBlue,
                  ),
                ),
                const SizedBox(width: 16),
              ],
              if (_traineeDetails!['height'] != null) ...[
                Expanded(
                  child: StatCard(
                    title: LocalizationService.isArabic ? 'الطول' : 'Height',
                    value:
                        '${_traineeDetails!['height']} ${LocalizationService.isArabic ? 'سم' : 'cm'}',
                    icon: Icons.height,
                    color: AppTheme.successGreen,
                  ),
                ),
              ],
            ],
          ),
          if (_traineeDetails!['target_weight'] != null) ...[
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: StatCard(
                    title: LocalizationService.isArabic
                        ? 'الوزن المستهدف'
                        : 'Target Weight',
                    value:
                        '${_traineeDetails!['target_weight']} ${LocalizationService.isArabic ? 'كجم' : 'kg'}',
                    icon: Icons.flag,
                    color: AppTheme.warningOrange,
                  ),
                ),
                const SizedBox(width: 16),
                if (_traineeDetails!['activity_level'] != null) ...[
                  Expanded(
                    child: StatCard(
                      title: LocalizationService.isArabic
                          ? 'مستوى النشاط'
                          : 'Activity Level',
                      value: '${_traineeDetails!['activity_level']}/5',
                      icon: Icons.trending_up,
                      color: AppTheme.primaryGold,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatsCards() {
    return Row(
      children: [
        Expanded(
          child: StatCard(
            title: LocalizationService.isArabic
                ? 'إجمالي الجلسات'
                : 'Total Sessions',
            value: _sessions.length.toString(),
            icon: Icons.fitness_center,
            color: AppTheme.infoBlue,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: StatCard(
            title:
                LocalizationService.isArabic ? 'خطط التمرين' : 'Workout Plans',
            value: _workoutPlans.length.toString(),
            icon: Icons.assignment,
            color: AppTheme.successGreen,
          ),
        ),
      ],
    );
  }

  Widget _buildSessionsTab() {
    if (_sessions.isEmpty) {
      return EmptyState(
        icon: Icons.event_busy,
        title: LocalizationService.isArabic ? 'لا توجد جلسات' : 'No Sessions',
        message: LocalizationService.isArabic
            ? 'لم يتم إنشاء أي جلسات لهذا المتدرب بعد'
            : 'No sessions have been created for this trainee yet',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _sessions.length,
      itemBuilder: (context, index) {
        final session = _sessions[index];
        return ProfessionalContainer(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getSessionStatusColor(session['status']),
              child: Icon(
                _getSessionStatusIcon(session['status']),
                color: Colors.white,
              ),
            ),
            title: Text(
              '${LocalizationService.isArabic ? 'جلسة' : 'Session'} #${session['id']}',
              style: AppTheme.bodyLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${LocalizationService.isArabic ? 'التاريخ' : 'Date'}: ${session['scheduled_at']}',
                  style: AppTheme.bodySmall,
                ),
                Text(
                  '${LocalizationService.isArabic ? 'الحالة' : 'Status'}: ${session['status']}',
                  style: AppTheme.bodySmall.copyWith(
                    color: _getSessionStatusColor(session['status']),
                  ),
                ),
              ],
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              // Navigate to session details
            },
          ),
        );
      },
    );
  }

  Widget _buildWorkoutPlansTab() {
    if (_workoutPlans.isEmpty) {
      return EmptyState(
        icon: Icons.fitness_center,
        title: LocalizationService.isArabic
            ? 'لا توجد خطط تمرين'
            : 'No Workout Plans',
        message: LocalizationService.isArabic
            ? 'لم يتم إنشاء أي خطط تمرين لهذا المتدرب بعد'
            : 'No workout plans have been created for this trainee yet',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _workoutPlans.length,
      itemBuilder: (context, index) {
        final plan = _workoutPlans[index];
        return ProfessionalContainer(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: const CircleAvatar(
              backgroundColor: AppTheme.infoBlue,
              child: Icon(Icons.fitness_center, color: Colors.white),
            ),
            title: Text(
              plan['name'] ??
                  '${LocalizationService.isArabic ? 'خطة تمرين' : 'Workout Plan'} #${plan['id']}',
              style: AppTheme.bodyLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: Text(
              plan['description'] ?? '',
              style: AppTheme.bodySmall,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              // Navigate to workout plan details
            },
          ),
        );
      },
    );
  }

  Widget _buildNutritionPlansTab() {
    if (_nutritionPlans.isEmpty) {
      return EmptyState(
        icon: Icons.restaurant,
        title: LocalizationService.isArabic
            ? 'لا توجد خطط تغذية'
            : 'No Nutrition Plans',
        message: LocalizationService.isArabic
            ? 'لم يتم إنشاء أي خطط تغذية لهذا المتدرب بعد'
            : 'No nutrition plans have been created for this trainee yet',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _nutritionPlans.length,
      itemBuilder: (context, index) {
        final plan = _nutritionPlans[index];
        return ProfessionalContainer(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: const CircleAvatar(
              backgroundColor: AppTheme.successGreen,
              child: Icon(Icons.restaurant, color: Colors.white),
            ),
            title: Text(
              plan['name'] ??
                  '${LocalizationService.isArabic ? 'خطة تغذية' : 'Nutrition Plan'} #${plan['id']}',
              style: AppTheme.bodyLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: Text(
              plan['description'] ?? '',
              style: AppTheme.bodySmall,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              // Navigate to nutrition plan details
            },
          ),
        );
      },
    );
  }

  Color _getSessionStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return AppTheme.successGreen;
      case 'scheduled':
        return AppTheme.infoBlue;
      case 'cancelled':
        return AppTheme.errorRed;
      default:
        return AppTheme.textSecondary;
    }
  }

  String _getFitnessGoalText(String goal) {
    switch (goal.toLowerCase()) {
      case 'weight_loss':
        return LocalizationService.isArabic ? 'فقدان الوزن' : 'Weight Loss';
      case 'muscle_gain':
        return LocalizationService.isArabic ? 'بناء العضلات' : 'Muscle Gain';
      case 'endurance':
        return LocalizationService.isArabic ? 'تحسين التحمل' : 'Endurance';
      case 'strength':
        return LocalizationService.isArabic ? 'زيادة القوة' : 'Strength';
      case 'flexibility':
        return LocalizationService.isArabic ? 'تحسين المرونة' : 'Flexibility';
      default:
        return goal;
    }
  }

  IconData _getSessionStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Icons.check_circle;
      case 'scheduled':
        return Icons.schedule;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.help;
    }
  }
}
