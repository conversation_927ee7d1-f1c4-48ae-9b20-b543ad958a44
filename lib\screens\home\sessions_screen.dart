import 'package:flutter/material.dart';
import '../../services/localization_service.dart';
import '../../main.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/premium_widgets.dart';
import '../../theme/app_theme.dart';
import '../session/session_details_screen.dart';

class SessionsScreen extends StatefulWidget {
  const SessionsScreen({super.key});

  @override
  State<SessionsScreen> createState() => _SessionsScreenState();
}

class _SessionsScreenState extends State<SessionsScreen> {
  List<Map<String, dynamic>> _sessions = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSessions();
  }

  Future<void> _loadSessions() async {
    try {
      final user = supabase.auth.currentUser;
      if (user == null) return;
      final trainerList = await supabase
          .from('trainers')
          .select('id')
          .eq('user_id', user.id)
          .limit(1);

      final trainer = trainerList.isNotEmpty ? trainerList.first : null;
      if (trainer == null) return;
      final sessions = await supabase
          .from('sessions')
          .select('*')
          .eq('trainer_id', trainer['id']);
      // جلب بيانات المتدربين المرتبطين بكل جلسة
      final traineeIds = sessions
          .map((s) => s['trainee_id'] as String?)
          .where((id) => id != null)
          .toSet()
          .toList();
      List<Map<String, dynamic>> usersData = [];
      if (traineeIds.isNotEmpty) {
        usersData = await supabase
            .from('users')
            .select('id, full_name, avatar_url')
            .inFilter('id', traineeIds);
      }
      final detailedSessions = sessions.map((s) {
        final userData = usersData.firstWhere(
          (u) => u['id'] == s['trainee_id'],
          orElse: () => {'full_name': '', 'avatar_url': ''},
        );
        return {
          ...s,
          'trainee_name': userData['full_name'],
          'trainee_avatar': userData['avatar_url'],
        };
      }).toList();
      setState(() {
        _sessions = List<Map<String, dynamic>>.from(detailedSessions);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _formatDateTime(String? dateTimeStr) {
    if (dateTimeStr == null) return '';
    try {
      final dateTime = DateTime.parse(dateTimeStr);
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final sessionDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

      if (sessionDate == today) {
        return LocalizationService.isArabic
            ? 'اليوم ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}'
            : 'Today ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      } else if (sessionDate == today.add(const Duration(days: 1))) {
        return LocalizationService.isArabic
            ? 'غداً ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}'
            : 'Tomorrow ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      } else {
        return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      }
    } catch (e) {
      return dateTimeStr;
    }
  }

  String _getSessionTypeArabic(String? type) {
    switch (type) {
      case 'training':
        return 'تدريب';
      case 'consultation':
        return 'استشارة';
      case 'assessment':
        return 'تقييم';
      case 'nutrition':
        return 'تغذية';
      default:
        return type ?? 'تدريب';
    }
  }

  String _getSessionTypeEnglish(String? type) {
    switch (type) {
      case 'training':
        return 'Training';
      case 'consultation':
        return 'Consultation';
      case 'assessment':
        return 'Assessment';
      case 'nutrition':
        return 'Nutrition';
      default:
        return type ?? 'Training';
    }
  }

  String _getStatusArabic(String? status) {
    switch (status) {
      case 'scheduled':
        return 'مجدولة';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      case 'reschedule_requested':
        return 'طلب إعادة جدولة';
      case 'no_show':
        return 'غياب';
      default:
        return status ?? '';
    }
  }

  String _getStatusEnglish(String? status) {
    switch (status) {
      case 'scheduled':
        return 'Scheduled';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      case 'reschedule_requested':
        return 'Reschedule Requested';
      case 'no_show':
        return 'No Show';
      default:
        return status ?? '';
    }
  }

  Color _getStatusColor(String? status) {
    switch (status) {
      case 'scheduled':
        return AppTheme.primaryGold;
      case 'completed':
        return AppTheme.successGreen;
      case 'cancelled':
        return AppTheme.errorRed;
      case 'reschedule_requested':
        return AppTheme.warningOrange;
      case 'no_show':
        return AppTheme.textSecondary;
      default:
        return AppTheme.textTertiary;
    }
  }

  IconData _getStatusIcon(String? status) {
    switch (status) {
      case 'scheduled':
        return Icons.schedule;
      case 'completed':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      case 'reschedule_requested':
        return Icons.update;
      case 'no_show':
        return Icons.person_off;
      default:
        return Icons.help;
    }
  }

  IconData _getSessionTypeIcon(String? type) {
    switch (type) {
      case 'training':
        return Icons.fitness_center;
      case 'consultation':
        return Icons.chat;
      case 'assessment':
        return Icons.assessment;
      case 'nutrition':
        return Icons.restaurant;
      default:
        return Icons.fitness_center;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      appBar: CustomAppBar(
        title: LocalizationService.isArabic ? 'الجلسات' : 'Sessions',
        actions: [
          Container(
            margin: const EdgeInsets.only(right: AppTheme.spacingM),
            child: PremiumActionButton(
              text: '',
              icon: Icons.add,
              isPrimary: false,
              width: 48,
              height: 48,
              onPressed: () {
                // Navigate to add session screen
              },
            ),
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.darkGradient,
        ),
        child: _isLoading
            ? const Center(
                child: CircularProgressIndicator(color: AppTheme.primaryGold))
            : _sessions.isEmpty
                ? _buildEmptyState()
                : _buildSessionsList(),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: PremiumGlassCard(
        width: double.infinity,
        margin: const EdgeInsets.all(AppTheme.spacingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingL),
              decoration: BoxDecoration(
                color: AppTheme.primaryGold.withValues(alpha: 0.2),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.event_note,
                size: 64,
                color: AppTheme.primaryGold,
              ),
            ),
            const SizedBox(height: AppTheme.spacingL),
            Text(
              LocalizationService.isArabic
                  ? 'لا توجد جلسات'
                  : 'No sessions found',
              style: AppTheme.headlineSmall.copyWith(
                color: AppTheme.textPrimary,
              ),
            ),
            const SizedBox(height: AppTheme.spacingS),
            Text(
              LocalizationService.isArabic
                  ? 'ابدأ بجدولة جلسات جديدة'
                  : 'Start by scheduling new sessions',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
            const SizedBox(height: AppTheme.spacingL),
            PremiumActionButton(
              text: LocalizationService.isArabic
                  ? 'جدولة جلسة'
                  : 'Schedule Session',
              icon: Icons.add_circle,
              onPressed: () {
                // Navigate to add session screen
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSessionsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      itemCount: _sessions.length,
      itemBuilder: (context, index) {
        final session = _sessions[index];
        return _buildSessionCard(session, index);
      },
    );
  }

  Widget _buildSessionCard(Map<String, dynamic> session, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      child: PremiumGlassCard(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => SessionDetailsScreen(
                session: session,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with avatar and status
              Row(
                children: [
                  PremiumAvatar(
                    imageUrl: session['trainee_avatar'],
                    name: session['trainee_name'],
                    radius: 24,
                    showStatus: false,
                  ),
                  const SizedBox(width: AppTheme.spacingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          session['trainee_name'] != ''
                              ? session['trainee_name']
                              : (LocalizationService.isArabic
                                  ? 'جلسة رقم: ${session['id']}'
                                  : 'Session ID: ${session['id']}'),
                          style: AppTheme.bodyLarge.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textPrimary,
                          ),
                        ),
                        const SizedBox(height: AppTheme.spacingXS),
                        Row(
                          children: [
                            Icon(
                              _getSessionTypeIcon(session['session_type']),
                              size: 16,
                              color: AppTheme.primaryGold,
                            ),
                            const SizedBox(width: AppTheme.spacingXS),
                            Text(
                              LocalizationService.isArabic
                                  ? _getSessionTypeArabic(
                                      session['session_type'])
                                  : _getSessionTypeEnglish(
                                      session['session_type']),
                              style: AppTheme.bodySmall.copyWith(
                                color: AppTheme.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  PremiumStatusBadge(
                    text: LocalizationService.isArabic
                        ? _getStatusArabic(session['status'])
                        : _getStatusEnglish(session['status']),
                    color: _getStatusColor(session['status']),
                    icon: _getStatusIcon(session['status']),
                  ),
                ],
              ),

              const SizedBox(height: AppTheme.spacingM),

              // Session details
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingM),
                decoration: BoxDecoration(
                  color: AppTheme.glassColor,
                  borderRadius: AppTheme.mediumRadius,
                  border: Border.all(
                    color: AppTheme.primaryGold.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    _buildSessionDetailRow(
                      Icons.schedule,
                      LocalizationService.isArabic ? 'التاريخ' : 'Date',
                      _formatDateTime(session['scheduled_at']),
                    ),
                    const SizedBox(height: AppTheme.spacingS),
                    _buildSessionDetailRow(
                      Icons.timer,
                      LocalizationService.isArabic ? 'المدة' : 'Duration',
                      LocalizationService.isArabic
                          ? '${session['duration_minutes'] ?? 60} دقيقة'
                          : '${session['duration_minutes'] ?? 60} minutes',
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSessionDetailRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: AppTheme.primaryGold,
        ),
        const SizedBox(width: AppTheme.spacingS),
        Text(
          '$label: ',
          style: AppTheme.bodySmall.copyWith(
            color: AppTheme.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.textPrimary,
            ),
          ),
        ),
      ],
    );
  }
}
