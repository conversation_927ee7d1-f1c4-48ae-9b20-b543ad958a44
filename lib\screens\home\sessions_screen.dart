import 'package:flutter/material.dart';
import '../../services/localization_service.dart';
import '../../main.dart';
import '../../widgets/custom_app_bar.dart';
import '../session/session_details_screen.dart';

class SessionsScreen extends StatefulWidget {
  const SessionsScreen({super.key});

  @override
  State<SessionsScreen> createState() => _SessionsScreenState();
}

class _SessionsScreenState extends State<SessionsScreen> {
  List<Map<String, dynamic>> _sessions = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSessions();
  }

  Future<void> _loadSessions() async {
    try {
      final user = supabase.auth.currentUser;
      if (user == null) return;
      final trainer = await supabase
          .from('trainers')
          .select('id')
          .eq('user_id', user.id)
          .maybeSingle();
      if (trainer == null) return;
      final sessions = await supabase
          .from('sessions')
          .select('*')
          .eq('trainer_id', trainer['id']);
      // جلب بيانات المتدربين المرتبطين بكل جلسة
      final traineeIds = sessions
          .map((s) => s['trainee_id'] as String?)
          .where((id) => id != null)
          .toSet()
          .toList();
      List<Map<String, dynamic>> usersData = [];
      if (traineeIds.isNotEmpty) {
        usersData = await supabase
            .from('users')
            .select('id, full_name, avatar_url')
            .inFilter('id', traineeIds);
      }
      final detailedSessions = sessions.map((s) {
        final userData = usersData.firstWhere(
          (u) => u['id'] == s['trainee_id'],
          orElse: () => {'full_name': '', 'avatar_url': ''},
        );
        return {
          ...s,
          'trainee_name': userData['full_name'],
          'trainee_avatar': userData['avatar_url'],
        };
      }).toList();
      setState(() {
        _sessions = List<Map<String, dynamic>>.from(detailedSessions);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: LocalizationService.isArabic ? 'الجلسات' : 'Sessions',
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Color(0xFFFFD700)))
          : _sessions.isEmpty
              ? Center(
                  child: Text(
                    LocalizationService.isArabic
                        ? 'لا توجد جلسات'
                        : 'No sessions found',
                    style: const TextStyle(fontSize: 20, color: Colors.grey),
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _sessions.length,
                  itemBuilder: (context, index) {
                    final session = _sessions[index];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF232526), Color(0xFF414345)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(18),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.12),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: const Color(0xFF43E97B),
                          backgroundImage: session['trainee_avatar'] != null &&
                                  session['trainee_avatar'] != ''
                              ? NetworkImage(session['trainee_avatar'])
                              : null,
                          child: (session['trainee_avatar'] == null ||
                                  session['trainee_avatar'] == '')
                              ? const Icon(Icons.person, color: Colors.white)
                              : null,
                        ),
                        title: Text(
                          session['trainee_name'] != ''
                              ? session['trainee_name']
                              : (LocalizationService.isArabic
                                  ? 'جلسة رقم: ${session['id']}'
                                  : 'Session ID: ${session['id']}'),
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              LocalizationService.isArabic
                                  ? 'التاريخ: ${session['date'] ?? ''}'
                                  : 'Date: ${session['date'] ?? ''}',
                              style: const TextStyle(
                                  color: Colors.white70, fontSize: 13),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              LocalizationService.isArabic
                                  ? 'الحالة: ${session['status']}'
                                  : 'Status: ${session['status']}',
                              style: const TextStyle(color: Colors.white70),
                            ),
                          ],
                        ),
                        trailing: const Icon(Icons.arrow_forward_ios,
                            color: Colors.white54, size: 18),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 12),
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => SessionDetailsScreen(
                                session: session,
                              ),
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
    );
  }
}
