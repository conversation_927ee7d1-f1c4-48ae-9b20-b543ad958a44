import 'package:flutter/material.dart';
import '../../services/localization_service.dart';
import '../../main.dart';
import '../../widgets/custom_app_bar.dart';
import '../session/session_details_screen.dart';

class SessionsScreen extends StatefulWidget {
  const SessionsScreen({super.key});

  @override
  State<SessionsScreen> createState() => _SessionsScreenState();
}

class _SessionsScreenState extends State<SessionsScreen> {
  List<Map<String, dynamic>> _sessions = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSessions();
  }

  Future<void> _loadSessions() async {
    try {
      final user = supabase.auth.currentUser;
      if (user == null) return;
      final trainerList = await supabase
          .from('trainers')
          .select('id')
          .eq('user_id', user.id)
          .limit(1);

      final trainer = trainerList.isNotEmpty ? trainerList.first : null;
      if (trainer == null) return;
      final sessions = await supabase
          .from('sessions')
          .select('*')
          .eq('trainer_id', trainer['id']);
      // جلب بيانات المتدربين المرتبطين بكل جلسة
      final traineeIds = sessions
          .map((s) => s['trainee_id'] as String?)
          .where((id) => id != null)
          .toSet()
          .toList();
      List<Map<String, dynamic>> usersData = [];
      if (traineeIds.isNotEmpty) {
        usersData = await supabase
            .from('users')
            .select('id, full_name, avatar_url')
            .inFilter('id', traineeIds);
      }
      final detailedSessions = sessions.map((s) {
        final userData = usersData.firstWhere(
          (u) => u['id'] == s['trainee_id'],
          orElse: () => {'full_name': '', 'avatar_url': ''},
        );
        return {
          ...s,
          'trainee_name': userData['full_name'],
          'trainee_avatar': userData['avatar_url'],
        };
      }).toList();
      setState(() {
        _sessions = List<Map<String, dynamic>>.from(detailedSessions);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _formatDateTime(String? dateTimeStr) {
    if (dateTimeStr == null) return '';
    try {
      final dateTime = DateTime.parse(dateTimeStr);
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final sessionDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

      if (sessionDate == today) {
        return LocalizationService.isArabic
            ? 'اليوم ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}'
            : 'Today ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      } else if (sessionDate == today.add(const Duration(days: 1))) {
        return LocalizationService.isArabic
            ? 'غداً ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}'
            : 'Tomorrow ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      } else {
        return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      }
    } catch (e) {
      return dateTimeStr;
    }
  }

  String _getSessionTypeArabic(String? type) {
    switch (type) {
      case 'training':
        return 'تدريب';
      case 'consultation':
        return 'استشارة';
      case 'assessment':
        return 'تقييم';
      case 'nutrition':
        return 'تغذية';
      default:
        return type ?? 'تدريب';
    }
  }

  String _getSessionTypeEnglish(String? type) {
    switch (type) {
      case 'training':
        return 'Training';
      case 'consultation':
        return 'Consultation';
      case 'assessment':
        return 'Assessment';
      case 'nutrition':
        return 'Nutrition';
      default:
        return type ?? 'Training';
    }
  }

  String _getStatusArabic(String? status) {
    switch (status) {
      case 'scheduled':
        return 'مجدولة';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      case 'reschedule_requested':
        return 'طلب إعادة جدولة';
      case 'no_show':
        return 'غياب';
      default:
        return status ?? '';
    }
  }

  String _getStatusEnglish(String? status) {
    switch (status) {
      case 'scheduled':
        return 'Scheduled';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      case 'reschedule_requested':
        return 'Reschedule Requested';
      case 'no_show':
        return 'No Show';
      default:
        return status ?? '';
    }
  }

  Color _getStatusColor(String? status) {
    switch (status) {
      case 'scheduled':
        return const Color(0xFFFFD700); // ذهبي
      case 'completed':
        return const Color(0xFF4CAF50); // أخضر
      case 'cancelled':
        return const Color(0xFFF44336); // أحمر
      case 'reschedule_requested':
        return const Color(0xFFFF9800); // برتقالي
      case 'no_show':
        return const Color(0xFF9E9E9E); // رمادي
      default:
        return Colors.white70;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: LocalizationService.isArabic ? 'الجلسات' : 'Sessions',
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Color(0xFFFFD700)))
          : _sessions.isEmpty
              ? Center(
                  child: Text(
                    LocalizationService.isArabic
                        ? 'لا توجد جلسات'
                        : 'No sessions found',
                    style: const TextStyle(fontSize: 20, color: Colors.grey),
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _sessions.length,
                  itemBuilder: (context, index) {
                    final session = _sessions[index];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF232526), Color(0xFF414345)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(18),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.12),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: const Color(0xFF43E97B),
                          backgroundImage: session['trainee_avatar'] != null &&
                                  session['trainee_avatar'] != ''
                              ? NetworkImage(session['trainee_avatar'])
                              : null,
                          child: (session['trainee_avatar'] == null ||
                                  session['trainee_avatar'] == '')
                              ? const Icon(Icons.person, color: Colors.white)
                              : null,
                        ),
                        title: Text(
                          session['trainee_name'] != ''
                              ? session['trainee_name']
                              : (LocalizationService.isArabic
                                  ? 'جلسة رقم: ${session['id']}'
                                  : 'Session ID: ${session['id']}'),
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              LocalizationService.isArabic
                                  ? 'التاريخ: ${_formatDateTime(session['scheduled_at'])}'
                                  : 'Date: ${_formatDateTime(session['scheduled_at'])}',
                              style: const TextStyle(
                                  color: Colors.white70, fontSize: 13),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              LocalizationService.isArabic
                                  ? 'المدة: ${session['duration_minutes'] ?? 60} دقيقة'
                                  : 'Duration: ${session['duration_minutes'] ?? 60} minutes',
                              style: const TextStyle(
                                  color: Colors.white70, fontSize: 13),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              LocalizationService.isArabic
                                  ? 'النوع: ${_getSessionTypeArabic(session['session_type'])}'
                                  : 'Type: ${_getSessionTypeEnglish(session['session_type'])}',
                              style: const TextStyle(
                                  color: Colors.white70, fontSize: 13),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              LocalizationService.isArabic
                                  ? 'الحالة: ${_getStatusArabic(session['status'])}'
                                  : 'Status: ${_getStatusEnglish(session['status'])}',
                              style: TextStyle(
                                color: _getStatusColor(session['status']),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        trailing: const Icon(Icons.arrow_forward_ios,
                            color: Colors.white54, size: 18),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 12),
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => SessionDetailsScreen(
                                session: session,
                              ),
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
    );
  }
}
