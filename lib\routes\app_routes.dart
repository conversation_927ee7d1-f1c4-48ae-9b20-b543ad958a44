import 'package:flutter/material.dart';
import '../screens/splash_screen.dart';
import '../screens/auth/login_screen.dart';
import '../screens/auth/register_screen.dart';
import '../screens/profile/trainer_profile_setup_screen.dart';
import '../screens/trainer_dashboard.dart';
import '../screens/sessions_management_screen.dart';
import '../screens/clients_management_screen.dart';
import '../screens/subscriptions/subscription_plans_screen.dart';

class AppRoutes {
  static const String splash = '/';
  static const String login = '/login';
  static const String register = '/register';
  static const String profileSetup = '/profile-setup';
  static const String dashboard = '/dashboard';
  static const String sessions = '/sessions';
  static const String clients = '/clients';
  static const String subscriptions = '/subscriptions';
  static const String reports = '/reports';
  static const String settings = '/settings';

  static Map<String, WidgetBuilder> getRoutes(
      Function(String) onLanguageChanged) {
    return {
      splash: (context) => SplashScreen(onLanguageChanged: onLanguageChanged),
      login: (context) => LoginScreen(onLanguageChanged: onLanguageChanged),
      register: (context) =>
          RegisterScreen(onLanguageChanged: onLanguageChanged),
      profileSetup: (context) =>
          TrainerProfileSetupScreen(onLanguageChanged: onLanguageChanged),
      dashboard: (context) => const TrainerDashboard(),
      sessions: (context) => const SessionsManagementScreen(),
      clients: (context) => const ClientsManagementScreen(),
      subscriptions: (context) => const SubscriptionPlansScreen(),
      // Add more routes as needed
    };
  }

  static Route<dynamic>? onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case splash:
      case login:
      case register:
      case profileSetup:
      case dashboard:
      case sessions:
      case clients:
      case subscriptions:
        return null; // Use named routes
      default:
        return MaterialPageRoute(
          builder: (context) => const NotFoundScreen(),
        );
    }
  }
}

class NotFoundScreen extends StatelessWidget {
  const NotFoundScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('صفحة غير موجودة'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'الصفحة المطلوبة غير موجودة',
              style: TextStyle(fontSize: 18),
            ),
          ],
        ),
      ),
    );
  }
}
