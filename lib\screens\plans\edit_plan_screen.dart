import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../../theme/app_theme.dart';
import '../../services/localization_service.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/premium_widgets.dart';

class EditPlanScreen extends StatefulWidget {
  final Map<String, dynamic> plan;
  final String planType; // 'workout' or 'nutrition'

  const EditPlanScreen({
    super.key,
    required this.plan,
    required this.planType,
  });

  @override
  State<EditPlanScreen> createState() => _EditPlanScreenState();
}

class _EditPlanScreenState extends State<EditPlanScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _detailsController = TextEditingController();

  List<File> _selectedImages = [];
  List<File> _selectedVideos = [];
  List<Map<String, dynamic>> _existingMedia = [];
  bool _isLoading = false;
  bool _isLoadingMedia = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _initializeForm();
    _loadExistingMedia();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    _detailsController.dispose();
    super.dispose();
  }

  void _initializeForm() {
    _titleController.text = widget.plan['title'] ?? '';
    _descriptionController.text = widget.plan['description'] ?? '';
    _detailsController.text = widget.plan['details'] ?? '';
  }

  Future<void> _loadExistingMedia() async {
    try {
      final mediaResponse = await Supabase.instance.client
          .from('media_files')
          .select('*')
          .eq('plan_id', widget.plan['id'])
          .eq('plan_type', widget.planType);

      setState(() {
        _existingMedia = List<Map<String, dynamic>>.from(mediaResponse);
        _isLoadingMedia = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingMedia = false;
      });
    }
  }

  Future<void> _pickImages() async {
    final picker = ImagePicker();
    final pickedFiles = await picker.pickMultiImage();
    
    if (pickedFiles.isNotEmpty) {
      setState(() {
        _selectedImages.addAll(pickedFiles.map((xFile) => File(xFile.path)));
      });
    }
  }

  Future<void> _pickVideos() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickVideo(source: ImageSource.gallery);
    
    if (pickedFile != null) {
      setState(() {
        _selectedVideos.add(File(pickedFile.path));
      });
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  void _removeVideo(int index) {
    setState(() {
      _selectedVideos.removeAt(index);
    });
  }

  Future<void> _removeExistingMedia(Map<String, dynamic> media) async {
    try {
      await Supabase.instance.client
          .from('media_files')
          .delete()
          .eq('id', media['id']);

      setState(() {
        _existingMedia.remove(media);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              LocalizationService.isArabic 
                  ? 'تم حذف الملف'
                  : 'File deleted',
            ),
            backgroundColor: AppTheme.successGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              LocalizationService.isArabic 
                  ? 'فشل في حذف الملف'
                  : 'Failed to delete file',
            ),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isNutrition = widget.planType == 'nutrition';
    final planColor = isNutrition ? AppTheme.infoBlue : AppTheme.warningOrange;
    final planIcon = isNutrition ? Icons.restaurant : Icons.fitness_center;

    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      appBar: CustomAppBar(
        title: LocalizationService.isArabic ? 'تعديل الخطة' : 'Edit Plan',
        centerTitle: true,
        showNotifications: true,
        notificationCount: 0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.darkGradient,
        ),
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppTheme.spacingM),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildPlanHeader(planColor, planIcon),
                    const SizedBox(height: AppTheme.spacingL),
                    _buildFormFields(),
                    const SizedBox(height: AppTheme.spacingL),
                    _buildExistingMediaSection(),
                    const SizedBox(height: AppTheme.spacingL),
                    _buildMediaUploadSection(),
                    const SizedBox(height: AppTheme.spacingXL),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlanHeader(Color planColor, IconData planIcon) {
    final isNutrition = widget.planType == 'nutrition';
    
    return PremiumGlassCard(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    planColor.withValues(alpha: 0.8),
                    planColor.withValues(alpha: 0.6),
                  ],
                ),
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: planColor.withValues(alpha: 0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Icon(
                planIcon,
                size: 30,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: AppTheme.spacingM),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    LocalizationService.isArabic ? 'تعديل الخطة' : 'Edit Plan',
                    style: AppTheme.headlineSmall.copyWith(
                      color: AppTheme.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingXS),
                  Text(
                    LocalizationService.isArabic
                        ? (isNutrition ? 'خطة تغذية' : 'خطة تمارين')
                        : (isNutrition ? 'Nutrition Plan' : 'Workout Plan'),
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormFields() {
    return PremiumGlassCard(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              LocalizationService.isArabic ? 'معلومات الخطة' : 'Plan Information',
              style: AppTheme.headlineSmall.copyWith(
                color: AppTheme.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppTheme.spacingL),
            
            // Title field
            TextFormField(
              controller: _titleController,
              style: AppTheme.bodyMedium.copyWith(color: AppTheme.textPrimary),
              decoration: InputDecoration(
                labelText: LocalizationService.isArabic ? 'عنوان الخطة' : 'Plan Title',
                prefixIcon: const Icon(Icons.title, color: AppTheme.primaryGold),
                border: OutlineInputBorder(
                  borderRadius: AppTheme.mediumRadius,
                  borderSide: BorderSide(color: AppTheme.primaryGold.withValues(alpha: 0.3)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: AppTheme.mediumRadius,
                  borderSide: BorderSide(color: AppTheme.primaryGold.withValues(alpha: 0.3)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: AppTheme.mediumRadius,
                  borderSide: const BorderSide(color: AppTheme.primaryGold),
                ),
                labelStyle: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return LocalizationService.isArabic 
                      ? 'يرجى إدخال عنوان الخطة'
                      : 'Please enter plan title';
                }
                return null;
              },
            ),
            const SizedBox(height: AppTheme.spacingM),
            
            // Description field
            TextFormField(
              controller: _descriptionController,
              style: AppTheme.bodyMedium.copyWith(color: AppTheme.textPrimary),
              decoration: InputDecoration(
                labelText: LocalizationService.isArabic ? 'وصف الخطة' : 'Plan Description',
                prefixIcon: const Icon(Icons.description, color: AppTheme.primaryGold),
                border: OutlineInputBorder(
                  borderRadius: AppTheme.mediumRadius,
                  borderSide: BorderSide(color: AppTheme.primaryGold.withValues(alpha: 0.3)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: AppTheme.mediumRadius,
                  borderSide: BorderSide(color: AppTheme.primaryGold.withValues(alpha: 0.3)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: AppTheme.mediumRadius,
                  borderSide: const BorderSide(color: AppTheme.primaryGold),
                ),
                labelStyle: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return LocalizationService.isArabic 
                      ? 'يرجى إدخال وصف الخطة'
                      : 'Please enter plan description';
                }
                return null;
              },
            ),
            const SizedBox(height: AppTheme.spacingM),
            
            // Details field
            TextFormField(
              controller: _detailsController,
              style: AppTheme.bodyMedium.copyWith(color: AppTheme.textPrimary),
              decoration: InputDecoration(
                labelText: LocalizationService.isArabic ? 'تفاصيل الخطة' : 'Plan Details',
                prefixIcon: const Icon(Icons.list_alt, color: AppTheme.primaryGold),
                border: OutlineInputBorder(
                  borderRadius: AppTheme.mediumRadius,
                  borderSide: BorderSide(color: AppTheme.primaryGold.withValues(alpha: 0.3)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: AppTheme.mediumRadius,
                  borderSide: BorderSide(color: AppTheme.primaryGold.withValues(alpha: 0.3)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: AppTheme.mediumRadius,
                  borderSide: const BorderSide(color: AppTheme.primaryGold),
                ),
                labelStyle: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
              ),
              maxLines: 4,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExistingMediaSection() {
    if (_isLoadingMedia) {
      return const Center(
        child: CircularProgressIndicator(color: AppTheme.primaryGold),
      );
    }

    if (_existingMedia.isEmpty) {
      return const SizedBox.shrink();
    }

    return PremiumGlassCard(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              LocalizationService.isArabic ? 'الملفات الحالية' : 'Current Files',
              style: AppTheme.headlineSmall.copyWith(
                color: AppTheme.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppTheme.spacingM),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: AppTheme.spacingS,
                mainAxisSpacing: AppTheme.spacingS,
                childAspectRatio: 1,
              ),
              itemCount: _existingMedia.length,
              itemBuilder: (context, index) {
                final media = _existingMedia[index];
                final isImage = media['file_type']?.startsWith('image') ?? false;
                
                return Stack(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: AppTheme.cardBackground,
                        borderRadius: AppTheme.smallRadius,
                        border: Border.all(
                          color: AppTheme.primaryGold.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            isImage ? Icons.image : Icons.video_library,
                            size: 24,
                            color: AppTheme.primaryGold,
                          ),
                          const SizedBox(height: AppTheme.spacingXS),
                          Text(
                            media['file_name'] ?? 'Unknown',
                            style: AppTheme.bodySmall.copyWith(
                              color: AppTheme.textPrimary,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    Positioned(
                      top: 4,
                      right: 4,
                      child: GestureDetector(
                        onTap: () => _removeExistingMedia(media),
                        child: Container(
                          width: 24,
                          height: 24,
                          decoration: const BoxDecoration(
                            color: AppTheme.errorRed,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            size: 16,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaUploadSection() {
    return PremiumGlassCard(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              LocalizationService.isArabic ? 'إضافة ملفات جديدة' : 'Add New Files',
              style: AppTheme.headlineSmall.copyWith(
                color: AppTheme.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppTheme.spacingM),
            
            // Upload buttons
            Row(
              children: [
                Expanded(
                  child: PremiumActionButton(
                    text: LocalizationService.isArabic ? 'إضافة صور' : 'Add Images',
                    icon: Icons.image,
                    isPrimary: false,
                    onPressed: _pickImages,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingS),
                Expanded(
                  child: PremiumActionButton(
                    text: LocalizationService.isArabic ? 'إضافة فيديو' : 'Add Video',
                    icon: Icons.video_library,
                    isPrimary: false,
                    onPressed: _pickVideos,
                  ),
                ),
              ],
            ),
            
            if (_selectedImages.isNotEmpty || _selectedVideos.isNotEmpty) ...[
              const SizedBox(height: AppTheme.spacingM),
              Text(
                LocalizationService.isArabic ? 'الملفات المحددة:' : 'Selected Files:',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                ),
              ),
              const SizedBox(height: AppTheme.spacingS),
              
              // Selected images
              if (_selectedImages.isNotEmpty)
                Wrap(
                  spacing: AppTheme.spacingS,
                  runSpacing: AppTheme.spacingS,
                  children: _selectedImages.asMap().entries.map((entry) {
                    final index = entry.key;
                    return Stack(
                      children: [
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            borderRadius: AppTheme.smallRadius,
                            border: Border.all(
                              color: AppTheme.primaryGold.withValues(alpha: 0.3),
                            ),
                          ),
                          child: const Icon(
                            Icons.image,
                            color: AppTheme.primaryGold,
                          ),
                        ),
                        Positioned(
                          top: 2,
                          right: 2,
                          child: GestureDetector(
                            onTap: () => _removeImage(index),
                            child: Container(
                              width: 20,
                              height: 20,
                              decoration: const BoxDecoration(
                                color: AppTheme.errorRed,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.close,
                                size: 14,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  }).toList(),
                ),
              
              // Selected videos
              if (_selectedVideos.isNotEmpty)
                Wrap(
                  spacing: AppTheme.spacingS,
                  runSpacing: AppTheme.spacingS,
                  children: _selectedVideos.asMap().entries.map((entry) {
                    final index = entry.key;
                    return Stack(
                      children: [
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            borderRadius: AppTheme.smallRadius,
                            border: Border.all(
                              color: AppTheme.primaryGold.withValues(alpha: 0.3),
                            ),
                          ),
                          child: const Icon(
                            Icons.video_library,
                            color: AppTheme.primaryGold,
                          ),
                        ),
                        Positioned(
                          top: 2,
                          right: 2,
                          child: GestureDetector(
                            onTap: () => _removeVideo(index),
                            child: Container(
                              width: 20,
                              height: 20,
                              decoration: const BoxDecoration(
                                color: AppTheme.errorRed,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.close,
                                size: 14,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  }).toList(),
                ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: PremiumActionButton(
            text: LocalizationService.isArabic ? 'إلغاء' : 'Cancel',
            icon: Icons.cancel,
            isPrimary: false,
            onPressed: () => Navigator.pop(context),
          ),
        ),
        const SizedBox(width: AppTheme.spacingM),
        Expanded(
          child: PremiumActionButton(
            text: LocalizationService.isArabic ? 'حفظ التغييرات' : 'Save Changes',
            icon: Icons.save,
            isLoading: _isLoading,
            onPressed: _updatePlan,
          ),
        ),
      ],
    );
  }

  Future<void> _updatePlan() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final tableName = widget.planType == 'workout' ? 'workout_plans' : 'nutrition_plans';
      
      // Update plan data
      final planData = {
        'title': _titleController.text,
        'description': _descriptionController.text,
        'details': _detailsController.text,
        'updated_at': DateTime.now().toIso8601String(),
      };

      await Supabase.instance.client
          .from(tableName)
          .update(planData)
          .eq('id', widget.plan['id']);

      // Upload new media files if any
      await _uploadNewMediaFiles();

      if (mounted) {
        Navigator.pop(context, true); // Return true to indicate update
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              LocalizationService.isArabic 
                  ? 'تم تحديث الخطة بنجاح'
                  : 'Plan updated successfully',
            ),
            backgroundColor: AppTheme.successGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              LocalizationService.isArabic 
                  ? 'فشل في تحديث الخطة'
                  : 'Failed to update plan',
            ),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _uploadNewMediaFiles() async {
    // Upload new images and videos
    for (final image in _selectedImages) {
      await _uploadFile(image, 'image');
    }
    
    for (final video in _selectedVideos) {
      await _uploadFile(video, 'video');
    }
  }

  Future<void> _uploadFile(File file, String type) async {
    try {
      final fileName = '${DateTime.now().millisecondsSinceEpoch}_${file.path.split('/').last}';
      final filePath = 'plans/${widget.planType}/${widget.plan['id']}/$fileName';
      
      await Supabase.instance.client.storage
          .from('media')
          .upload(filePath, file);

      // Insert media record
      await Supabase.instance.client.from('media_files').insert({
        'plan_id': widget.plan['id'],
        'plan_type': widget.planType,
        'file_name': fileName,
        'file_path': filePath,
        'file_type': type == 'image' ? 'image/jpeg' : 'video/mp4',
        'uploaded_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      // Handle upload error
      print('Upload error: $e');
    }
  }
}
