import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../theme/app_theme.dart';
import '../../services/localization_service.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/premium_widgets.dart';

class PlanDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> plan;
  final String planType; // 'workout' or 'nutrition'

  const PlanDetailsScreen({
    super.key,
    required this.plan,
    required this.planType,
  });

  @override
  State<PlanDetailsScreen> createState() => _PlanDetailsScreenState();
}

class _PlanDetailsScreenState extends State<PlanDetailsScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  List<Map<String, dynamic>> _mediaFiles = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _loadPlanDetails();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadPlanDetails() async {
    try {
      final tableName = widget.planType == 'workout' ? 'workout_plans' : 'nutrition_plans';
      final mediaResponse = await Supabase.instance.client
          .from('media_files')
          .select('*')
          .eq('plan_id', widget.plan['id'])
          .eq('plan_type', widget.planType);

      setState(() {
        _mediaFiles = List<Map<String, dynamic>>.from(mediaResponse);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isNutrition = widget.planType == 'nutrition';
    final planColor = isNutrition ? AppTheme.infoBlue : AppTheme.warningOrange;
    final planIcon = isNutrition ? Icons.restaurant : Icons.fitness_center;

    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      appBar: CustomAppBar(
        title: LocalizationService.isArabic ? 'تفاصيل الخطة' : 'Plan Details',
        centerTitle: true,
        showNotifications: true,
        notificationCount: 0,
        actions: [
          Container(
            margin: const EdgeInsets.only(right: AppTheme.spacingS),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(12),
                onTap: () {
                  Navigator.pushNamed(
                    context,
                    '/edit-plan',
                    arguments: {
                      'plan': widget.plan,
                      'planType': widget.planType,
                    },
                  );
                },
                child: Container(
                  width: 44,
                  height: 44,
                  alignment: Alignment.center,
                  child: const Icon(
                    Icons.edit_rounded,
                    color: AppTheme.darkBackground,
                    size: 20,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.darkGradient,
        ),
        child: _isLoading
            ? const Center(
                child: CircularProgressIndicator(color: AppTheme.primaryGold),
              )
            : FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(AppTheme.spacingM),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildPlanHeader(planColor, planIcon),
                        const SizedBox(height: AppTheme.spacingL),
                        _buildPlanInfo(),
                        const SizedBox(height: AppTheme.spacingL),
                        _buildMediaSection(),
                        const SizedBox(height: AppTheme.spacingL),
                        _buildActionButtons(),
                      ],
                    ),
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildPlanHeader(Color planColor, IconData planIcon) {
    final isNutrition = widget.planType == 'nutrition';
    
    return PremiumGlassCard(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Column(
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    planColor.withValues(alpha: 0.8),
                    planColor.withValues(alpha: 0.6),
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: planColor.withValues(alpha: 0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Icon(
                planIcon,
                size: 40,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: AppTheme.spacingM),
            Text(
              widget.plan['title'] ?? 
              (LocalizationService.isArabic
                  ? (isNutrition ? 'خطة تغذية' : 'خطة تمارين')
                  : (isNutrition ? 'Nutrition Plan' : 'Workout Plan')),
              style: AppTheme.headlineMedium.copyWith(
                color: AppTheme.textPrimary,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingS),
            Text(
              LocalizationService.isArabic
                  ? (isNutrition ? 'خطة تغذية متخصصة' : 'خطة تمارين متخصصة')
                  : (isNutrition ? 'Specialized Nutrition Plan' : 'Specialized Workout Plan'),
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlanInfo() {
    return PremiumGlassCard(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppTheme.spacingS),
                  decoration: BoxDecoration(
                    gradient: AppTheme.premiumGoldGradient,
                    borderRadius: AppTheme.smallRadius,
                  ),
                  child: const Icon(
                    Icons.info_outline,
                    color: AppTheme.darkBackground,
                    size: 20,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingM),
                Text(
                  LocalizationService.isArabic ? 'معلومات الخطة' : 'Plan Information',
                  style: AppTheme.headlineSmall.copyWith(
                    color: AppTheme.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingL),
            _buildInfoRow(
              LocalizationService.isArabic ? 'الوصف:' : 'Description:',
              widget.plan['description'] ?? 
              (LocalizationService.isArabic ? 'لا يوجد وصف' : 'No description'),
            ),
            const SizedBox(height: AppTheme.spacingM),
            _buildInfoRow(
              LocalizationService.isArabic ? 'تاريخ البداية:' : 'Start Date:',
              widget.plan['start_date'] ?? 
              (LocalizationService.isArabic ? 'غير محدد' : 'Not specified'),
            ),
            const SizedBox(height: AppTheme.spacingM),
            _buildInfoRow(
              LocalizationService.isArabic ? 'تاريخ الإنشاء:' : 'Created Date:',
              widget.plan['created_at'] != null 
                  ? DateTime.parse(widget.plan['created_at']).toString().split(' ')[0]
                  : (LocalizationService.isArabic ? 'غير محدد' : 'Not specified'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const SizedBox(width: AppTheme.spacingM),
        Expanded(
          child: Text(
            value,
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textPrimary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMediaSection() {
    if (_mediaFiles.isEmpty) {
      return PremiumGlassCard(
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingL),
          child: Column(
            children: [
              Icon(
                Icons.photo_library_outlined,
                size: 48,
                color: AppTheme.textSecondary.withValues(alpha: 0.5),
              ),
              const SizedBox(height: AppTheme.spacingM),
              Text(
                LocalizationService.isArabic 
                    ? 'لا توجد ملفات وسائط'
                    : 'No media files',
                style: AppTheme.bodyLarge.copyWith(
                  color: AppTheme.textSecondary,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return PremiumGlassCard(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppTheme.spacingS),
                  decoration: BoxDecoration(
                    gradient: AppTheme.premiumGoldGradient,
                    borderRadius: AppTheme.smallRadius,
                  ),
                  child: const Icon(
                    Icons.photo_library,
                    color: AppTheme.darkBackground,
                    size: 20,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingM),
                Text(
                  LocalizationService.isArabic ? 'الملفات المرفقة' : 'Attached Files',
                  style: AppTheme.headlineSmall.copyWith(
                    color: AppTheme.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingL),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: AppTheme.spacingM,
                mainAxisSpacing: AppTheme.spacingM,
                childAspectRatio: 1.2,
              ),
              itemCount: _mediaFiles.length,
              itemBuilder: (context, index) {
                final media = _mediaFiles[index];
                final isImage = media['file_type']?.startsWith('image') ?? false;
                
                return Container(
                  decoration: BoxDecoration(
                    color: AppTheme.cardBackground,
                    borderRadius: AppTheme.mediumRadius,
                    border: Border.all(
                      color: AppTheme.primaryGold.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        isImage ? Icons.image : Icons.video_library,
                        size: 32,
                        color: AppTheme.primaryGold,
                      ),
                      const SizedBox(height: AppTheme.spacingS),
                      Text(
                        media['file_name'] ?? 'Unknown',
                        style: AppTheme.bodySmall.copyWith(
                          color: AppTheme.textPrimary,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: PremiumActionButton(
            text: LocalizationService.isArabic ? 'تعديل الخطة' : 'Edit Plan',
            icon: Icons.edit,
            onPressed: () {
              Navigator.pushNamed(
                context,
                '/edit-plan',
                arguments: {
                  'plan': widget.plan,
                  'planType': widget.planType,
                },
              );
            },
          ),
        ),
        const SizedBox(width: AppTheme.spacingM),
        Expanded(
          child: PremiumActionButton(
            text: LocalizationService.isArabic ? 'حذف الخطة' : 'Delete Plan',
            icon: Icons.delete,
            isPrimary: false,
            onPressed: () {
              _showDeleteConfirmation();
            },
          ),
        ),
      ],
    );
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.cardBackground,
        shape: RoundedRectangleBorder(
          borderRadius: AppTheme.largeRadius,
        ),
        title: Text(
          LocalizationService.isArabic ? 'تأكيد الحذف' : 'Confirm Delete',
          style: AppTheme.headlineSmall.copyWith(
            color: AppTheme.textPrimary,
          ),
        ),
        content: Text(
          LocalizationService.isArabic 
              ? 'هل أنت متأكد من حذف هذه الخطة؟'
              : 'Are you sure you want to delete this plan?',
          style: AppTheme.bodyMedium.copyWith(
            color: AppTheme.textSecondary,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              LocalizationService.isArabic ? 'إلغاء' : 'Cancel',
              style: const TextStyle(color: AppTheme.textSecondary),
            ),
          ),
          PremiumActionButton(
            text: LocalizationService.isArabic ? 'حذف' : 'Delete',
            icon: Icons.delete,
            isPrimary: false,
            width: 100,
            height: 40,
            onPressed: () {
              Navigator.pop(context);
              _deletePlan();
            },
          ),
        ],
      ),
    );
  }

  Future<void> _deletePlan() async {
    try {
      final tableName = widget.planType == 'workout' ? 'workout_plans' : 'nutrition_plans';
      
      // Delete media files first
      await Supabase.instance.client
          .from('media_files')
          .delete()
          .eq('plan_id', widget.plan['id'])
          .eq('plan_type', widget.planType);
      
      // Delete the plan
      await Supabase.instance.client
          .from(tableName)
          .delete()
          .eq('id', widget.plan['id']);

      if (mounted) {
        Navigator.pop(context, true); // Return true to indicate deletion
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              LocalizationService.isArabic 
                  ? 'تم حذف الخطة بنجاح'
                  : 'Plan deleted successfully',
            ),
            backgroundColor: AppTheme.successGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              LocalizationService.isArabic 
                  ? 'فشل في حذف الخطة'
                  : 'Failed to delete plan',
            ),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    }
  }
}
